using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة المدفوعات المتقدمة
    /// </summary>
    public class PaymentService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;

        public PaymentService(DatabaseService dbService, NotificationService notificationService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        }

        #region Payment Management

        /// <summary>
        /// إضافة مدفوعة جديدة
        /// </summary>
        public async Task<int> AddPaymentAsync(Payment payment)
        {
            try
            {
                // توليد رقم المدفوعة
                if (string.IsNullOrEmpty(payment.PaymentNumber))
                {
                    payment.PaymentNumber = await GeneratePaymentNumberAsync();
                }

                const string sql = @"
                    INSERT INTO Payments (
                        PaymentNumber, Amount, PaymentType, Status, Direction,
                        CustomerId, SupplierId, InvoiceId, SaleId, Description,
                        Reference, PaymentDate, DueDate, Notes, CreatedBy,
                        CreatedAt, IsRecurring, RecurringType, RecurringInterval,
                        NextPaymentDate, PaidAmount, RemainingAmount
                    ) VALUES (
                        @PaymentNumber, @Amount, @PaymentType, @Status, @Direction,
                        @CustomerId, @SupplierId, @InvoiceId, @SaleId, @Description,
                        @Reference, @PaymentDate, @DueDate, @Notes, @CreatedBy,
                        @CreatedAt, @IsRecurring, @RecurringType, @RecurringInterval,
                        @NextPaymentDate, @PaidAmount, @RemainingAmount
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    payment.PaymentNumber,
                    payment.Amount,
                    PaymentType = payment.PaymentType.ToString(),
                    Status = payment.Status.ToString(),
                    Direction = payment.Direction.ToString(),
                    payment.CustomerId,
                    payment.SupplierId,
                    payment.InvoiceId,
                    payment.SaleId,
                    payment.Description,
                    payment.Reference,
                    PaymentDate = payment.PaymentDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    DueDate = payment.DueDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    payment.Notes,
                    payment.CreatedBy,
                    CreatedAt = payment.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    payment.IsRecurring,
                    RecurringType = payment.RecurringType?.ToString(),
                    payment.RecurringInterval,
                    NextPaymentDate = payment.NextPaymentDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    payment.PaidAmount,
                    payment.RemainingAmount
                };

                var paymentId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                payment.Id = paymentId;

                // إرسال إشعار
                await SendPaymentNotificationAsync(payment, "تم إضافة مدفوعة جديدة");

                LoggingService.LogInfo($"تم إضافة مدفوعة جديدة: {payment.PaymentNumber}");
                return paymentId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة المدفوعة: {payment?.PaymentNumber}");
                throw;
            }
        }

        /// <summary>
        /// تحديث مدفوعة
        /// </summary>
        public async Task<bool> UpdatePaymentAsync(Payment payment)
        {
            try
            {
                payment.UpdatedAt = DateTime.Now;

                const string sql = @"
                    UPDATE Payments SET
                        Amount = @Amount, PaymentType = @PaymentType, Status = @Status,
                        Direction = @Direction, CustomerId = @CustomerId, SupplierId = @SupplierId,
                        InvoiceId = @InvoiceId, SaleId = @SaleId, Description = @Description,
                        Reference = @Reference, PaymentDate = @PaymentDate, DueDate = @DueDate,
                        Notes = @Notes, UpdatedAt = @UpdatedAt, IsRecurring = @IsRecurring,
                        RecurringType = @RecurringType, RecurringInterval = @RecurringInterval,
                        NextPaymentDate = @NextPaymentDate, PaidAmount = @PaidAmount,
                        RemainingAmount = @RemainingAmount
                    WHERE Id = @Id";

                var parameters = new
                {
                    payment.Id,
                    payment.Amount,
                    PaymentType = payment.PaymentType.ToString(),
                    Status = payment.Status.ToString(),
                    Direction = payment.Direction.ToString(),
                    payment.CustomerId,
                    payment.SupplierId,
                    payment.InvoiceId,
                    payment.SaleId,
                    payment.Description,
                    payment.Reference,
                    PaymentDate = payment.PaymentDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    DueDate = payment.DueDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    payment.Notes,
                    UpdatedAt = payment.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss"),
                    payment.IsRecurring,
                    RecurringType = payment.RecurringType?.ToString(),
                    payment.RecurringInterval,
                    NextPaymentDate = payment.NextPaymentDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    payment.PaidAmount,
                    payment.RemainingAmount
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم تحديث المدفوعة: {payment.PaymentNumber}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث المدفوعة: {payment?.Id}");
                throw;
            }
        }

        /// <summary>
        /// حذف مدفوعة
        /// </summary>
        public async Task<bool> DeletePaymentAsync(int paymentId)
        {
            try
            {
                const string sql = "DELETE FROM Payments WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = paymentId });

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم حذف المدفوعة: {paymentId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف المدفوعة: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مدفوعة بالمعرف
        /// </summary>
        public async Task<Payment?> GetPaymentByIdAsync(int paymentId)
        {
            try
            {
                const string sql = "SELECT * FROM Payments WHERE Id = @Id";
                var paymentData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = paymentId });

                if (paymentData != null)
                {
                    return MapToPayment(paymentData);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على المدفوعة: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المدفوعات
        /// </summary>
        public async Task<IEnumerable<Payment>> GetAllPaymentsAsync(int limit = 100, int offset = 0)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM Payments 
                    ORDER BY CreatedAt DESC 
                    LIMIT @Limit OFFSET @Offset";

                var paymentsData = await _dbService.QueryAsync<dynamic>(sql, new { Limit = limit, Offset = offset });
                return paymentsData.Select(MapToPayment);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المدفوعات");
                throw;
            }
        }

        /// <summary>
        /// البحث في المدفوعات
        /// </summary>
        public async Task<IEnumerable<Payment>> SearchPaymentsAsync(string searchTerm, PaymentStatus? status = null, PaymentType? type = null)
        {
            try
            {
                var sql = @"
                    SELECT * FROM Payments 
                    WHERE (PaymentNumber LIKE @SearchTerm OR Description LIKE @SearchTerm OR Reference LIKE @SearchTerm)";

                var parameters = new Dictionary<string, object>
                {
                    ["SearchTerm"] = $"%{searchTerm}%"
                };

                if (status.HasValue)
                {
                    sql += " AND Status = @Status";
                    parameters["Status"] = status.ToString();
                }

                if (type.HasValue)
                {
                    sql += " AND PaymentType = @PaymentType";
                    parameters["PaymentType"] = type.ToString();
                }

                sql += " ORDER BY CreatedAt DESC";

                var paymentsData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return paymentsData.Select(MapToPayment);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في البحث في المدفوعات: {searchTerm}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المدفوعات المستحقة
        /// </summary>
        public async Task<IEnumerable<Payment>> GetOverduePaymentsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM Payments 
                    WHERE DueDate < @CurrentDate 
                    AND Status IN ('Pending', 'PartiallyPaid')
                    ORDER BY DueDate ASC";

                var currentDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var paymentsData = await _dbService.QueryAsync<dynamic>(sql, new { CurrentDate = currentDate });
                return paymentsData.Select(MapToPayment);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المدفوعات المستحقة");
                throw;
            }
        }

        /// <summary>
        /// معالجة دفعة جزئية
        /// </summary>
        public async Task<bool> ProcessPartialPaymentAsync(int paymentId, decimal amount, string notes = "")
        {
            try
            {
                var payment = await GetPaymentByIdAsync(paymentId);
                if (payment == null) return false;

                payment.AddPartialPayment(amount);
                payment.Notes += $"\nدفعة جزئية: {amount:C} - {DateTime.Now:dd/MM/yyyy} - {notes}";

                var success = await UpdatePaymentAsync(payment);
                if (success)
                {
                    await SendPaymentNotificationAsync(payment, $"تم دفع مبلغ جزئي: {amount:C}");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في معالجة الدفعة الجزئية: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// تحديد المدفوعة كمدفوعة بالكامل
        /// </summary>
        public async Task<bool> MarkAsFullyPaidAsync(int paymentId, string notes = "")
        {
            try
            {
                var payment = await GetPaymentByIdAsync(paymentId);
                if (payment == null) return false;

                payment.MarkAsPaid();
                if (!string.IsNullOrEmpty(notes))
                {
                    payment.Notes += $"\n{notes}";
                }

                var success = await UpdatePaymentAsync(payment);
                if (success)
                {
                    await SendPaymentNotificationAsync(payment, "تم دفع المبلغ بالكامل");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديد المدفوعة كمدفوعة: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء مدفوعة
        /// </summary>
        public async Task<bool> CancelPaymentAsync(int paymentId, string reason = "")
        {
            try
            {
                var payment = await GetPaymentByIdAsync(paymentId);
                if (payment == null) return false;

                payment.Cancel();
                if (!string.IsNullOrEmpty(reason))
                {
                    payment.Notes += $"\nسبب الإلغاء: {reason}";
                }

                var success = await UpdatePaymentAsync(payment);
                if (success)
                {
                    await SendPaymentNotificationAsync(payment, "تم إلغاء المدفوعة");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إلغاء المدفوعة: {paymentId}");
                throw;
            }
        }

        /// <summary>
        /// استرداد مدفوعة
        /// </summary>
        public async Task<bool> RefundPaymentAsync(int paymentId, decimal refundAmount = 0, string reason = "")
        {
            try
            {
                var payment = await GetPaymentByIdAsync(paymentId);
                if (payment == null) return false;

                payment.Refund(refundAmount);
                payment.Notes += $"\nاسترداد: {refundAmount:C} - {DateTime.Now:dd/MM/yyyy} - {reason}";

                var success = await UpdatePaymentAsync(payment);
                if (success)
                {
                    await SendPaymentNotificationAsync(payment, $"تم استرداد مبلغ: {refundAmount:C}");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في استرداد المدفوعة: {paymentId}");
                throw;
            }
        }

        #endregion

        #region Statistics and Reports

        /// <summary>
        /// الحصول على إحصائيات المدفوعات
        /// </summary>
        public async Task<PaymentStatistics> GetPaymentStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var from = fromDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
                var to = toDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd");

                const string sql = @"
                    SELECT 
                        COUNT(*) as TotalPayments,
                        SUM(CASE WHEN Status = 'Paid' THEN Amount ELSE 0 END) as TotalPaidAmount,
                        SUM(CASE WHEN Status = 'Pending' THEN Amount ELSE 0 END) as TotalPendingAmount,
                        SUM(CASE WHEN Status = 'Overdue' THEN Amount ELSE 0 END) as TotalOverdueAmount,
                        COUNT(CASE WHEN Status = 'Paid' THEN 1 END) as PaidCount,
                        COUNT(CASE WHEN Status = 'Pending' THEN 1 END) as PendingCount,
                        COUNT(CASE WHEN Status = 'Overdue' THEN 1 END) as OverdueCount,
                        SUM(CASE WHEN Direction = 'Incoming' AND Status = 'Paid' THEN Amount ELSE 0 END) as TotalIncoming,
                        SUM(CASE WHEN Direction = 'Outgoing' AND Status = 'Paid' THEN Amount ELSE 0 END) as TotalOutgoing
                    FROM Payments 
                    WHERE DATE(PaymentDate) BETWEEN @FromDate AND @ToDate";

                var result = await _dbService.QuerySingleAsync<dynamic>(sql, new { FromDate = from, ToDate = to });

                return new PaymentStatistics
                {
                    TotalPayments = result.TotalPayments ?? 0,
                    TotalPaidAmount = result.TotalPaidAmount ?? 0,
                    TotalPendingAmount = result.TotalPendingAmount ?? 0,
                    TotalOverdueAmount = result.TotalOverdueAmount ?? 0,
                    PaidCount = result.PaidCount ?? 0,
                    PendingCount = result.PendingCount ?? 0,
                    OverdueCount = result.OverdueCount ?? 0,
                    TotalIncoming = result.TotalIncoming ?? 0,
                    TotalOutgoing = result.TotalOutgoing ?? 0,
                    NetAmount = (result.TotalIncoming ?? 0) - (result.TotalOutgoing ?? 0)
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على إحصائيات المدفوعات");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المدفوعات حسب النوع
        /// </summary>
        public async Task<Dictionary<PaymentType, decimal>> GetPaymentsByTypeAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var from = fromDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd");
                var to = toDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd");

                const string sql = @"
                    SELECT PaymentType, SUM(Amount) as TotalAmount
                    FROM Payments 
                    WHERE DATE(PaymentDate) BETWEEN @FromDate AND @ToDate
                    AND Status = 'Paid'
                    GROUP BY PaymentType";

                var results = await _dbService.QueryAsync<dynamic>(sql, new { FromDate = from, ToDate = to });
                
                var paymentsByType = new Dictionary<PaymentType, decimal>();
                foreach (var result in results)
                {
                    if (Enum.TryParse<PaymentType>(result.PaymentType, out var paymentType))
                    {
                        paymentsByType[paymentType] = result.TotalAmount ?? 0;
                    }
                }

                return paymentsByType;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المدفوعات حسب النوع");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// توليد رقم مدفوعة جديد
        /// </summary>
        private async Task<string> GeneratePaymentNumberAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM Payments WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);
                
                var today = DateTime.Now;
                return $"PAY-{today:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد رقم المدفوعة");
                return $"PAY-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج المدفوعة
        /// </summary>
        private Payment MapToPayment(dynamic data)
        {
            return new Payment
            {
                Id = data.Id,
                PaymentNumber = data.PaymentNumber ?? "",
                Amount = data.Amount ?? 0,
                PaymentType = Enum.TryParse<PaymentType>(data.PaymentType, out var type) ? type : PaymentType.Cash,
                Status = Enum.TryParse<PaymentStatus>(data.Status, out var status) ? status : PaymentStatus.Pending,
                Direction = Enum.TryParse<PaymentDirection>(data.Direction, out var direction) ? direction : PaymentDirection.Incoming,
                CustomerId = data.CustomerId,
                SupplierId = data.SupplierId,
                InvoiceId = data.InvoiceId,
                SaleId = data.SaleId,
                Description = data.Description ?? "",
                Reference = data.Reference ?? "",
                PaymentDate = DateTime.TryParse(data.PaymentDate, out var paymentDate) ? paymentDate : DateTime.Now,
                DueDate = DateTime.TryParse(data.DueDate, out var dueDate) ? dueDate : null,
                Notes = data.Notes ?? "",
                CreatedBy = data.CreatedBy ?? "",
                CreatedAt = DateTime.TryParse(data.CreatedAt, out var createdAt) ? createdAt : DateTime.Now,
                UpdatedAt = DateTime.TryParse(data.UpdatedAt, out var updatedAt) ? updatedAt : null,
                IsRecurring = data.IsRecurring == 1,
                RecurringType = Enum.TryParse<RecurringType>(data.RecurringType, out var recurringType) ? recurringType : null,
                RecurringInterval = data.RecurringInterval,
                NextPaymentDate = DateTime.TryParse(data.NextPaymentDate, out var nextPaymentDate) ? nextPaymentDate : null,
                PaidAmount = data.PaidAmount ?? 0,
                RemainingAmount = data.RemainingAmount ?? 0
            };
        }

        /// <summary>
        /// إرسال إشعار المدفوعة
        /// </summary>
        private async Task SendPaymentNotificationAsync(Payment payment, string message)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "إشعار مدفوعة",
                    Message = $"{message}: {payment.PaymentNumber} - {payment.FormattedAmount}",
                    Type = "PaymentDue",
                    Priority = payment.Status == PaymentStatus.Overdue ? "High" : "Normal",
                    ActionUrl = $"/payments/{payment.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار المدفوعة");
            }
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات المدفوعات
    /// </summary>
    public class PaymentStatistics
    {
        public int TotalPayments { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public decimal TotalPendingAmount { get; set; }
        public decimal TotalOverdueAmount { get; set; }
        public int PaidCount { get; set; }
        public int PendingCount { get; set; }
        public int OverdueCount { get; set; }
        public decimal TotalIncoming { get; set; }
        public decimal TotalOutgoing { get; set; }
        public decimal NetAmount { get; set; }

        public string FormattedTotalPaidAmount => TotalPaidAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalPendingAmount => TotalPendingAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalOverdueAmount => TotalOverdueAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalIncoming => TotalIncoming.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalOutgoing => TotalOutgoing.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedNetAmount => NetAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
    }
}
