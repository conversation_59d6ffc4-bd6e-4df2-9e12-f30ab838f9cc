using System;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class LoginViewModel : BaseViewModel
    {
        #region Services

        private readonly AuthenticationService _authService;

        #endregion

        #region Properties

        private string _username = string.Empty;
        public string Username
        {
            get => _username;
            set
            {
                if (SetProperty(ref _username, value))
                {
                    LoginCommand.RaiseCanExecuteChanged();
                    ClearError();
                }
            }
        }

        private string _password = string.Empty;
        public string Password
        {
            get => _password;
            set
            {
                if (SetProperty(ref _password, value))
                {
                    LoginCommand.RaiseCanExecuteChanged();
                    ClearError();
                }
            }
        }

        private bool _rememberMe;
        public bool RememberMe
        {
            get => _rememberMe;
            set => SetProperty(ref _rememberMe, value);
        }

        private string _loginButtonText = "تسجيل الدخول";
        public string LoginButtonText
        {
            get => _loginButtonText;
            set => SetProperty(ref _loginButtonText, value);
        }

        public bool CanLogin => !IsLoading && !string.IsNullOrWhiteSpace(Username) && !string.IsNullOrWhiteSpace(Password);

        #endregion

        #region Commands

        private DelegateCommand? _loginCommand;
        public DelegateCommand LoginCommand => _loginCommand ??= new DelegateCommand(Login, () => CanLogin);

        private DelegateCommand? _forgotPasswordCommand;
        public DelegateCommand ForgotPasswordCommand => _forgotPasswordCommand ??= new DelegateCommand(ForgotPassword);

        #endregion

        #region Constructor

        public LoginViewModel()
        {
            var dbService = new DatabaseService();
            _authService = new AuthenticationService(dbService);
            
            // Load saved credentials if remember me was checked
            LoadSavedCredentials();
        }

        #endregion

        #region Methods

        private async void Login()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تسجيل الدخول...";
                LoginButtonText = "جاري التحقق...";
                ClearError();

                // Simulate network delay for better UX
                await Task.Delay(500);

                var result = await _authService.LoginAsync(Username, Password);

                if (result.Success)
                {
                    if (result.RequiresPasswordChange)
                    {
                        // Show password change dialog
                        var changePasswordResult = await ShowChangePasswordDialog(result.User);
                        if (!changePasswordResult)
                        {
                            SetError("يجب تغيير كلمة المرور للمتابعة");
                            return;
                        }
                    }

                    // Save credentials if remember me is checked
                    if (RememberMe)
                    {
                        SaveCredentials();
                    }
                    else
                    {
                        ClearSavedCredentials();
                    }

                    LoggingService.LogSystemEvent("تسجيل دخول ناجح", $"المستخدم: {Username}");
                    
                    // Notify successful login
                    LoginSuccessful?.Invoke(result.User);
                }
                else
                {
                    SetError(result.Message);
                    
                    // Clear password on failed login
                    Password = string.Empty;
                    
                    LoggingService.LogSecurityEvent("فشل تسجيل الدخول", $"المستخدم: {Username} - السبب: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                SetError("حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.");
                LoggingService.LogError(ex, $"خطأ في تسجيل الدخول للمستخدم: {Username}");
            }
            finally
            {
                IsLoading = false;
                LoginButtonText = "تسجيل الدخول";
            }
        }

        private void ForgotPassword()
        {
            try
            {
                // Show forgot password dialog or process
                System.Windows.MessageBox.Show(
                    "يرجى الاتصال بمدير النظام لإعادة تعيين كلمة المرور.",
                    "نسيت كلمة المرور",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
                
                LoggingService.LogSystemEvent("طلب إعادة تعيين كلمة المرور", $"المستخدم: {Username}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في معالجة طلب إعادة تعيين كلمة المرور");
            }
        }

        private async Task<bool> ShowChangePasswordDialog(User user)
        {
            try
            {
                // Create and show change password dialog
                var changePasswordDialog = new Views.Dialogs.ChangePasswordDialog(user);
                var result = changePasswordDialog.ShowDialog();
                
                return result == true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في عرض حوار تغيير كلمة المرور");
                return false;
            }
        }

        private void LoadSavedCredentials()
        {
            try
            {
                var settingsService = new SettingsService(new DatabaseService());
                
                var savedUsername = settingsService.GetSettingAsync("SavedUsername", "").Result;
                var rememberMe = bool.Parse(settingsService.GetSettingAsync("RememberMe", "false").Result);

                if (rememberMe && !string.IsNullOrEmpty(savedUsername))
                {
                    Username = savedUsername;
                    RememberMe = true;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل بيانات الاعتماد المحفوظة");
            }
        }

        private void SaveCredentials()
        {
            try
            {
                var settingsService = new SettingsService(new DatabaseService());
                
                _ = settingsService.SetSettingAsync("SavedUsername", Username);
                _ = settingsService.SetSettingAsync("RememberMe", "true");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حفظ بيانات الاعتماد");
            }
        }

        private void ClearSavedCredentials()
        {
            try
            {
                var settingsService = new SettingsService(new DatabaseService());
                
                _ = settingsService.SetSettingAsync("SavedUsername", "");
                _ = settingsService.SetSettingAsync("RememberMe", "false");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في مسح بيانات الاعتماد المحفوظة");
            }
        }

        public void SetPassword(string password)
        {
            Password = password;
        }

        #endregion

        #region Events

        public event Action<User>? LoginSuccessful;

        #endregion
    }
}
