<Window x:Class="SalesManagementSystem.Views.NotificationSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إعدادات الإشعارات"
        Height="600"
        Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="BellSettings"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="إعدادات الإشعارات"
                      Style="{DynamicResource MaterialDesignHeadline4TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- General Settings -->
                <GroupBox Header="الإعدادات العامة"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        
                        <CheckBox Content="تفعيل الإشعارات"
                                 IsChecked="{Binding EnableNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="عرض إشعارات Toast"
                                 IsChecked="{Binding ShowToastNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="تشغيل الأصوات"
                                 IsChecked="{Binding PlaySounds}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <StackPanel Margin="0,16,0,0">
                            <TextBlock Text="مدة عرض Toast (ثواني)"
                                      Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                      Margin="0,0,0,8"/>
                            <Slider x:Name="ToastDurationSlider"
                                   Minimum="3" Maximum="15" 
                                   Value="{Binding ToastDuration}"
                                   TickFrequency="1"
                                   IsSnapToTickEnabled="True"
                                   Style="{DynamicResource MaterialDesignSlider}"/>
                            <TextBlock Text="{Binding ElementName=ToastDurationSlider, Path=Value, StringFormat='{}{0:F0} ثانية'}"
                                      HorizontalAlignment="Center"
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                        
                        <StackPanel Margin="0,16,0,0">
                            <TextBlock Text="الحد الأقصى لإشعارات Toast"
                                      Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                      Margin="0,0,0,8"/>
                            <Slider x:Name="MaxToastsSlider"
                                   Minimum="1" Maximum="10" 
                                   Value="{Binding MaxToasts}"
                                   TickFrequency="1"
                                   IsSnapToTickEnabled="True"
                                   Style="{DynamicResource MaterialDesignSlider}"/>
                            <TextBlock Text="{Binding ElementName=MaxToastsSlider, Path=Value, StringFormat='{}{0:F0} إشعار'}"
                                      HorizontalAlignment="Center"
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- Notification Types -->
                <GroupBox Header="أنواع الإشعارات"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        
                        <CheckBox Content="إشعارات المخزون المنخفض"
                                 IsChecked="{Binding ShowLowStockNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="إشعارات نفاد المخزون"
                                 IsChecked="{Binding ShowOutOfStockNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="إشعارات استحقاق المدفوعات"
                                 IsChecked="{Binding ShowPaymentDueNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="إشعارات النظام"
                                 IsChecked="{Binding ShowSystemNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="إشعارات النسخ الاحتياطي"
                                 IsChecked="{Binding ShowBackupNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                    </StackPanel>
                </GroupBox>

                <!-- Priority Settings -->
                <GroupBox Header="إعدادات الأولوية"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        
                        <CheckBox Content="عرض Toast للإشعارات منخفضة الأولوية"
                                 IsChecked="{Binding ShowLowPriorityToast}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="تشغيل صوت للإشعارات عالية الأولوية"
                                 IsChecked="{Binding PlaySoundForHighPriority}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="عدم إغلاق الإشعارات الحرجة تلقائياً"
                                 IsChecked="{Binding NeverAutoDismissCritical}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                    </StackPanel>
                </GroupBox>

                <!-- Schedule Settings -->
                <GroupBox Header="إعدادات الجدولة"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        
                        <CheckBox Content="تفعيل الإشعارات المجدولة"
                                 IsChecked="{Binding EnableScheduledNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <CheckBox Content="إشعارات خارج ساعات العمل"
                                 IsChecked="{Binding AllowAfterHoursNotifications}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                            <TextBlock Text="ساعات العمل من:"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <materialDesign:TimePicker SelectedTime="{Binding WorkHoursStart}"
                                                     Width="100"
                                                     Margin="0,0,8,0"/>
                            <TextBlock Text="إلى:"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <materialDesign:TimePicker SelectedTime="{Binding WorkHoursEnd}"
                                                     Width="100"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- Auto-cleanup Settings -->
                <GroupBox Header="التنظيف التلقائي"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        
                        <CheckBox Content="تفعيل التنظيف التلقائي"
                                 IsChecked="{Binding EnableAutoCleanup}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,8"/>
                        
                        <StackPanel Margin="0,16,0,0">
                            <TextBlock Text="حذف الإشعارات القديمة بعد (أيام)"
                                      Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                      Margin="0,0,0,8"/>
                            <Slider x:Name="CleanupDaysSlider"
                                   Minimum="7" Maximum="365" 
                                   Value="{Binding CleanupAfterDays}"
                                   TickFrequency="7"
                                   IsSnapToTickEnabled="True"
                                   Style="{DynamicResource MaterialDesignSlider}"/>
                            <TextBlock Text="{Binding ElementName=CleanupDaysSlider, Path=Value, StringFormat='{}{0:F0} يوم'}"
                                      HorizontalAlignment="Center"
                                      Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- Test Section -->
                <GroupBox Header="اختبار الإشعارات"
                         Style="{DynamicResource MaterialDesignCardGroupBox}">
                    <StackPanel Margin="16">
                        <TextBlock Text="اختبر إعداداتك بإرسال إشعارات تجريبية"
                                  Style="{DynamicResource MaterialDesignBody2TextBlock}"
                                  Margin="0,0,0,16"/>
                        
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Command="{Binding TestInfoNotificationCommand}"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Content="معلومات"
                                   Margin="0,0,8,0"/>
                            <Button Command="{Binding TestWarningNotificationCommand}"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Content="تحذير"
                                   Margin="0,0,8,0"/>
                            <Button Command="{Binding TestErrorNotificationCommand}"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Content="خطأ"
                                   Margin="0,0,8,0"/>
                            <Button Command="{Binding TestSuccessNotificationCommand}"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Content="نجاح"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right"
                   Margin="0,24,0,0">
            
            <Button Command="{Binding ResetToDefaultsCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Content="استعادة الافتراضي"
                   Margin="0,0,8,0"/>
            
            <Button Command="{Binding SaveCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Content="حفظ"
                   Margin="0,0,8,0"/>
            
            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Content="إلغاء"/>
        </StackPanel>
    </Grid>
</Window>
