using System;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem
{
    public partial class TestAuthenticationSystem : Window
    {
        private readonly AuthenticationService _authService;
        private readonly SecurityService _securityService;
        private User? _currentUser;

        public TestAuthenticationSystem()
        {
            InitializeComponent();
            
            var dbService = new DatabaseService();
            _authService = new AuthenticationService(dbService);
            _securityService = new SecurityService();
            
            AddResult("نظام المصادقة والأمان جاهز للاختبار");
        }

        #region Event Handlers

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var loginWindow = new LoginWindow();
                var result = loginWindow.ShowDialog();

                if (result == true && loginWindow.AuthenticatedUser != null)
                {
                    _currentUser = loginWindow.AuthenticatedUser;
                    UpdateCurrentUserDisplay();
                    AddResult($"✅ تم تسجيل الدخول بنجاح: {_currentUser.Username}");
                }
                else
                {
                    AddResult("❌ تم إلغاء تسجيل الدخول");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _authService.Logout();
                _currentUser = null;
                UpdateCurrentUserDisplay();
                AddResult("✅ تم تسجيل الخروج بنجاح");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تسجيل الخروج: {ex.Message}");
            }
        }

        private void ChangePasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentUser == null)
                {
                    AddResult("❌ يجب تسجيل الدخول أولاً");
                    return;
                }

                var changePasswordDialog = new ChangePasswordDialog(_currentUser);
                var result = changePasswordDialog.ShowDialog();

                if (result == true)
                {
                    AddResult("✅ تم تغيير كلمة المرور بنجاح");
                }
                else
                {
                    AddResult("❌ تم إلغاء تغيير كلمة المرور");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تغيير كلمة المرور: {ex.Message}");
            }
        }

        private async void CreateUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create a test user
                var testUser = new User
                {
                    Username = "testuser",
                    Email = "<EMAIL>",
                    FullName = "مستخدم تجريبي",
                    RoleName = "Cashier",
                    IsActive = true
                };

                var success = await _authService.CreateUserAsync(testUser, "TestPassword123!");
                
                if (success)
                {
                    AddResult("✅ تم إنشاء المستخدم التجريبي بنجاح");
                    AddResult($"   اسم المستخدم: {testUser.Username}");
                    AddResult($"   كلمة المرور: TestPassword123!");
                }
                else
                {
                    AddResult("❌ فشل في إنشاء المستخدم التجريبي");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إنشاء المستخدم: {ex.Message}");
            }
        }

        private void TestPasswordStrengthButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار قوة كلمات المرور:");
                
                var passwords = new[]
                {
                    "123",
                    "password",
                    "Password123",
                    "MyStr0ng!Pass",
                    "VeryStr0ng!P@ssw0rd2024"
                };

                foreach (var password in passwords)
                {
                    var strength = _securityService.ValidatePasswordStrength(password);
                    AddResult($"   '{password}' -> {strength.Level} (النقاط: {strength.Score}/5)");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في اختبار قوة كلمة المرور: {ex.Message}");
            }
        }

        private void TestEncryptionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔐 اختبار التشفير:");
                
                var testData = "هذا نص تجريبي للتشفير";
                var password = "MySecretKey123!";
                
                // Test encryption
                var encrypted = _securityService.EncryptData(testData, password);
                AddResult($"   النص الأصلي: {testData}");
                AddResult($"   النص المشفر: {encrypted}");
                
                // Test decryption
                var decrypted = _securityService.DecryptData(encrypted, password);
                AddResult($"   النص بعد فك التشفير: {decrypted}");
                
                if (testData == decrypted)
                {
                    AddResult("✅ اختبار التشفير نجح");
                }
                else
                {
                    AddResult("❌ اختبار التشفير فشل");
                }
                
                // Test password hashing
                var (hash, salt) = _securityService.HashPassword("TestPassword123!");
                var isValid = _securityService.VerifyPassword("TestPassword123!", hash, salt);
                AddResult($"   اختبار تشفير كلمة المرور: {(isValid ? "✅ نجح" : "❌ فشل")}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في اختبار التشفير: {ex.Message}");
            }
        }

        private void GeneratePasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🎲 توليد كلمات مرور:");
                
                for (int i = 0; i < 5; i++)
                {
                    var password = _securityService.GenerateSecurePassword(12);
                    var strength = _securityService.ValidatePasswordStrength(password);
                    AddResult($"   {password} -> {strength.Level}");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في توليد كلمة المرور: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private void UpdateCurrentUserDisplay()
        {
            if (_currentUser != null)
            {
                CurrentUserTextBlock.Text = _currentUser.Username;
                CurrentRoleTextBlock.Text = _currentUser.RoleName;
                LastLoginTextBlock.Text = _currentUser.LastLoginText;
                
                LogoutButton.IsEnabled = true;
                ChangePasswordButton.IsEnabled = true;
            }
            else
            {
                CurrentUserTextBlock.Text = "غير مسجل دخول";
                CurrentRoleTextBlock.Text = "-";
                LastLoginTextBlock.Text = "-";
                
                LogoutButton.IsEnabled = false;
                ChangePasswordButton.IsEnabled = false;
            }
        }

        private void AddResult(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var newText = $"[{timestamp}] {message}\n";
            
            ResultsTextBox.Text += newText;
            ResultsTextBox.ScrollToEnd();
        }

        #endregion
    }
}
