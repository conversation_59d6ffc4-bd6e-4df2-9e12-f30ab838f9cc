using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Configurations;
using LiveCharts.Wpf;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class ChartService
    {
        private readonly ReportService _reportService;

        public ChartService(ReportService reportService)
        {
            _reportService = reportService;
        }

        #region Chart Data Preparation

        /// <summary>
        /// إعداد بيانات الرسم البياني للمبيعات الشهرية
        /// </summary>
        public async Task<SeriesCollection> GetMonthlySalesChartAsync(int year)
        {
            try
            {
                var startDate = new DateTime(year, 1, 1);
                var endDate = new DateTime(year, 12, 31);
                var salesReport = await _reportService.GetSalesReportAsync(startDate, endDate);

                var series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "المبيعات الشهرية",
                        Values = new ChartValues<decimal>(salesReport.SalesByMonth.Select(m => m.TotalAmount)),
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                        StrokeThickness = 3
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعداد بيانات الرسم البياني للمبيعات الشهرية");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// إعداد بيانات الرسم البياني الدائري للمنتجات الأكثر مبيعاً
        /// </summary>
        public async Task<SeriesCollection> GetTopProductsPieChartAsync(DateTime startDate, DateTime endDate, int topCount = 10)
        {
            try
            {
                var productSales = await _reportService.GetProductSalesReportAsync(startDate, endDate);
                var topProducts = productSales.Take(topCount);

                var series = new SeriesCollection();
                var colors = GetChartColors();
                int colorIndex = 0;

                foreach (var product in topProducts)
                {
                    series.Add(new PieSeries
                    {
                        Title = product.ProductName,
                        Values = new ChartValues<decimal> { product.TotalAmount },
                        DataLabels = true,
                        LabelPoint = chartPoint => $"{product.ProductName}: {chartPoint.Y:C}",
                        Fill = new SolidColorBrush(colors[colorIndex % colors.Count])
                    });
                    colorIndex++;
                }

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعداد بيانات الرسم البياني الدائري للمنتجات");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// إعداد بيانات الرسم البياني للأرباح والخسائر
        /// </summary>
        public async Task<SeriesCollection> GetProfitLossChartAsync(int year)
        {
            try
            {
                var startDate = new DateTime(year, 1, 1);
                var endDate = new DateTime(year, 12, 31);
                var profitLoss = await _reportService.GetProfitLossReportAsync(startDate, endDate);

                // Create dummy monthly data for demonstration
                var monthlyProfit = new List<MonthlyProfitSummary>();
                for (int i = 1; i <= 12; i++)
                {
                    monthlyProfit.Add(new MonthlyProfitSummary
                    {
                        Month = $"{year}-{i:00}",
                        MonthName = new DateTime(year, i, 1).ToString("MMMM"),
                        Revenue = profitLoss.TotalRevenue / 12,
                        CostOfGoods = profitLoss.TotalCOGS / 12,
                        Expenses = profitLoss.TotalExpenses / 12,
                        NetProfit = profitLoss.NetProfit / 12
                    });
                }

                var series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "الإيرادات",
                        Values = new ChartValues<decimal>(monthlyProfit.Select(m => m.Revenue)),
                        Fill = new SolidColorBrush(Color.FromRgb(76, 175, 80))
                    },
                    new ColumnSeries
                    {
                        Title = "التكاليف",
                        Values = new ChartValues<decimal>(monthlyProfit.Select(m => m.CostOfGoods)),
                        Fill = new SolidColorBrush(Color.FromRgb(255, 152, 0))
                    },
                    new ColumnSeries
                    {
                        Title = "المصروفات",
                        Values = new ChartValues<decimal>(monthlyProfit.Select(m => m.Expenses)),
                        Fill = new SolidColorBrush(Color.FromRgb(244, 67, 54))
                    },
                    new LineSeries
                    {
                        Title = "صافي الربح",
                        Values = new ChartValues<decimal>(monthlyProfit.Select(m => m.NetProfit)),
                        PointGeometry = DefaultGeometries.Diamond,
                        PointGeometrySize = 8,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(156, 39, 176)),
                        StrokeThickness = 3
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعداد بيانات الرسم البياني للأرباح والخسائر");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// إعداد بيانات الرسم البياني لمقارنة الفترات
        /// </summary>
        public SeriesCollection GetPeriodComparisonChart(PeriodComparisonReport report)
        {
            try
            {
                var series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = report.Period1.PeriodName,
                        Values = new ChartValues<decimal>
                        {
                            report.Period1.Sales,
                            report.Period1.Purchases,
                            report.Period1.Expenses,
                            report.Period1.Profit
                        },
                        Fill = new SolidColorBrush(Color.FromRgb(33, 150, 243))
                    },
                    new ColumnSeries
                    {
                        Title = report.Period2.PeriodName,
                        Values = new ChartValues<decimal>
                        {
                            report.Period2.Sales,
                            report.Period2.Purchases,
                            report.Period2.Expenses,
                            report.Period2.Profit
                        },
                        Fill = new SolidColorBrush(Color.FromRgb(76, 175, 80))
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعداد بيانات الرسم البياني لمقارنة الفترات");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// إعداد بيانات الرسم البياني لتحليل الاتجاهات
        /// </summary>
        public SeriesCollection GetTrendAnalysisChart(TrendAnalysisReport report)
        {
            try
            {
                var series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "المبيعات",
                        Values = new ChartValues<decimal>(report.SalesTrend.Select(t => t.Value)),
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 6,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                        StrokeThickness = 2
                    },
                    new LineSeries
                    {
                        Title = "المشتريات",
                        Values = new ChartValues<decimal>(report.PurchasesTrend.Select(t => t.Value)),
                        PointGeometry = DefaultGeometries.Square,
                        PointGeometrySize = 6,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(255, 152, 0)),
                        StrokeThickness = 2
                    },
                    new LineSeries
                    {
                        Title = "الأرباح",
                        Values = new ChartValues<decimal>(report.ProfitTrend.Select(t => t.Value)),
                        PointGeometry = DefaultGeometries.Diamond,
                        PointGeometrySize = 6,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                        StrokeThickness = 2
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعداد بيانات الرسم البياني لتحليل الاتجاهات");
                return new SeriesCollection();
            }
        }

        /// <summary>
        /// إعداد بيانات الرسم البياني لأداء الموظفين
        /// </summary>
        public SeriesCollection GetEmployeePerformanceChart(List<EmployeePerformanceReport> employeeReports)
        {
            try
            {
                var series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "إجمالي المبيعات",
                        Values = new ChartValues<decimal>(employeeReports.Select(e => e.TotalRevenue)),
                        Fill = new SolidColorBrush(Color.FromRgb(33, 150, 243))
                    },
                    new LineSeries
                    {
                        Title = "متوسط قيمة الطلب",
                        Values = new ChartValues<decimal>(employeeReports.Select(e => e.AverageOrderValue)),
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8,
                        Fill = Brushes.Transparent,
                        Stroke = new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                        StrokeThickness = 3,
                        ScalesYAt = 1
                    }
                };

                return series;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إعداد بيانات الرسم البياني لأداء الموظفين");
                return new SeriesCollection();
            }
        }

        #endregion

        #region Chart Labels

        /// <summary>
        /// الحصول على تسميات الأشهر
        /// </summary>
        public string[] GetMonthLabels()
        {
            return new[]
            {
                "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
            };
        }

        /// <summary>
        /// الحصول على تسميات مقارنة الفترات
        /// </summary>
        public string[] GetPeriodComparisonLabels()
        {
            return new[] { "المبيعات", "المشتريات", "المصروفات", "الأرباح" };
        }

        /// <summary>
        /// الحصول على تسميات الموظفين
        /// </summary>
        public string[] GetEmployeeLabels(List<EmployeePerformanceReport> employeeReports)
        {
            return employeeReports.Select(e => e.EmployeeName).ToArray();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// الحصول على ألوان الرسوم البيانية
        /// </summary>
        private List<Color> GetChartColors()
        {
            return new List<Color>
            {
                Color.FromRgb(33, 150, 243),   // Blue
                Color.FromRgb(76, 175, 80),    // Green
                Color.FromRgb(255, 152, 0),    // Orange
                Color.FromRgb(244, 67, 54),    // Red
                Color.FromRgb(156, 39, 176),   // Purple
                Color.FromRgb(0, 188, 212),    // Cyan
                Color.FromRgb(255, 193, 7),    // Amber
                Color.FromRgb(121, 85, 72),    // Brown
                Color.FromRgb(158, 158, 158),  // Grey
                Color.FromRgb(255, 87, 34)     // Deep Orange
            };
        }

        /// <summary>
        /// تنسيق القيم المالية للرسوم البيانية
        /// </summary>
        public Func<double, string> GetCurrencyFormatter()
        {
            return value => value.ToString("C");
        }

        /// <summary>
        /// تنسيق النسب المئوية للرسوم البيانية
        /// </summary>
        public Func<double, string> GetPercentageFormatter()
        {
            return value => $"{value:F1}%";
        }

        #endregion
    }

}
