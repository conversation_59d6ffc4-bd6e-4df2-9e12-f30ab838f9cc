<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="8.0.5" />
    <PackageReference Include="iTextSharp" Version="5.5.13.4" />
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="MahApps.Metro" Version="2.4.9" />
    <PackageReference Include="MaterialDesignThemes" Version="4.6.1" />
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WPF" Version="2.0.0-rc2" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.116" />
    <PackageReference Include="Prism.Core" Version="8.1.97" />
    <PackageReference Include="FluentValidation" Version="11.2.2" />
    <PackageReference Include="NLog" Version="5.0.4" />
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="ZXing.Net.Bindings.Windows.Compatibility" Version="0.16.12" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
  </ItemGroup>



  <ItemGroup>
    <Folder Include="Assets\Fonts\" />
    <Folder Include="Assets\Styles\" />
  </ItemGroup>

</Project>