using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class ReportService
    {
        private readonly DatabaseService _dbService;
        private readonly SaleService _saleService;
        private readonly PurchaseService _purchaseService;
        private readonly ProductService _productService;
        private readonly CustomerService _customerService;
        private readonly SupplierService _supplierService;
        private readonly EmployeeService _employeeService;
        private readonly ExpenseService _expenseService;

        public ReportService(
            DatabaseService dbService,
            SaleService saleService,
            PurchaseService purchaseService,
            ProductService productService,
            CustomerService customerService,
            SupplierService supplierService,
            EmployeeService employeeService,
            ExpenseService expenseService)
        {
            _dbService = dbService;
            _saleService = saleService;
            _purchaseService = purchaseService;
            _productService = productService;
            _customerService = customerService;
            _supplierService = supplierService;
            _employeeService = employeeService;
            _expenseService = expenseService;
        }

        #region Sales Reports

        public async Task<SalesReport> GetSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            var report = new SalesReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSales = await _saleService.GetTotalSalesAsync(startDate, endDate),
                SalesByMonth = (await _saleService.GetSaleSummaryByMonthAsync(DateTime.Now.Year)).ToList(),
                TopProducts = (await _saleService.GetSaleSummaryByProductAsync(startDate, endDate)).Take(10).ToList(),
                TopCustomers = (await _customerService.GetTopCustomersAsync(10)).ToList()
            };

            return report;
        }

        public async Task<IEnumerable<SaleSummaryByMonth>> GetMonthlySalesReportAsync(int year)
        {
            return await _saleService.GetSaleSummaryByMonthAsync(year);
        }

        public async Task<IEnumerable<SaleSummaryByProduct>> GetProductSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            return await _saleService.GetSaleSummaryByProductAsync(startDate, endDate);
        }

        #endregion

        #region Purchase Reports

        public async Task<PurchaseReport> GetPurchaseReportAsync(DateTime startDate, DateTime endDate)
        {
            var report = new PurchaseReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalPurchases = await _purchaseService.GetTotalPurchasesAsync(startDate, endDate),
                PurchasesByMonth = (await _purchaseService.GetPurchaseSummaryByMonthAsync(DateTime.Now.Year)).ToList(),
                TopProducts = (await _purchaseService.GetPurchaseSummaryByProductAsync(startDate, endDate)).Take(10).ToList(),
                TopSuppliers = (await _supplierService.GetTopSuppliersAsync(10)).ToList()
            };

            return report;
        }

        public async Task<IEnumerable<PurchaseSummaryByMonth>> GetMonthlyPurchaseReportAsync(int year)
        {
            return await _purchaseService.GetPurchaseSummaryByMonthAsync(year);
        }

        public async Task<IEnumerable<PurchaseSummaryByProduct>> GetProductPurchaseReportAsync(DateTime startDate, DateTime endDate)
        {
            return await _purchaseService.GetPurchaseSummaryByProductAsync(startDate, endDate);
        }

        #endregion

        #region Inventory Reports

        public async Task<InventoryReport> GetInventoryReportAsync()
        {
            var report = new InventoryReport
            {
                TotalProducts = await _productService.GetProductCountAsync(),
                TotalStockValue = await _productService.GetTotalStockValueAsync(),
                LowStockProducts = (await _productService.GetLowStockProductsAsync()).ToList(),
                ProductsByCategory = (await _productService.GetProductCountByCategoryAsync()).Select(c => new CategoryCount
                {
                    CategoryName = c.CategoryName,
                    ProductCount = c.ProductCount
                }).ToList()
            };

            return report;
        }

        public async Task<IEnumerable<Product>> GetLowStockReportAsync()
        {
            return await _productService.GetLowStockProductsAsync();
        }

        #endregion

        #region Financial Reports

        public async Task<FinancialReport> GetFinancialReportAsync(DateTime startDate, DateTime endDate)
        {
            decimal totalSales = await _saleService.GetTotalSalesAsync(startDate, endDate);
            decimal totalPurchases = await _purchaseService.GetTotalPurchasesAsync(startDate, endDate);
            decimal totalExpenses = await _expenseService.GetTotalExpensesAsync(startDate, endDate);
            decimal totalSalaries = await _employeeService.GetTotalSalariesAsync();

            var report = new FinancialReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRevenue = totalSales,
                TotalCostOfGoods = totalPurchases,
                TotalExpenses = totalExpenses + totalSalaries,
                GrossProfit = totalSales - totalPurchases,
                NetProfit = totalSales - totalPurchases - totalExpenses - totalSalaries,
                ExpensesByCategory = (await _expenseService.GetExpenseSummaryByCategoryAsync(startDate, endDate)).ToList(),
                MonthlyProfitSummary = await GetMonthlyProfitSummaryAsync(DateTime.Now.Year)
            };

            return report;
        }

        public async Task<IEnumerable<MonthlyProfitSummary>> GetMonthlyProfitSummaryAsync(int year)
        {
            var salesByMonth = await _saleService.GetSaleSummaryByMonthAsync(year);
            var purchasesByMonth = await _purchaseService.GetPurchaseSummaryByMonthAsync(year);
            var expensesByMonth = await _expenseService.GetExpenseSummaryByMonthAsync(year);

            var result = new List<MonthlyProfitSummary>();

            for (int month = 1; month <= 12; month++)
            {
                string monthStr = month.ToString("D2");
                var salesForMonth = salesByMonth.FirstOrDefault(s => s.Month == monthStr);
                var purchasesForMonth = purchasesByMonth.FirstOrDefault(p => p.Month == monthStr);
                var expensesForMonth = expensesByMonth.FirstOrDefault(e => e.Month == monthStr);

                decimal salesAmount = salesForMonth?.TotalAmount ?? 0;
                decimal purchasesAmount = purchasesForMonth?.TotalAmount ?? 0;
                decimal expensesAmount = expensesForMonth?.TotalAmount ?? 0;

                result.Add(new MonthlyProfitSummary
                {
                    Month = monthStr,
                    MonthName = new DateTime(2000, month, 1).ToString("MMMM"),
                    Revenue = salesAmount,
                    CostOfGoods = purchasesAmount,
                    Expenses = expensesAmount,
                    GrossProfit = salesAmount - purchasesAmount,
                    NetProfit = salesAmount - purchasesAmount - expensesAmount
                });
            }

            return result;
        }

        public async Task<CashFlowReport> GetCashFlowReportAsync(DateTime startDate, DateTime endDate)
        {
            // Get all sales with payment method and status
            const string salesSql = @"
                SELECT Date, PaymentMethod, PaymentStatus, Total
                FROM Sales
                WHERE Date BETWEEN @StartDate AND @EndDate";

            var sales = await _dbService.QueryAsync<dynamic>(salesSql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });

            // Get all purchases with payment method and status
            const string purchasesSql = @"
                SELECT Date, PaymentMethod, PaymentStatus, Total
                FROM Purchases
                WHERE Date BETWEEN @StartDate AND @EndDate";

            var purchases = await _dbService.QueryAsync<dynamic>(purchasesSql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd")
            });

            // Get all expenses
            var expenses = await _expenseService.GetExpensesByDateRangeAsync(startDate, endDate);

            // Calculate cash inflows and outflows by payment method
            var cashInflows = sales
                .Where(s => s.PaymentStatus == "Paid")
                .GroupBy(s => s.PaymentMethod)
                .Select(g => new CashFlowItem
                {
                    Category = "Sales",
                    PaymentMethod = g.Key,
                    Amount = g.Sum(s => (decimal)s.Total)
                })
                .ToList();

            var cashOutflows = purchases
                .Where(p => p.PaymentStatus == "Paid")
                .GroupBy(p => p.PaymentMethod)
                .Select(g => new CashFlowItem
                {
                    Category = "Purchases",
                    PaymentMethod = g.Key,
                    Amount = g.Sum(p => (decimal)p.Total)
                })
                .ToList();

            // Add expenses to cash outflows
            cashOutflows.Add(new CashFlowItem
            {
                Category = "Expenses",
                PaymentMethod = "Various",
                Amount = expenses.Sum(e => e.Amount)
            });

            return new CashFlowReport
            {
                StartDate = startDate,
                EndDate = endDate,
                CashInflows = cashInflows,
                CashOutflows = cashOutflows,
                NetCashFlow = cashInflows.Sum(i => i.Amount) - cashOutflows.Sum(o => o.Amount)
            };
        }

        #endregion

        #region Customer Reports

        public async Task<CustomerReport> GetCustomerReportAsync()
        {
            var report = new CustomerReport
            {
                TotalCustomers = await _customerService.GetCustomerCountAsync(),
                TotalOutstandingBalance = await _customerService.GetTotalCustomerBalanceAsync(),
                CustomersWithOutstandingBalance = (await _customerService.GetCustomersWithOutstandingBalanceAsync()).ToList(),
                TopCustomers = (await _customerService.GetTopCustomersAsync(10)).ToList()
            };

            return report;
        }

        #endregion

        #region Supplier Reports

        public async Task<SupplierReport> GetSupplierReportAsync()
        {
            var report = new SupplierReport
            {
                TotalSuppliers = await _supplierService.GetAllSuppliersAsync().ContinueWith(t => t.Result.Count()),
                TotalOutstandingBalance = await _supplierService.GetTotalSupplierBalanceAsync(),
                SuppliersWithOutstandingBalance = (await _supplierService.GetSuppliersWithOutstandingBalanceAsync()).ToList(),
                TopSuppliers = (await _supplierService.GetTopSuppliersAsync(10)).ToList()
            };

            return report;
        }

        #endregion

        #region Employee Reports

        public async Task<EmployeeReport> GetEmployeeReportAsync()
        {
            var report = new EmployeeReport
            {
                TotalEmployees = await _employeeService.GetEmployeeCountAsync(),
                ActiveEmployees = await _employeeService.GetActiveEmployeeCountAsync(),
                TotalSalaries = await _employeeService.GetTotalSalariesAsync(),
                EmployeesByPosition = (await _employeeService.GetEmployeeCountByPositionAsync()).Select(e => new PositionCount
                {
                    Position = e.Position,
                    Count = e.EmployeeCount
                })
            };

            return report;
        }

        #endregion

        #region Dashboard

        public async Task<DashboardData> GetDashboardDataAsync()
        {
            // Get current month data
            DateTime startDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            DateTime endDate = startDate.AddMonths(1).AddDays(-1);

            // Get previous month data for comparison
            DateTime prevStartDate = startDate.AddMonths(-1);
            DateTime prevEndDate = startDate.AddDays(-1);

            decimal currentSales = await _saleService.GetTotalSalesAsync(startDate, endDate);
            decimal previousSales = await _saleService.GetTotalSalesAsync(prevStartDate, prevEndDate);

            decimal currentPurchases = await _purchaseService.GetTotalPurchasesAsync(startDate, endDate);
            decimal previousPurchases = await _purchaseService.GetTotalPurchasesAsync(prevStartDate, prevEndDate);

            decimal currentExpenses = await _expenseService.GetTotalExpensesAsync(startDate, endDate);
            decimal previousExpenses = await _expenseService.GetTotalExpensesAsync(prevStartDate, prevEndDate);

            decimal currentProfit = currentSales - currentPurchases - currentExpenses;
            decimal previousProfit = previousSales - previousPurchases - previousExpenses;

            var dashboard = new DashboardData
            {
                TodaySales = await _saleService.GetTotalSalesAsync(DateTime.Today, DateTime.Today),
                CurrentMonthSales = currentSales,
                SalesGrowth = CalculateGrowthPercentage(previousSales, currentSales),

                TodayPurchases = await _purchaseService.GetTotalPurchasesAsync(DateTime.Today, DateTime.Today),
                CurrentMonthPurchases = currentPurchases,
                PurchasesGrowth = CalculateGrowthPercentage(previousPurchases, currentPurchases),

                TodayExpenses = await _expenseService.GetTotalExpensesAsync(DateTime.Today, DateTime.Today),
                CurrentMonthExpenses = currentExpenses,
                ExpensesGrowth = CalculateGrowthPercentage(previousExpenses, currentExpenses),

                CurrentMonthProfit = currentProfit,
                ProfitGrowth = CalculateGrowthPercentage(previousProfit, currentProfit),

                LowStockProducts = (await _productService.GetLowStockProductsAsync()).Take(5).ToList(),
                RecentSales = (await _saleService.GetSalesByDateRangeAsync(DateTime.Today.AddDays(-7), DateTime.Today)).Take(5).ToList(),
                TopSellingProducts = (await _saleService.GetSaleSummaryByProductAsync(startDate, endDate)).Take(5).ToList(),
                CustomersWithOutstandingBalance = (await _customerService.GetCustomersWithOutstandingBalanceAsync()).Take(5).ToList()
            };

            return dashboard;
        }

        private decimal CalculateGrowthPercentage(decimal previous, decimal current)
        {
            if (previous == 0)
            {
                return current > 0 ? 100 : 0;
            }

            return Math.Round(((current - previous) / previous) * 100, 2);
        }

        #endregion

        #region Advanced Reports

        /// <summary>
        /// تقرير مقارنة الفترات
        /// </summary>
        public async Task<PeriodComparisonReport> GetPeriodComparisonReportAsync(
            DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        {
            try
            {
                var period1Sales = await GetTotalSalesAsync(period1Start, period1End);
                var period1Purchases = await GetTotalPurchasesAsync(period1Start, period1End);
                var period1Expenses = await GetTotalExpensesAsync(period1Start, period1End);

                var period2Sales = await GetTotalSalesAsync(period2Start, period2End);
                var period2Purchases = await GetTotalPurchasesAsync(period2Start, period2End);
                var period2Expenses = await GetTotalExpensesAsync(period2Start, period2End);

                return new PeriodComparisonReport
                {
                    Period1 = new PeriodData
                    {
                        StartDate = period1Start,
                        EndDate = period1End,
                        Sales = period1Sales,
                        Purchases = period1Purchases,
                        Expenses = period1Expenses,
                        Profit = period1Sales - period1Purchases - period1Expenses
                    },
                    Period2 = new PeriodData
                    {
                        StartDate = period2Start,
                        EndDate = period2End,
                        Sales = period2Sales,
                        Purchases = period2Purchases,
                        Expenses = period2Expenses,
                        Profit = period2Sales - period2Purchases - period2Expenses
                    }
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير مقارنة الفترات");
                throw;
            }
        }

        /// <summary>
        /// تقرير الاتجاهات والتوقعات
        /// </summary>
        public async Task<TrendAnalysisReport> GetTrendAnalysisReportAsync(int months = 12)
        {
            try
            {
                var endDate = DateTime.Today;
                var startDate = endDate.AddMonths(-months);

                var monthlySales = new List<MonthlyTrendData>();
                var monthlyPurchases = new List<MonthlyTrendData>();
                var monthlyProfits = new List<MonthlyTrendData>();

                for (int i = 0; i < months; i++)
                {
                    var monthStart = startDate.AddMonths(i);
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                    var sales = await GetTotalSalesAsync(monthStart, monthEnd);
                    var purchases = await GetTotalPurchasesAsync(monthStart, monthEnd);
                    var expenses = await GetTotalExpensesAsync(monthStart, monthEnd);
                    var profit = sales - purchases - expenses;

                    monthlySales.Add(new MonthlyTrendData
                    {
                        Month = monthStart.ToString("yyyy-MM"),
                        MonthName = monthStart.ToString("MMMM yyyy"),
                        Value = sales
                    });

                    monthlyPurchases.Add(new MonthlyTrendData
                    {
                        Month = monthStart.ToString("yyyy-MM"),
                        MonthName = monthStart.ToString("MMMM yyyy"),
                        Value = purchases
                    });

                    monthlyProfits.Add(new MonthlyTrendData
                    {
                        Month = monthStart.ToString("yyyy-MM"),
                        MonthName = monthStart.ToString("MMMM yyyy"),
                        Value = profit
                    });
                }

                return new TrendAnalysisReport
                {
                    SalesTrend = monthlySales,
                    PurchasesTrend = monthlyPurchases,
                    ProfitTrend = monthlyProfits,
                    SalesGrowthRate = CalculateTrendGrowthRate(monthlySales),
                    ProfitGrowthRate = CalculateTrendGrowthRate(monthlyProfits)
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير تحليل الاتجاهات");
                throw;
            }
        }

        private decimal CalculateTrendGrowthRate(List<MonthlyTrendData> data)
        {
            if (data.Count < 2) return 0;

            var firstValue = data.First().Value;
            var lastValue = data.Last().Value;

            if (firstValue == 0) return lastValue > 0 ? 100 : 0;

            return Math.Round(((lastValue - firstValue) / firstValue) * 100, 2);
        }

        /// <summary>
        /// تقرير أداء المبيعات حسب الموظف
        /// </summary>
        public async Task<List<EmployeePerformanceReport>> GetEmployeePerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                const string sql = @"
                    SELECT
                        e.Id as EmployeeId,
                        e.FullName as EmployeeName,
                        e.Position,
                        COUNT(s.Id) as TotalSales,
                        SUM(s.NetAmount) as TotalRevenue,
                        AVG(s.NetAmount) as AverageOrderValue,
                        SUM(s.DiscountAmount) as TotalDiscounts
                    FROM Employees e
                    LEFT JOIN Sales s ON e.Id = s.EmployeeId AND s.SaleDate BETWEEN @StartDate AND @EndDate
                    WHERE e.IsActive = 1
                    GROUP BY e.Id, e.FullName, e.Position
                    ORDER BY TotalRevenue DESC";

                var result = await _dbService.QueryAsync<EmployeePerformanceReport>(sql, new { StartDate = startDate, EndDate = endDate });
                return result.ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير أداء الموظفين");
                throw;
            }
        }

        private async Task<decimal> GetTotalPurchasesAsync(DateTime startDate, DateTime endDate)
        {
            var sql = "SELECT COALESCE(SUM(TotalAmount), 0) FROM Purchases WHERE PurchaseDate BETWEEN @StartDate AND @EndDate";
            return await _dbService.QuerySingleOrDefaultAsync<decimal>(sql, new { StartDate = startDate, EndDate = endDate });
        }

        private async Task<decimal> GetTotalExpensesAsync(DateTime startDate, DateTime endDate)
        {
            var sql = "SELECT COALESCE(SUM(Amount), 0) FROM Expenses WHERE ExpenseDate BETWEEN @StartDate AND @EndDate";
            return await _dbService.QuerySingleOrDefaultAsync<decimal>(sql, new { StartDate = startDate, EndDate = endDate });
        }

        private async Task<decimal> GetTotalSalesAsync(DateTime startDate, DateTime endDate)
        {
            var sql = "SELECT COALESCE(SUM(NetAmount), 0) FROM Sales WHERE SaleDate BETWEEN @StartDate AND @EndDate";
            return await _dbService.QuerySingleOrDefaultAsync<decimal>(sql, new { StartDate = startDate, EndDate = endDate });
        }

        /// <summary>
        /// تقرير الأرباح والخسائر
        /// </summary>
        public async Task<ProfitLossReport> GetProfitLossReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                // إجمالي المبيعات
                var salesSql = @"
                    SELECT
                        COALESCE(SUM(NetAmount), 0) as TotalRevenue,
                        COALESCE(SUM(TaxAmount), 0) as TotalTax,
                        COALESCE(SUM(DiscountAmount), 0) as TotalDiscounts
                    FROM Sales
                    WHERE SaleDate BETWEEN @StartDate AND @EndDate";

                var salesData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(salesSql, new { StartDate = startDate, EndDate = endDate });

                // تكلفة البضاعة المباعة
                var cogsSql = @"
                    SELECT COALESCE(SUM(si.Quantity * p.CostPrice), 0) as TotalCOGS
                    FROM SaleItems si
                    INNER JOIN Sales s ON si.SaleId = s.Id
                    INNER JOIN Products p ON si.ProductId = p.Id
                    WHERE s.SaleDate BETWEEN @StartDate AND @EndDate";

                var cogsData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(cogsSql, new { StartDate = startDate, EndDate = endDate });

                // المصروفات
                var expensesSql = @"
                    SELECT COALESCE(SUM(Amount), 0) as TotalExpenses
                    FROM Expenses
                    WHERE ExpenseDate BETWEEN @StartDate AND @EndDate";

                var expensesData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(expensesSql, new { StartDate = startDate, EndDate = endDate });

                var totalRevenue = (decimal)(salesData?.TotalRevenue ?? 0);
                var totalCOGS = (decimal)(cogsData?.TotalCOGS ?? 0);
                var totalExpenses = (decimal)(expensesData?.TotalExpenses ?? 0);
                var grossProfit = totalRevenue - totalCOGS;
                var netProfit = grossProfit - totalExpenses;

                return new ProfitLossReport
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    TotalRevenue = totalRevenue,
                    TotalCOGS = totalCOGS,
                    GrossProfit = grossProfit,
                    TotalExpenses = totalExpenses,
                    NetProfit = netProfit,
                    GrossProfitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0,
                    NetProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير الأرباح والخسائر");
                throw;
            }
        }

        /// <summary>
        /// تقرير مبيعات العملاء
        /// </summary>
        public async Task<List<CustomerSalesReport>> GetCustomerSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                var sql = @"
                    SELECT
                        c.Id as CustomerId,
                        c.Name as CustomerName,
                        c.Phone as CustomerPhone,
                        c.Email as CustomerEmail,
                        COUNT(s.Id) as TotalOrders,
                        SUM(s.NetAmount) as TotalSpent,
                        AVG(s.NetAmount) as AverageOrderValue,
                        MAX(s.SaleDate) as LastOrderDate,
                        MIN(s.SaleDate) as FirstOrderDate
                    FROM Customers c
                    INNER JOIN Sales s ON c.Id = s.CustomerId
                    WHERE s.SaleDate BETWEEN @StartDate AND @EndDate
                    GROUP BY c.Id, c.Name, c.Phone, c.Email
                    ORDER BY TotalSpent DESC";

                var result = await _dbService.QueryAsync<CustomerSalesReport>(sql, new { StartDate = startDate, EndDate = endDate });
                return result.ToList();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إنشاء تقرير مبيعات العملاء");
                throw;
            }
        }

        #endregion
    }

    #region Report Models

    public class SalesReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public List<SaleSummaryByMonth> SalesByMonth { get; set; } = new();
        public List<SaleSummaryByProduct> TopProducts { get; set; } = new();
        public List<Customer> TopCustomers { get; set; } = new();
    }

    public class PurchaseReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalPurchases { get; set; }
        public List<PurchaseSummaryByMonth> PurchasesByMonth { get; set; } = new();
        public List<PurchaseSummaryByProduct> TopProducts { get; set; } = new();
        public List<Supplier> TopSuppliers { get; set; } = new();
    }

    public class InventoryReport
    {
        public int TotalProducts { get; set; }
        public decimal TotalStockValue { get; set; }
        public List<Product> LowStockProducts { get; set; } = new();
        public List<CategoryCount> ProductsByCategory { get; set; } = new();
    }

    public class CategoryCount
    {
        public int CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public int ProductCount { get; set; }
    }

    public class FinancialReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCostOfGoods { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }
        public List<ExpenseSummaryByCategory> ExpensesByCategory { get; set; } = new();
        public IEnumerable<MonthlyProfitSummary> MonthlyProfitSummary { get; set; } = new List<MonthlyProfitSummary>();

        public decimal GrossProfitMargin => TotalRevenue > 0 ? Math.Round((GrossProfit / TotalRevenue) * 100, 2) : 0;
        public decimal NetProfitMargin => TotalRevenue > 0 ? Math.Round((NetProfit / TotalRevenue) * 100, 2) : 0;
    }

    public class MonthlyProfitSummary
    {
        public string Month { get; set; } = string.Empty;
        public string MonthName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal CostOfGoods { get; set; }
        public decimal Expenses { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal NetProfit { get; set; }

        public decimal GrossProfitMargin => Revenue > 0 ? Math.Round((GrossProfit / Revenue) * 100, 2) : 0;
        public decimal NetProfitMargin => Revenue > 0 ? Math.Round((NetProfit / Revenue) * 100, 2) : 0;
    }

    public class CashFlowReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<CashFlowItem> CashInflows { get; set; } = new();
        public List<CashFlowItem> CashOutflows { get; set; } = new();
        public decimal NetCashFlow { get; set; }
    }

    public class CashFlowItem
    {
        public string Category { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }

    public class CustomerReport
    {
        public int TotalCustomers { get; set; }
        public decimal TotalOutstandingBalance { get; set; }
        public List<Customer> CustomersWithOutstandingBalance { get; set; } = new();
        public List<Customer> TopCustomers { get; set; } = new();
    }

    public class SupplierReport
    {
        public int TotalSuppliers { get; set; }
        public decimal TotalOutstandingBalance { get; set; }
        public List<Supplier> SuppliersWithOutstandingBalance { get; set; } = new();
        public List<Supplier> TopSuppliers { get; set; } = new();
    }

    public class EmployeeReport
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public decimal TotalSalaries { get; set; }
        public IEnumerable<PositionCount> EmployeesByPosition { get; set; } = new List<PositionCount>();
    }

    public class PositionCount
    {
        public string Position { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class DashboardData
    {
        public decimal TodaySales { get; set; }
        public decimal CurrentMonthSales { get; set; }
        public decimal SalesGrowth { get; set; }

        public decimal TodayPurchases { get; set; }
        public decimal CurrentMonthPurchases { get; set; }
        public decimal PurchasesGrowth { get; set; }

        public decimal TodayExpenses { get; set; }
        public decimal CurrentMonthExpenses { get; set; }
        public decimal ExpensesGrowth { get; set; }

        public decimal CurrentMonthProfit { get; set; }
        public decimal ProfitGrowth { get; set; }

        public List<Product> LowStockProducts { get; set; } = new();
        public List<Sale> RecentSales { get; set; } = new();
        public List<SaleSummaryByProduct> TopSellingProducts { get; set; } = new();
        public List<Customer> CustomersWithOutstandingBalance { get; set; } = new();
    }

    public class PeriodComparisonReport
    {
        public PeriodData Period1 { get; set; } = new();
        public PeriodData Period2 { get; set; } = new();

        public decimal SalesChange => Period1.Sales != 0 ? Math.Round(((Period2.Sales - Period1.Sales) / Period1.Sales) * 100, 2) : 0;
        public decimal PurchasesChange => Period1.Purchases != 0 ? Math.Round(((Period2.Purchases - Period1.Purchases) / Period1.Purchases) * 100, 2) : 0;
        public decimal ExpensesChange => Period1.Expenses != 0 ? Math.Round(((Period2.Expenses - Period1.Expenses) / Period1.Expenses) * 100, 2) : 0;
        public decimal ProfitChange => Period1.Profit != 0 ? Math.Round(((Period2.Profit - Period1.Profit) / Period1.Profit) * 100, 2) : 0;
    }

    public class PeriodData
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal Sales { get; set; }
        public decimal Purchases { get; set; }
        public decimal Expenses { get; set; }
        public decimal Profit { get; set; }
        public string PeriodName => $"{StartDate:dd/MM/yyyy} - {EndDate:dd/MM/yyyy}";
    }

    public class TrendAnalysisReport
    {
        public List<MonthlyTrendData> SalesTrend { get; set; } = new();
        public List<MonthlyTrendData> PurchasesTrend { get; set; } = new();
        public List<MonthlyTrendData> ProfitTrend { get; set; } = new();
        public decimal SalesGrowthRate { get; set; }
        public decimal ProfitGrowthRate { get; set; }
    }

    public class MonthlyTrendData
    {
        public string Month { get; set; } = string.Empty;
        public string MonthName { get; set; } = string.Empty;
        public decimal Value { get; set; }
    }

    public class EmployeePerformanceReport
    {
        public int EmployeeId { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageOrderValue { get; set; }
        public decimal TotalDiscounts { get; set; }
        public decimal PerformanceScore => TotalSales > 0 ? Math.Round(TotalRevenue / TotalSales, 2) : 0;
    }

    public class ProfitLossReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCOGS { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal GrossProfitMargin { get; set; }
        public decimal NetProfitMargin { get; set; }
    }

    public class CustomerSalesReport
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public string CustomerEmail { get; set; } = string.Empty;
        public int TotalOrders { get; set; }
        public decimal TotalSpent { get; set; }
        public decimal AverageOrderValue { get; set; }
        public DateTime LastOrderDate { get; set; }
        public DateTime FirstOrderDate { get; set; }
        public string CustomerType => TotalSpent > 10000 ? "VIP" : TotalSpent > 5000 ? "مميز" : "عادي";
    }

    public class ProductSalesReport
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int TotalQuantitySold { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AveragePrice { get; set; }
        public int NumberOfOrders { get; set; }
    }

    public class InventoryReportItem
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public decimal CostPrice { get; set; }
        public decimal SalePrice { get; set; }
        public decimal ProfitMargin => SalePrice > 0 ? ((SalePrice - CostPrice) / SalePrice) * 100 : 0;
        public string StockStatus => CurrentStock <= MinimumStock ? "منخفض" : CurrentStock <= MinimumStock * 2 ? "متوسط" : "جيد";
    }

    public class SaleReportItem
    {
        public int SaleId { get; set; }
        public DateTime SaleDate { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal NetAmount { get; set; }
        public string SalespersonName { get; set; } = string.Empty;
    }

    #endregion
}