using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج الكوبون والعروض
    /// </summary>
    public class Coupon : INotifyPropertyChanged
    {
        private int _id;
        private int _storeId;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private CouponType _type = CouponType.Percentage;
        private CouponStatus _status = CouponStatus.Active;
        private decimal _value;
        private decimal _minimumAmount;
        private decimal _maximumDiscount;
        private int _usageLimit;
        private int _usageCount;
        private int _usageLimitPerCustomer = 1;
        private bool _isFirstOrderOnly;
        private bool _isFreeShipping;
        private DateTime _startDate = DateTime.Now;
        private DateTime _endDate = DateTime.Now.AddDays(30);
        private string _applicableProducts = string.Empty;
        private string _applicableCategories = string.Empty;
        private string _excludedProducts = string.Empty;
        private string _excludedCategories = string.Empty;
        private string _applicableCustomers = string.Empty;
        private string _notes = string.Empty;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private ObservableCollection<CouponUsage> _usageHistory = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int StoreId
        {
            get => _storeId;
            set
            {
                if (_storeId != value)
                {
                    _storeId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value?.ToUpper();
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public CouponType Type
        {
            get => _type;
            set
            {
                if (_type != value)
                {
                    _type = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(TypeDisplay));
                    OnPropertyChanged(nameof(TypeIcon));
                    OnPropertyChanged(nameof(FormattedValue));
                }
            }
        }

        public CouponStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public decimal Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    _value = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedValue));
                }
            }
        }

        public decimal MinimumAmount
        {
            get => _minimumAmount;
            set
            {
                if (_minimumAmount != value)
                {
                    _minimumAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMinimumAmount));
                }
            }
        }

        public decimal MaximumDiscount
        {
            get => _maximumDiscount;
            set
            {
                if (_maximumDiscount != value)
                {
                    _maximumDiscount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMaximumDiscount));
                }
            }
        }

        public int UsageLimit
        {
            get => _usageLimit;
            set
            {
                if (_usageLimit != value)
                {
                    _usageLimit = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RemainingUses));
                    OnPropertyChanged(nameof(IsUsageLimitReached));
                }
            }
        }

        public int UsageCount
        {
            get => _usageCount;
            set
            {
                if (_usageCount != value)
                {
                    _usageCount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RemainingUses));
                    OnPropertyChanged(nameof(IsUsageLimitReached));
                }
            }
        }

        public int UsageLimitPerCustomer
        {
            get => _usageLimitPerCustomer;
            set
            {
                if (_usageLimitPerCustomer != value)
                {
                    _usageLimitPerCustomer = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsFirstOrderOnly
        {
            get => _isFirstOrderOnly;
            set
            {
                if (_isFirstOrderOnly != value)
                {
                    _isFirstOrderOnly = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsFreeShipping
        {
            get => _isFreeShipping;
            set
            {
                if (_isFreeShipping != value)
                {
                    _isFreeShipping = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime StartDate
        {
            get => _startDate;
            set
            {
                if (_startDate != value)
                {
                    _startDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedStartDate));
                    OnPropertyChanged(nameof(IsActive));
                    OnPropertyChanged(nameof(IsExpired));
                }
            }
        }

        public DateTime EndDate
        {
            get => _endDate;
            set
            {
                if (_endDate != value)
                {
                    _endDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedEndDate));
                    OnPropertyChanged(nameof(IsActive));
                    OnPropertyChanged(nameof(IsExpired));
                    OnPropertyChanged(nameof(DaysRemaining));
                }
            }
        }

        public string ApplicableProducts
        {
            get => _applicableProducts;
            set
            {
                if (_applicableProducts != value)
                {
                    _applicableProducts = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasProductRestrictions));
                }
            }
        }

        public string ApplicableCategories
        {
            get => _applicableCategories;
            set
            {
                if (_applicableCategories != value)
                {
                    _applicableCategories = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasCategoryRestrictions));
                }
            }
        }

        public string ExcludedProducts
        {
            get => _excludedProducts;
            set
            {
                if (_excludedProducts != value)
                {
                    _excludedProducts = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ExcludedCategories
        {
            get => _excludedCategories;
            set
            {
                if (_excludedCategories != value)
                {
                    _excludedCategories = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApplicableCustomers
        {
            get => _applicableCustomers;
            set
            {
                if (_applicableCustomers != value)
                {
                    _applicableCustomers = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasCustomerRestrictions));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<CouponUsage> UsageHistory
        {
            get => _usageHistory;
            set
            {
                if (_usageHistory != value)
                {
                    _usageHistory = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsActive => Status == CouponStatus.Active && !IsExpired && !IsUsageLimitReached;
        public bool IsExpired => DateTime.Now < StartDate || DateTime.Now > EndDate;
        public bool IsUsageLimitReached => UsageLimit > 0 && UsageCount >= UsageLimit;
        public bool HasProductRestrictions => !string.IsNullOrEmpty(ApplicableProducts);
        public bool HasCategoryRestrictions => !string.IsNullOrEmpty(ApplicableCategories);
        public bool HasCustomerRestrictions => !string.IsNullOrEmpty(ApplicableCustomers);
        public int RemainingUses => UsageLimit > 0 ? Math.Max(0, UsageLimit - UsageCount) : int.MaxValue;

        public int DaysRemaining
        {
            get
            {
                if (IsExpired) return 0;
                return (int)(EndDate - DateTime.Now).TotalDays;
            }
        }

        // Display Properties
        public string TypeDisplay
        {
            get
            {
                return Type switch
                {
                    CouponType.Percentage => "نسبة مئوية",
                    CouponType.FixedAmount => "مبلغ ثابت",
                    CouponType.FreeShipping => "شحن مجاني",
                    CouponType.BuyXGetY => "اشتري X واحصل على Y",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    CouponStatus.Active => "نشط",
                    CouponStatus.Inactive => "غير نشط",
                    CouponStatus.Expired => "منتهي الصلاحية",
                    CouponStatus.Used => "مستخدم",
                    _ => "غير محدد"
                };
            }
        }

        public string TypeIcon
        {
            get
            {
                return Type switch
                {
                    CouponType.Percentage => "Percent",
                    CouponType.FixedAmount => "CurrencyUsd",
                    CouponType.FreeShipping => "TruckDelivery",
                    CouponType.BuyXGetY => "Gift",
                    _ => "Tag"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                if (IsExpired) return "Red";
                if (IsUsageLimitReached) return "Orange";
                return Status switch
                {
                    CouponStatus.Active => "Green",
                    CouponStatus.Inactive => "Gray",
                    CouponStatus.Expired => "Red",
                    CouponStatus.Used => "Blue",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedValue
        {
            get
            {
                return Type switch
                {
                    CouponType.Percentage => $"{Value:F1}%",
                    CouponType.FixedAmount => Value.ToString("C", new System.Globalization.CultureInfo("ar-SA")),
                    CouponType.FreeShipping => "شحن مجاني",
                    CouponType.BuyXGetY => $"اشتري {Value} واحصل على 1 مجاناً",
                    _ => Value.ToString()
                };
            }
        }

        public string FormattedMinimumAmount => MinimumAmount > 0 ? MinimumAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA")) : "لا يوجد حد أدنى";
        public string FormattedMaximumDiscount => MaximumDiscount > 0 ? MaximumDiscount.ToString("C", new System.Globalization.CultureInfo("ar-SA")) : "غير محدود";
        public string FormattedStartDate => StartDate.ToString("dd/MM/yyyy");
        public string FormattedEndDate => EndDate.ToString("dd/MM/yyyy");
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        public bool IsValidForOrder(decimal orderAmount, int? customerId = null, string[] productIds = null, string[] categoryIds = null)
        {
            // Check if coupon is active
            if (!IsActive) return false;

            // Check minimum amount
            if (MinimumAmount > 0 && orderAmount < MinimumAmount) return false;

            // Check customer restrictions
            if (HasCustomerRestrictions && customerId.HasValue)
            {
                var applicableCustomerIds = ApplicableCustomers.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.TryParse(id.Trim(), out var result) ? result : 0)
                    .Where(id => id > 0);
                
                if (!applicableCustomerIds.Contains(customerId.Value)) return false;
            }

            // Check product restrictions
            if (HasProductRestrictions && productIds != null)
            {
                var applicableProductIds = ApplicableProducts.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => id.Trim());
                
                if (!productIds.Any(pid => applicableProductIds.Contains(pid))) return false;
            }

            // Check category restrictions
            if (HasCategoryRestrictions && categoryIds != null)
            {
                var applicableCategoryIds = ApplicableCategories.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => id.Trim());
                
                if (!categoryIds.Any(cid => applicableCategoryIds.Contains(cid))) return false;
            }

            return true;
        }

        public decimal CalculateDiscount(decimal orderAmount)
        {
            if (!IsActive) return 0;

            decimal discount = Type switch
            {
                CouponType.Percentage => orderAmount * (Value / 100),
                CouponType.FixedAmount => Value,
                CouponType.FreeShipping => 0, // Handled separately
                CouponType.BuyXGetY => 0, // Handled separately
                _ => 0
            };

            // Apply maximum discount limit
            if (MaximumDiscount > 0 && discount > MaximumDiscount)
                discount = MaximumDiscount;

            return discount;
        }

        public void Use(int? customerId = null, int? orderId = null, decimal discountAmount = 0)
        {
            UsageCount++;
            
            UsageHistory.Add(new CouponUsage
            {
                CouponId = Id,
                CustomerId = customerId,
                OrderId = orderId,
                DiscountAmount = discountAmount,
                UsedAt = DateTime.Now
            });

            if (UsageLimit > 0 && UsageCount >= UsageLimit)
                Status = CouponStatus.Used;

            UpdatedAt = DateTime.Now;
        }

        public void Activate()
        {
            Status = CouponStatus.Active;
            UpdatedAt = DateTime.Now;
        }

        public void Deactivate()
        {
            Status = CouponStatus.Inactive;
            UpdatedAt = DateTime.Now;
        }

        public void Expire()
        {
            Status = CouponStatus.Expired;
            UpdatedAt = DateTime.Now;
        }

        public static string GenerateCode(int length = 8)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    /// <summary>
    /// سجل استخدام الكوبون
    /// </summary>
    public class CouponUsage
    {
        public int Id { get; set; }
        public int CouponId { get; set; }
        public int? CustomerId { get; set; }
        public int? OrderId { get; set; }
        public decimal DiscountAmount { get; set; }
        public DateTime UsedAt { get; set; } = DateTime.Now;
        public string CustomerName { get; set; } = string.Empty;
        public string OrderNumber { get; set; } = string.Empty;

        public string FormattedDiscountAmount => DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedUsedAt => UsedAt.ToString("dd/MM/yyyy HH:mm");
    }

    #region Enums

    public enum CouponType
    {
        Percentage,     // نسبة مئوية
        FixedAmount,    // مبلغ ثابت
        FreeShipping,   // شحن مجاني
        BuyXGetY        // اشتري X واحصل على Y
    }

    public enum CouponStatus
    {
        Active,         // نشط
        Inactive,       // غير نشط
        Expired,        // منتهي الصلاحية
        Used            // مستخدم
    }

    #endregion

    #region Validation

    public class CouponValidator : AbstractValidator<Coupon>
    {
        public CouponValidator()
        {
            RuleFor(c => c.Code)
                .NotEmpty().WithMessage("كود الكوبون مطلوب")
                .MaximumLength(20).WithMessage("كود الكوبون لا يمكن أن يتجاوز 20 حرف")
                .Matches("^[A-Z0-9]+$").WithMessage("كود الكوبون يجب أن يحتوي على أحرف إنجليزية كبيرة وأرقام فقط");

            RuleFor(c => c.Name)
                .NotEmpty().WithMessage("اسم الكوبون مطلوب")
                .MaximumLength(100).WithMessage("اسم الكوبون لا يمكن أن يتجاوز 100 حرف");

            RuleFor(c => c.Value)
                .GreaterThan(0).WithMessage("قيمة الكوبون يجب أن تكون أكبر من صفر");

            RuleFor(c => c.Value)
                .LessThanOrEqualTo(100).When(c => c.Type == CouponType.Percentage)
                .WithMessage("نسبة الخصم لا يمكن أن تتجاوز 100%");

            RuleFor(c => c.StartDate)
                .LessThan(c => c.EndDate).WithMessage("تاريخ البداية يجب أن يكون قبل تاريخ النهاية");

            RuleFor(c => c.UsageLimit)
                .GreaterThan(0).When(c => c.UsageLimit > 0)
                .WithMessage("حد الاستخدام يجب أن يكون أكبر من صفر");

            RuleFor(c => c.UsageLimitPerCustomer)
                .GreaterThan(0).WithMessage("حد الاستخدام لكل عميل يجب أن يكون أكبر من صفر");

            RuleFor(c => c.MinimumAmount)
                .GreaterThanOrEqualTo(0).WithMessage("الحد الأدنى للطلب لا يمكن أن يكون سالب");

            RuleFor(c => c.MaximumDiscount)
                .GreaterThanOrEqualTo(0).WithMessage("الحد الأقصى للخصم لا يمكن أن يكون سالب");
        }
    }

    #endregion
}
