using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة التجارة الإلكترونية المتكاملة
    /// </summary>
    public class ECommerceService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly InventoryService _inventoryService;

        public ECommerceService(DatabaseService dbService, NotificationService notificationService, InventoryService inventoryService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _inventoryService = inventoryService ?? throw new ArgumentNullException(nameof(inventoryService));
        }

        #region Store Management

        /// <summary>
        /// إنشاء متجر إلكتروني جديد
        /// </summary>
        public async Task<int> CreateStoreAsync(ECommerceStore store)
        {
            try
            {
                // توليد كود المتجر إذا لم يكن موجود
                if (string.IsNullOrEmpty(store.StoreCode))
                {
                    store.StoreCode = await GenerateStoreCodeAsync();
                }

                // توليد النطاق إذا لم يكن موجود
                if (string.IsNullOrEmpty(store.Domain))
                {
                    store.Domain = GenerateDomainFromName(store.StoreName);
                }

                const string sql = @"
                    INSERT INTO ECommerceStores (
                        StoreName, StoreCode, Description, Logo, Banner, Status, StoreType,
                        Domain, CustomDomain, Theme, PrimaryColor, SecondaryColor, Currency,
                        Language, Timezone, IsMultiLanguage, IsMultiCurrency, AllowGuestCheckout,
                        RequireEmailVerification, EnableWishlist, EnableReviews, EnableInventoryTracking,
                        EnableCoupons, EnableTaxCalculation, TaxRate, TaxNumber, ContactEmail,
                        ContactPhone, SupportEmail, Address, City, Country, PostalCode,
                        SocialMediaLinks, SeoTitle, SeoDescription, SeoKeywords, AnalyticsCode,
                        PixelCode, CreatedBy, CreatedAt
                    ) VALUES (
                        @StoreName, @StoreCode, @Description, @Logo, @Banner, @Status, @StoreType,
                        @Domain, @CustomDomain, @Theme, @PrimaryColor, @SecondaryColor, @Currency,
                        @Language, @Timezone, @IsMultiLanguage, @IsMultiCurrency, @AllowGuestCheckout,
                        @RequireEmailVerification, @EnableWishlist, @EnableReviews, @EnableInventoryTracking,
                        @EnableCoupons, @EnableTaxCalculation, @TaxRate, @TaxNumber, @ContactEmail,
                        @ContactPhone, @SupportEmail, @Address, @City, @Country, @PostalCode,
                        @SocialMediaLinks, @SeoTitle, @SeoDescription, @SeoKeywords, @AnalyticsCode,
                        @PixelCode, @CreatedBy, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    store.StoreName,
                    store.StoreCode,
                    store.Description,
                    store.Logo,
                    store.Banner,
                    Status = store.Status.ToString(),
                    StoreType = store.StoreType.ToString(),
                    store.Domain,
                    store.CustomDomain,
                    store.Theme,
                    store.PrimaryColor,
                    store.SecondaryColor,
                    store.Currency,
                    store.Language,
                    store.Timezone,
                    store.IsMultiLanguage,
                    store.IsMultiCurrency,
                    store.AllowGuestCheckout,
                    store.RequireEmailVerification,
                    store.EnableWishlist,
                    store.EnableReviews,
                    store.EnableInventoryTracking,
                    store.EnableCoupons,
                    store.EnableTaxCalculation,
                    store.TaxRate,
                    store.TaxNumber,
                    store.ContactEmail,
                    store.ContactPhone,
                    store.SupportEmail,
                    store.Address,
                    store.City,
                    store.Country,
                    store.PostalCode,
                    store.SocialMediaLinks,
                    store.SeoTitle,
                    store.SeoDescription,
                    store.SeoKeywords,
                    store.AnalyticsCode,
                    store.PixelCode,
                    store.CreatedBy,
                    CreatedAt = store.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var storeId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                store.Id = storeId;

                // إرسال إشعار
                await SendStoreNotificationAsync(store, "تم إنشاء متجر إلكتروني جديد");

                LoggingService.LogInfo($"تم إنشاء متجر إلكتروني جديد: {store.StoreName}");
                return storeId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء المتجر الإلكتروني: {store?.StoreName}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على متجر بالمعرف
        /// </summary>
        public async Task<ECommerceStore?> GetStoreByIdAsync(int storeId)
        {
            try
            {
                const string sql = "SELECT * FROM ECommerceStores WHERE Id = @Id";
                var storeData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = storeId });

                if (storeData != null)
                {
                    return MapToECommerceStore(storeData);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على المتجر: {storeId}");
                throw;
            }
        }

        #endregion

        #region Product Management

        /// <summary>
        /// إضافة منتج للمتجر الإلكتروني
        /// </summary>
        public async Task<int> AddProductToStoreAsync(OnlineProduct product)
        {
            try
            {
                // توليد SKU إذا لم يكن موجود
                if (string.IsNullOrEmpty(product.Sku))
                {
                    product.Sku = await GenerateSkuAsync(product.StoreId);
                }

                // توليد Slug إذا لم يكن موجود
                if (string.IsNullOrEmpty(product.Slug))
                {
                    product.Slug = GenerateSlugFromName(product.Name);
                }

                const string sql = @"
                    INSERT INTO OnlineProducts (
                        StoreId, ProductId, Sku, Name, Slug, ShortDescription, FullDescription,
                        Price, ComparePrice, CostPrice, TrackQuantity, StockQuantity, LowStockThreshold,
                        AllowBackorders, Status, Visibility, IsFeatured, IsDigital, Weight, Dimensions,
                        ShippingClass, MetaTitle, MetaDescription, MetaKeywords, Tags, SortOrder,
                        PublishDate, SaleStartDate, SaleEndDate, SalePrice, CreatedAt
                    ) VALUES (
                        @StoreId, @ProductId, @Sku, @Name, @Slug, @ShortDescription, @FullDescription,
                        @Price, @ComparePrice, @CostPrice, @TrackQuantity, @StockQuantity, @LowStockThreshold,
                        @AllowBackorders, @Status, @Visibility, @IsFeatured, @IsDigital, @Weight, @Dimensions,
                        @ShippingClass, @MetaTitle, @MetaDescription, @MetaKeywords, @Tags, @SortOrder,
                        @PublishDate, @SaleStartDate, @SaleEndDate, @SalePrice, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    product.StoreId,
                    product.ProductId,
                    product.Sku,
                    product.Name,
                    product.Slug,
                    product.ShortDescription,
                    product.FullDescription,
                    product.Price,
                    product.ComparePrice,
                    product.CostPrice,
                    product.TrackQuantity,
                    product.StockQuantity,
                    product.LowStockThreshold,
                    product.AllowBackorders,
                    Status = product.Status.ToString(),
                    Visibility = product.Visibility.ToString(),
                    product.IsFeatured,
                    product.IsDigital,
                    product.Weight,
                    product.Dimensions,
                    product.ShippingClass,
                    product.MetaTitle,
                    product.MetaDescription,
                    product.MetaKeywords,
                    product.Tags,
                    product.SortOrder,
                    PublishDate = product.PublishDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    SaleStartDate = product.SaleStartDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    SaleEndDate = product.SaleEndDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    product.SalePrice,
                    CreatedAt = product.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var productId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                product.Id = productId;

                LoggingService.LogInfo($"تم إضافة منتج للمتجر الإلكتروني: {product.Name}");
                return productId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة المنتج للمتجر: {product?.Name}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على منتجات المتجر
        /// </summary>
        public async Task<IEnumerable<OnlineProduct>> GetStoreProductsAsync(int storeId, ProductStatus? status = null, bool? isFeatured = null)
        {
            try
            {
                var sql = "SELECT * FROM OnlineProducts WHERE StoreId = @StoreId";
                var parameters = new Dictionary<string, object> { ["StoreId"] = storeId };

                if (status.HasValue)
                {
                    sql += " AND Status = @Status";
                    parameters["Status"] = status.ToString();
                }

                if (isFeatured.HasValue)
                {
                    sql += " AND IsFeatured = @IsFeatured";
                    parameters["IsFeatured"] = isFeatured.Value;
                }

                sql += " ORDER BY SortOrder, CreatedAt DESC";

                var productsData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return productsData.Select(MapToOnlineProduct);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على منتجات المتجر: {storeId}");
                throw;
            }
        }

        #endregion

        #region Shopping Cart Management

        /// <summary>
        /// إنشاء سلة تسوق جديدة
        /// </summary>
        public async Task<int> CreateShoppingCartAsync(ShoppingCart cart)
        {
            try
            {
                // توليد معرف الجلسة إذا لم يكن موجود
                if (string.IsNullOrEmpty(cart.SessionId))
                {
                    cart.SessionId = Guid.NewGuid().ToString();
                }

                // تعيين تاريخ انتهاء الصلاحية (24 ساعة)
                cart.SetExpiry(TimeSpan.FromHours(24));

                const string sql = @"
                    INSERT INTO ShoppingCarts (
                        SessionId, CustomerId, StoreId, Currency, Subtotal, TaxAmount,
                        ShippingAmount, DiscountAmount, TotalAmount, CouponCode, CouponDiscount,
                        ShippingMethod, ShippingCost, Notes, CreatedAt, UpdatedAt, ExpiresAt
                    ) VALUES (
                        @SessionId, @CustomerId, @StoreId, @Currency, @Subtotal, @TaxAmount,
                        @ShippingAmount, @DiscountAmount, @TotalAmount, @CouponCode, @CouponDiscount,
                        @ShippingMethod, @ShippingCost, @Notes, @CreatedAt, @UpdatedAt, @ExpiresAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    cart.SessionId,
                    cart.CustomerId,
                    cart.StoreId,
                    cart.Currency,
                    cart.Subtotal,
                    cart.TaxAmount,
                    cart.ShippingAmount,
                    cart.DiscountAmount,
                    cart.TotalAmount,
                    cart.CouponCode,
                    cart.CouponDiscount,
                    cart.ShippingMethod,
                    cart.ShippingCost,
                    cart.Notes,
                    CreatedAt = cart.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    UpdatedAt = cart.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    ExpiresAt = cart.ExpiresAt?.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var cartId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                cart.Id = cartId;

                LoggingService.LogInfo($"تم إنشاء سلة تسوق جديدة: {cart.SessionId}");
                return cartId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء سلة التسوق: {cart?.SessionId}");
                throw;
            }
        }

        /// <summary>
        /// إضافة عنصر للسلة
        /// </summary>
        public async Task<bool> AddItemToCartAsync(int cartId, int productId, int quantity, string variantInfo = "")
        {
            try
            {
                // الحصول على معلومات المنتج
                var product = await GetOnlineProductByIdAsync(productId);
                if (product == null || !product.IsActive)
                    return false;

                // التحقق من توفر المخزون
                if (product.TrackQuantity && product.StockQuantity < quantity)
                    return false;

                // الحصول على السلة
                var cart = await GetShoppingCartByIdAsync(cartId);
                if (cart == null || cart.IsExpired)
                    return false;

                // إضافة العنصر للسلة
                cart.AddItem(productId, product.Name, product.Sku, product.CurrentPrice, quantity, variantInfo, product.MainImage);

                // حفظ عنصر السلة في قاعدة البيانات
                const string sql = @"
                    INSERT OR REPLACE INTO CartItems (
                        CartId, ProductId, ProductName, ProductSku, ProductImage,
                        UnitPrice, Quantity, Total, VariantInfo, AddedAt
                    ) VALUES (
                        @CartId, @ProductId, @ProductName, @ProductSku, @ProductImage,
                        @UnitPrice, @Quantity, @Total, @VariantInfo, @AddedAt
                    )";

                var cartItem = cart.Items.Last();
                await _dbService.ExecuteAsync(sql, new
                {
                    cartItem.CartId,
                    cartItem.ProductId,
                    cartItem.ProductName,
                    cartItem.ProductSku,
                    cartItem.ProductImage,
                    cartItem.UnitPrice,
                    cartItem.Quantity,
                    cartItem.Total,
                    cartItem.VariantInfo,
                    AddedAt = cartItem.AddedAt.ToString("yyyy-MM-dd HH:mm:ss")
                });

                // تحديث السلة
                await UpdateShoppingCartAsync(cart);

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة عنصر للسلة: {cartId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على سلة التسوق
        /// </summary>
        public async Task<ShoppingCart?> GetShoppingCartByIdAsync(int cartId)
        {
            try
            {
                const string sql = "SELECT * FROM ShoppingCarts WHERE Id = @Id";
                var cartData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = cartId });

                if (cartData != null)
                {
                    var cart = MapToShoppingCart(cartData);
                    
                    // تحميل عناصر السلة
                    cart.Items = new System.Collections.ObjectModel.ObservableCollection<CartItem>(
                        await GetCartItemsAsync(cartId));

                    return cart;
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على سلة التسوق: {cartId}");
                throw;
            }
        }

        #endregion

        #region Order Management

        /// <summary>
        /// إنشاء طلب من السلة
        /// </summary>
        public async Task<int> CreateOrderFromCartAsync(int cartId, BillingAddress billingAddress, ShippingAddress shippingAddress, string paymentMethod)
        {
            try
            {
                var cart = await GetShoppingCartByIdAsync(cartId);
                if (cart == null || cart.IsEmpty || cart.IsExpired)
                    throw new InvalidOperationException("السلة غير صالحة أو فارغة");

                // تحويل السلة إلى طلب
                var order = cart.ConvertToOrder();
                order.OrderNumber = await GenerateOrderNumberAsync();
                order.BillingAddress = billingAddress;
                order.ShippingAddress = shippingAddress;
                order.PaymentMethod = paymentMethod;

                // حفظ الطلب
                var orderId = await SaveOrderAsync(order);

                // تحديث المخزون
                foreach (var item in order.Items)
                {
                    if (order.StoreId > 0) // تحديد المخزن بناءً على المتجر
                    {
                        await _inventoryService.RemoveInventoryAsync(item.ProductId, 1, null, item.Quantity);
                    }
                }

                // مسح السلة
                await ClearShoppingCartAsync(cartId);

                // إرسال إشعار
                await SendOrderNotificationAsync(order, "تم إنشاء طلب جديد");

                LoggingService.LogInfo($"تم إنشاء طلب جديد من السلة: {order.OrderNumber}");
                return orderId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء الطلب من السلة: {cartId}");
                throw;
            }
        }

        #endregion

        #region Coupon Management

        /// <summary>
        /// تطبيق كوبون على السلة
        /// </summary>
        public async Task<bool> ApplyCouponToCartAsync(int cartId, string couponCode)
        {
            try
            {
                var cart = await GetShoppingCartByIdAsync(cartId);
                if (cart == null || cart.IsExpired)
                    return false;

                var coupon = await GetCouponByCodeAsync(couponCode);
                if (coupon == null || !coupon.IsValidForOrder(cart.Subtotal, cart.CustomerId))
                    return false;

                var discountAmount = coupon.CalculateDiscount(cart.Subtotal);
                cart.ApplyCoupon(couponCode, discountAmount);

                await UpdateShoppingCartAsync(cart);
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تطبيق الكوبون: {couponCode}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على كوبون بالكود
        /// </summary>
        public async Task<Coupon?> GetCouponByCodeAsync(string code)
        {
            try
            {
                const string sql = "SELECT * FROM Coupons WHERE Code = @Code AND Status = 'Active'";
                var couponData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Code = code.ToUpper() });

                if (couponData != null)
                {
                    return MapToCoupon(couponData);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على الكوبون: {code}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private async Task<string> GenerateStoreCodeAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM ECommerceStores";
                var count = await _dbService.QuerySingleAsync<int>(sql);
                return $"STORE{(count + 1):D4}";
            }
            catch
            {
                return $"STORE{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private async Task<string> GenerateSkuAsync(int storeId)
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM OnlineProducts WHERE StoreId = @StoreId";
                var count = await _dbService.QuerySingleAsync<int>(sql, new { StoreId = storeId });
                return $"SKU{storeId:D3}{(count + 1):D6}";
            }
            catch
            {
                return $"SKU{storeId:D3}{DateTime.Now:HHmmss}";
            }
        }

        private async Task<string> GenerateOrderNumberAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM OnlineOrders WHERE DATE(OrderDate) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);
                
                var today = DateTime.Now;
                return $"ORD-{today:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch
            {
                return $"ORD-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private string GenerateDomainFromName(string storeName)
        {
            return storeName.ToLower()
                           .Replace(" ", "-")
                           .Replace("أ", "a")
                           .Replace("ب", "b")
                           .Replace("ت", "t");
        }

        private string GenerateSlugFromName(string productName)
        {
            return productName.ToLower()
                             .Replace(" ", "-")
                             .Replace("أ", "a")
                             .Replace("ب", "b")
                             .Replace("ت", "t");
        }

        // Mapping methods would go here...
        private ECommerceStore MapToECommerceStore(dynamic data) { /* Implementation */ return new ECommerceStore(); }
        private OnlineProduct MapToOnlineProduct(dynamic data) { /* Implementation */ return new OnlineProduct(); }
        private ShoppingCart MapToShoppingCart(dynamic data) { /* Implementation */ return new ShoppingCart(); }
        private Coupon MapToCoupon(dynamic data) { /* Implementation */ return new Coupon(); }

        // Additional helper methods...
        private async Task<OnlineProduct?> GetOnlineProductByIdAsync(int productId) { /* Implementation */ return null; }
        private async Task<IEnumerable<CartItem>> GetCartItemsAsync(int cartId) { /* Implementation */ return new List<CartItem>(); }
        private async Task UpdateShoppingCartAsync(ShoppingCart cart) { /* Implementation */ }
        private async Task ClearShoppingCartAsync(int cartId) { /* Implementation */ }
        private async Task<int> SaveOrderAsync(OnlineOrder order) { /* Implementation */ return 0; }
        private async Task SendStoreNotificationAsync(ECommerceStore store, string message) { /* Implementation */ }
        private async Task SendOrderNotificationAsync(OnlineOrder order, string message) { /* Implementation */ }

        #endregion
    }
}
