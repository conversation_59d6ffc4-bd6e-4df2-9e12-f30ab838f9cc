using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem
{
    public partial class TestSearchableComboBox : Window, INotifyPropertyChanged
    {
        #region Properties

        private ObservableCollection<Customer> _sampleCustomers = new();
        public ObservableCollection<Customer> SampleCustomers
        {
            get => _sampleCustomers;
            set
            {
                _sampleCustomers = value;
                OnPropertyChanged();
            }
        }

        private ObservableCollection<Product> _sampleProducts = new();
        public ObservableCollection<Product> SampleProducts
        {
            get => _sampleProducts;
            set
            {
                _sampleProducts = value;
                OnPropertyChanged();
            }
        }

        private Customer _selectedCustomer;
        public Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                _selectedCustomer = value;
                OnPropertyChanged();
            }
        }

        private Product _selectedProduct;
        public Product SelectedProduct
        {
            get => _selectedProduct;
            set
            {
                _selectedProduct = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Constructor

        public TestSearchableComboBox()
        {
            InitializeComponent();
            DataContext = this;
            LoadSampleData();
        }

        #endregion

        #region Methods

        private void LoadSampleData()
        {
            // Sample Customers
            SampleCustomers.Clear();
            SampleCustomers.Add(new Customer { Id = 1, Code = "CUS001", Name = "أحمد محمد علي", Phone = "0501234567", Balance = 1500.50m });
            SampleCustomers.Add(new Customer { Id = 2, Code = "CUS002", Name = "فاطمة أحمد", Phone = "0507654321", Balance = 750.25m });
            SampleCustomers.Add(new Customer { Id = 3, Code = "CUS003", Name = "محمد عبدالله", Phone = "0551122334", Balance = 2200.00m });
            SampleCustomers.Add(new Customer { Id = 4, Code = "CUS004", Name = "نورا سالم", Phone = "0559988776", Balance = 0.00m });
            SampleCustomers.Add(new Customer { Id = 5, Code = "CUS005", Name = "خالد الأحمد", Phone = "0505566778", Balance = 3500.75m });
            SampleCustomers.Add(new Customer { Id = 6, Code = "CUS006", Name = "سارة محمود", Phone = "0503344556", Balance = 890.30m });
            SampleCustomers.Add(new Customer { Id = 7, Code = "CUS007", Name = "عبدالرحمن يوسف", Phone = "0556677889", Balance = 1200.00m });
            SampleCustomers.Add(new Customer { Id = 8, Code = "CUS008", Name = "مريم عبدالعزيز", Phone = "0502233445", Balance = 450.80m });

            // Sample Products
            SampleProducts.Clear();
            SampleProducts.Add(new Product { Id = 1, Code = "PRD001", Name = "لابتوب ديل", Quantity = 15, SalePrice = 2500.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 2, Code = "PRD002", Name = "ماوس لاسلكي", Quantity = 50, SalePrice = 85.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 3, Code = "PRD003", Name = "لوحة مفاتيح", Quantity = 30, SalePrice = 120.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 4, Code = "PRD004", Name = "شاشة سامسونج 24 بوصة", Quantity = 8, SalePrice = 800.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 5, Code = "PRD005", Name = "طابعة HP", Quantity = 5, SalePrice = 450.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 6, Code = "PRD006", Name = "كابل USB", Quantity = 100, SalePrice = 25.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 7, Code = "PRD007", Name = "سماعات بلوتوث", Quantity = 25, SalePrice = 180.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 8, Code = "PRD008", Name = "هارد ديسك خارجي 1TB", Quantity = 12, SalePrice = 320.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 9, Code = "PRD009", Name = "ذاكرة فلاش 32GB", Quantity = 60, SalePrice = 45.00m, Unit = "قطعة" });
            SampleProducts.Add(new Product { Id = 10, Code = "PRD010", Name = "كاميرا ويب", Quantity = 20, SalePrice = 150.00m, Unit = "قطعة" });
        }

        #endregion

        #region Event Handlers

        private void CustomerSearchBox_AddNewRequested(object sender, object e)
        {
            try
            {
                var customerDialog = new CustomerDialog();
                if (customerDialog.ShowDialog() == true && customerDialog.Result != null)
                {
                    SampleCustomers.Add(customerDialog.Result);
                    SelectedCustomer = customerDialog.Result;
                    MessageBox.Show($"تم إضافة العميل: {customerDialog.Result.Name}", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ProductSearchBox_AddNewRequested(object sender, object e)
        {
            try
            {
                var productDialog = new ProductDialog();
                if (productDialog.ShowDialog() == true && productDialog.Result != null)
                {
                    SampleProducts.Add(productDialog.Result);
                    SelectedProduct = productDialog.Result;
                    MessageBox.Show($"تم إضافة المنتج: {productDialog.Result.Name}", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            SelectedCustomer = null;
            SelectedProduct = null;
            CustomerSearchBox.ClearSelection();
            ProductSearchBox.ClearSelection();
        }

        private void TestSaleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saleDialog = new SaleDialog();
                saleDialog.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح حوار المبيعات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
