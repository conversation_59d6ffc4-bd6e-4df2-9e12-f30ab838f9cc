using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Printing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Models;
using DrawingFontStyle = System.Drawing.FontStyle;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة الطباعة - طباعة الفواتير والتقارير
    /// </summary>
    public class PrintService
    {
        private readonly SettingsService _settingsService;
        private readonly BarcodeService _barcodeService;

        public PrintService(SettingsService settingsService, BarcodeService barcodeService)
        {
            _settingsService = settingsService;
            _barcodeService = barcodeService;
        }

        /// <summary>
        /// الحصول على قائمة الطابعات المتاحة
        /// </summary>
        /// <returns>قائمة أسماء الطابعات</returns>
        public List<string> GetAvailablePrinters()
        {
            try
            {
                var printers = new List<string>();

                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    printers.Add(printerName);
                }

                return printers;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على قائمة الطابعات");
                return new List<string>();
            }
        }

        /// <summary>
        /// طباعة فاتورة مبيعات
        /// </summary>
        /// <param name="sale">فاتورة المبيعات</param>
        /// <param name="printerName">اسم الطابعة (اختياري)</param>
        /// <returns>true إذا تمت الطباعة بنجاح</returns>
        public async Task<bool> PrintSaleInvoiceAsync(Sale sale, string? printerName = null)
        {
            try
            {
                if (sale == null)
                    throw new ArgumentNullException(nameof(sale));

                // الحصول على اسم الطابعة
                printerName ??= await _settingsService.GetReceiptPrinterAsync();

                if (string.IsNullOrEmpty(printerName))
                {
                    printerName = GetDefaultPrinter();
                }

                // إنشاء مستند الطباعة
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = printerName;

                // تعيين معالج الطباعة
                printDocument.PrintPage += (sender, e) => PrintSaleInvoicePage(sender, e, sale);

                // طباعة المستند
                printDocument.Print();

                LoggingService.LogInfo($"تم طباعة فاتورة المبيعات رقم: {sale.InvoiceNumber}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في طباعة فاتورة المبيعات: {sale?.InvoiceNumber}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// طباعة فاتورة مشتريات
        /// </summary>
        /// <param name="purchase">فاتورة المشتريات</param>
        /// <param name="printerName">اسم الطابعة (اختياري)</param>
        /// <returns>true إذا تمت الطباعة بنجاح</returns>
        public async Task<bool> PrintPurchaseInvoiceAsync(Purchase purchase, string? printerName = null)
        {
            try
            {
                if (purchase == null)
                    throw new ArgumentNullException(nameof(purchase));

                // الحصول على اسم الطابعة
                printerName ??= await _settingsService.GetReceiptPrinterAsync();

                if (string.IsNullOrEmpty(printerName))
                {
                    printerName = GetDefaultPrinter();
                }

                // إنشاء مستند الطباعة
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = printerName;

                // تعيين معالج الطباعة
                printDocument.PrintPage += (sender, e) => PrintPurchaseInvoicePage(sender, e, purchase);

                // طباعة المستند
                printDocument.Print();

                LoggingService.LogInfo($"تم طباعة فاتورة المشتريات رقم: {purchase.InvoiceNumber}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في طباعة فاتورة المشتريات: {purchase?.InvoiceNumber}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// طباعة تقرير
        /// </summary>
        /// <param name="reportTitle">عنوان التقرير</param>
        /// <param name="reportContent">محتوى التقرير</param>
        /// <param name="printerName">اسم الطابعة (اختياري)</param>
        /// <returns>true إذا تمت الطباعة بنجاح</returns>
        public async Task<bool> PrintReportAsync(string reportTitle, string reportContent, string? printerName = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(reportTitle))
                    throw new ArgumentException("عنوان التقرير لا يمكن أن يكون فارغاً", nameof(reportTitle));

                // الحصول على اسم الطابعة
                printerName ??= await _settingsService.GetReceiptPrinterAsync();

                if (string.IsNullOrEmpty(printerName))
                {
                    printerName = GetDefaultPrinter();
                }

                // إنشاء مستند الطباعة
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = printerName;

                // تعيين معالج الطباعة
                printDocument.PrintPage += (sender, e) => PrintReportPage(sender, e, reportTitle, reportContent);

                // طباعة المستند
                printDocument.Print();

                LoggingService.LogInfo($"تم طباعة التقرير: {reportTitle}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في طباعة التقرير: {reportTitle}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// طباعة باركود المنتج
        /// </summary>
        /// <param name="product">المنتج</param>
        /// <param name="printerName">اسم الطابعة (اختياري)</param>
        /// <returns>true إذا تمت الطباعة بنجاح</returns>
        public async Task<bool> PrintProductBarcodeAsync(Product product, string? printerName = null)
        {
            try
            {
                if (product == null)
                    throw new ArgumentNullException(nameof(product));

                if (string.IsNullOrWhiteSpace(product.Barcode))
                    throw new ArgumentException("المنتج لا يحتوي على باركود");

                // الحصول على اسم الطابعة
                printerName ??= await _settingsService.GetReceiptPrinterAsync();

                if (string.IsNullOrEmpty(printerName))
                {
                    printerName = GetDefaultPrinter();
                }

                // إنشاء مستند الطباعة
                var printDocument = new PrintDocument();
                printDocument.PrinterSettings.PrinterName = printerName;

                // تعيين معالج الطباعة
                printDocument.PrintPage += (sender, e) => PrintProductBarcodePage(sender, e, product);

                // طباعة المستند
                printDocument.Print();

                LoggingService.LogInfo($"تم طباعة باركود المنتج: {product.Name}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في طباعة باركود المنتج: {product?.Name}");
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        /// <param name="printDocument">مستند الطباعة</param>
        public void ShowPrintPreview(PrintDocument printDocument)
        {
            try
            {
                // معاينة الطباعة ستكون متاحة في التحديث القادم
                MessageBox.Show("معاينة الطباعة ستكون متاحة في التحديث القادم", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في معاينة الطباعة");
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الحصول على الطابعة الافتراضية
        /// </summary>
        /// <returns>اسم الطابعة الافتراضية</returns>
        private string GetDefaultPrinter()
        {
            try
            {
                var printDocument = new PrintDocument();
                return printDocument.PrinterSettings.PrinterName;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// طباعة صفحة فاتورة المبيعات
        /// </summary>
        private void PrintSaleInvoicePage(object sender, PrintPageEventArgs e, Sale sale)
        {
            try
            {
                var graphics = e.Graphics;
                var font = new Font(FontFamily.GenericSansSerif, 12);
                var titleFont = new Font(FontFamily.GenericSansSerif, 16, DrawingFontStyle.Bold);
                var headerFont = new Font(FontFamily.GenericSansSerif, 14, DrawingFontStyle.Bold);

                float yPosition = 50;
                float leftMargin = 50;
                float rightMargin = e.PageBounds.Width - 50;

                // عنوان الفاتورة
                var title = "فاتورة مبيعات";
                var titleSize = graphics.MeasureString(title, titleFont);
                graphics.DrawString(title, titleFont, System.Drawing.Brushes.Black,
                    (e.PageBounds.Width - titleSize.Width) / 2, yPosition);
                yPosition += titleSize.Height + 20;

                // معلومات الفاتورة
                graphics.DrawString($"رقم الفاتورة: {sale.InvoiceNumber}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                graphics.DrawString($"التاريخ: {sale.Date:dd/MM/yyyy}", font, System.Drawing.Brushes.Black, rightMargin - 200, yPosition);
                yPosition += 30;

                graphics.DrawString($"العميل: {sale.CustomerName}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                yPosition += 30;

                // خط فاصل
                graphics.DrawLine(System.Drawing.Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 20;

                // رؤوس الأعمدة
                graphics.DrawString("المنتج", headerFont, System.Drawing.Brushes.Black, leftMargin, yPosition);
                graphics.DrawString("الكمية", headerFont, System.Drawing.Brushes.Black, leftMargin + 200, yPosition);
                graphics.DrawString("السعر", headerFont, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                graphics.DrawString("المجموع", headerFont, System.Drawing.Brushes.Black, leftMargin + 400, yPosition);
                yPosition += 30;

                // خط فاصل
                graphics.DrawLine(System.Drawing.Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 10;

                // أصناف الفاتورة
                foreach (var item in sale.Items)
                {
                    graphics.DrawString(item.ProductName, font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                    graphics.DrawString(item.Quantity.ToString(), font, System.Drawing.Brushes.Black, leftMargin + 200, yPosition);
                    graphics.DrawString(item.UnitPrice.ToString("F2"), font, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                    graphics.DrawString(item.Total.ToString("F2"), font, System.Drawing.Brushes.Black, leftMargin + 400, yPosition);
                    yPosition += 25;
                }

                // خط فاصل
                yPosition += 10;
                graphics.DrawLine(System.Drawing.Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 20;

                // المجاميع
                graphics.DrawString($"المجموع الفرعي: {sale.Subtotal:F2}", font, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                yPosition += 25;
                graphics.DrawString($"الضريبة: {sale.Tax:F2}", font, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                yPosition += 25;
                graphics.DrawString($"الخصم: {sale.Discount:F2}", font, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                yPosition += 25;
                graphics.DrawString($"المجموع الكلي: {sale.Total:F2}", titleFont, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة صفحة فاتورة المبيعات");
            }
        }

        /// <summary>
        /// طباعة صفحة فاتورة المشتريات
        /// </summary>
        private void PrintPurchaseInvoicePage(object sender, PrintPageEventArgs e, Purchase purchase)
        {
            try
            {
                var graphics = e.Graphics;
                var font = new Font(FontFamily.GenericSansSerif, 12);
                var titleFont = new Font(FontFamily.GenericSansSerif, 16, DrawingFontStyle.Bold);
                var headerFont = new Font(FontFamily.GenericSansSerif, 14, DrawingFontStyle.Bold);

                float yPosition = 50;
                float leftMargin = 50;
                float rightMargin = e.PageBounds.Width - 50;

                // عنوان الفاتورة
                var title = "فاتورة مشتريات";
                var titleSize = graphics.MeasureString(title, titleFont);
                graphics.DrawString(title, titleFont, System.Drawing.Brushes.Black,
                    (e.PageBounds.Width - titleSize.Width) / 2, yPosition);
                yPosition += titleSize.Height + 20;

                // معلومات الفاتورة
                graphics.DrawString($"رقم الفاتورة: {purchase.InvoiceNumber}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                graphics.DrawString($"التاريخ: {purchase.Date:dd/MM/yyyy}", font, System.Drawing.Brushes.Black, rightMargin - 200, yPosition);
                yPosition += 30;

                graphics.DrawString($"المورد: {purchase.SupplierName}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                yPosition += 30;

                // خط فاصل
                graphics.DrawLine(System.Drawing.Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 20;

                // رؤوس الأعمدة
                graphics.DrawString("المنتج", headerFont, System.Drawing.Brushes.Black, leftMargin, yPosition);
                graphics.DrawString("الكمية", headerFont, System.Drawing.Brushes.Black, leftMargin + 200, yPosition);
                graphics.DrawString("السعر", headerFont, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                graphics.DrawString("المجموع", headerFont, System.Drawing.Brushes.Black, leftMargin + 400, yPosition);
                yPosition += 30;

                // خط فاصل
                graphics.DrawLine(System.Drawing.Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 10;

                // أصناف الفاتورة
                foreach (var item in purchase.Items)
                {
                    graphics.DrawString(item.ProductName, font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                    graphics.DrawString(item.Quantity.ToString(), font, System.Drawing.Brushes.Black, leftMargin + 200, yPosition);
                    graphics.DrawString(item.UnitPrice.ToString("F2"), font, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
                    graphics.DrawString(item.Total.ToString("F2"), font, System.Drawing.Brushes.Black, leftMargin + 400, yPosition);
                    yPosition += 25;
                }

                // خط فاصل
                yPosition += 10;
                graphics.DrawLine(System.Drawing.Pens.Black, leftMargin, yPosition, rightMargin, yPosition);
                yPosition += 20;

                // المجموع الكلي
                graphics.DrawString($"المجموع الكلي: {purchase.Total:F2}", titleFont, System.Drawing.Brushes.Black, leftMargin + 300, yPosition);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة صفحة فاتورة المشتريات");
            }
        }

        /// <summary>
        /// طباعة صفحة التقرير
        /// </summary>
        private void PrintReportPage(object sender, PrintPageEventArgs e, string reportTitle, string reportContent)
        {
            try
            {
                var graphics = e.Graphics;
                var font = new Font(FontFamily.GenericSansSerif, 12);
                var titleFont = new Font(FontFamily.GenericSansSerif, 16, DrawingFontStyle.Bold);

                float yPosition = 50;
                float leftMargin = 50;

                // عنوان التقرير
                var titleSize = graphics.MeasureString(reportTitle, titleFont);
                graphics.DrawString(reportTitle, titleFont, System.Drawing.Brushes.Black,
                    (e.PageBounds.Width - titleSize.Width) / 2, yPosition);
                yPosition += titleSize.Height + 30;

                // تاريخ التقرير
                graphics.DrawString($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                yPosition += 40;

                // محتوى التقرير
                var lines = reportContent.Split('\n');
                foreach (var line in lines)
                {
                    graphics.DrawString(line, font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                    yPosition += 20;

                    // التحقق من نهاية الصفحة
                    if (yPosition > e.PageBounds.Height - 100)
                    {
                        e.HasMorePages = true;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة صفحة التقرير");
            }
        }

        /// <summary>
        /// طباعة صفحة باركود المنتج
        /// </summary>
        private void PrintProductBarcodePage(object sender, PrintPageEventArgs e, Product product)
        {
            try
            {
                var graphics = e.Graphics;
                var font = new Font(FontFamily.GenericSansSerif, 12);
                var titleFont = new Font(FontFamily.GenericSansSerif, 16, DrawingFontStyle.Bold);

                float yPosition = 50;
                float leftMargin = 50;

                // اسم المنتج
                var productNameSize = graphics.MeasureString(product.Name, titleFont);
                graphics.DrawString(product.Name, titleFont, System.Drawing.Brushes.Black,
                    (e.PageBounds.Width - productNameSize.Width) / 2, yPosition);
                yPosition += productNameSize.Height + 20;

                // كود المنتج
                graphics.DrawString($"كود المنتج: {product.Code}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                yPosition += 30;

                // السعر
                graphics.DrawString($"السعر: {product.SalePrice:F2}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                yPosition += 50;

                // الباركود (نص)
                graphics.DrawString($"الباركود: {product.Barcode}", font, System.Drawing.Brushes.Black, leftMargin, yPosition);
                yPosition += 30;

                // هنا يمكن إضافة صورة الباركود عند توفر التحويل المناسب
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في طباعة صفحة باركود المنتج");
            }
        }
    }
}
