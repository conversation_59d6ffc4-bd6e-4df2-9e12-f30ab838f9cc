<Window x:Class="SalesManagementSystem.Views.Dialogs.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:SalesManagementSystem.ViewModels"
        Title="إعدادات النظام"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200"/>
            <ColumnDefinition Width="16"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Grid.ColumnSpan="3" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="Settings"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="إعدادات النظام"
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Categories List -->
        <GroupBox Grid.Row="1" Grid.Column="0" Header="الفئات"
                 Style="{DynamicResource MaterialDesignCardGroupBox}">
            <ListBox ItemsSource="{Binding Categories}"
                    SelectedItem="{Binding SelectedCategory}"
                    Style="{DynamicResource MaterialDesignListBox}">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal" Margin="8">
                            <materialDesign:PackIcon Kind="{Binding Icon}"
                                                   Width="20" Height="20"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </GroupBox>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" Grid.Column="2" VerticalScrollBarVisibility="Auto">
            <ContentControl Content="{Binding SelectedCategory}">
                <ContentControl.Resources>

                    <!-- General Settings -->
                    <DataTemplate DataType="{x:Type local:GeneralSettingsCategory}">
                        <StackPanel>
                            <GroupBox Header="إعدادات عامة" Style="{DynamicResource MaterialDesignCardGroupBox}" Margin="0,0,0,16">
                                <StackPanel Margin="16">
                                    <TextBox Style="{StaticResource FormTextBox}"
                                            materialDesign:HintAssist.Hint="اسم الشركة"
                                            Text="{Binding CompanyName, UpdateSourceTrigger=PropertyChanged}"/>

                                    <TextBox Style="{StaticResource FormTextBox}"
                                            materialDesign:HintAssist.Hint="عنوان الشركة"
                                            Text="{Binding CompanyAddress, UpdateSourceTrigger=PropertyChanged}"/>

                                    <TextBox Style="{StaticResource FormTextBox}"
                                            materialDesign:HintAssist.Hint="هاتف الشركة"
                                            Text="{Binding CompanyPhone, UpdateSourceTrigger=PropertyChanged}"/>

                                    <TextBox Style="{StaticResource FormTextBox}"
                                            materialDesign:HintAssist.Hint="الرقم الضريبي"
                                            Text="{Binding TaxNumber, UpdateSourceTrigger=PropertyChanged}"/>
                                </StackPanel>
                            </GroupBox>

                            <GroupBox Header="إعدادات النظام" Style="{DynamicResource MaterialDesignCardGroupBox}">
                                <StackPanel Margin="16">
                                    <ComboBox Style="{StaticResource FormComboBox}"
                                             materialDesign:HintAssist.Hint="اللغة"
                                             ItemsSource="{Binding Languages}"
                                             SelectedItem="{Binding SelectedLanguage}"/>

                                    <ComboBox Style="{StaticResource FormComboBox}"
                                             materialDesign:HintAssist.Hint="المظهر"
                                             ItemsSource="{Binding Themes}"
                                             SelectedItem="{Binding SelectedTheme}"/>

                                    <CheckBox Content="تشغيل مع بداية النظام"
                                             IsChecked="{Binding StartWithWindows}"
                                             Margin="0,8"
                                             Style="{DynamicResource MaterialDesignCheckBox}"/>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </DataTemplate>

                    <!-- Financial Settings -->
                    <DataTemplate DataType="{x:Type local:FinancialSettingsCategory}">
                        <GroupBox Header="الإعدادات المالية" Style="{DynamicResource MaterialDesignCardGroupBox}">
                            <StackPanel Margin="16">
                                <ComboBox Style="{StaticResource FormComboBox}"
                                         materialDesign:HintAssist.Hint="العملة الافتراضية"
                                         ItemsSource="{Binding Currencies}"
                                         SelectedItem="{Binding DefaultCurrency}"/>

                                <TextBox Style="{StaticResource FormTextBox}"
                                        materialDesign:HintAssist.Hint="معدل الضريبة (%)"
                                        Text="{Binding TaxRate, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                                <CheckBox Content="حساب الضريبة تلقائياً"
                                         IsChecked="{Binding AutoCalculateTax}"
                                         Margin="0,8"
                                         Style="{DynamicResource MaterialDesignCheckBox}"/>

                                <CheckBox Content="السماح بالمخزون السالب"
                                         IsChecked="{Binding AllowNegativeStock}"
                                         Margin="0,8"
                                         Style="{DynamicResource MaterialDesignCheckBox}"/>
                            </StackPanel>
                        </GroupBox>
                    </DataTemplate>

                    <!-- Inventory Settings -->
                    <DataTemplate DataType="{x:Type local:InventorySettingsCategory}">
                        <GroupBox Header="إعدادات المخزون" Style="{DynamicResource MaterialDesignCardGroupBox}">
                            <StackPanel Margin="16">
                                <TextBox Style="{StaticResource FormTextBox}"
                                        materialDesign:HintAssist.Hint="الحد الأدنى للمخزون"
                                        Text="{Binding LowStockThreshold, UpdateSourceTrigger=PropertyChanged}"/>

                                <CheckBox Content="إظهار تنبيهات المخزون المنخفض"
                                         IsChecked="{Binding ShowLowStockAlerts}"
                                         Margin="0,8"
                                         Style="{DynamicResource MaterialDesignCheckBox}"/>

                                <CheckBox Content="تتبع المخزون تلقائياً"
                                         IsChecked="{Binding AutoTrackStock}"
                                         Margin="0,8"
                                         Style="{DynamicResource MaterialDesignCheckBox}"/>
                            </StackPanel>
                        </GroupBox>
                    </DataTemplate>

                    <!-- Backup Settings -->
                    <DataTemplate DataType="{x:Type local:BackupSettingsCategory}">
                        <StackPanel>
                            <GroupBox Header="إعدادات النسخ الاحتياطي" Style="{DynamicResource MaterialDesignCardGroupBox}" Margin="0,0,0,16">
                                <StackPanel Margin="16">
                                    <CheckBox Content="تفعيل النسخ الاحتياطي التلقائي"
                                             IsChecked="{Binding AutoBackupEnabled}"
                                             Margin="0,8"
                                             Style="{DynamicResource MaterialDesignCheckBox}"/>

                                    <ComboBox Style="{StaticResource FormComboBox}"
                                             materialDesign:HintAssist.Hint="تكرار النسخ الاحتياطي"
                                             ItemsSource="{Binding BackupIntervals}"
                                             SelectedItem="{Binding BackupInterval}"
                                             IsEnabled="{Binding AutoBackupEnabled}"/>

                                    <TextBox Style="{StaticResource FormTextBox}"
                                            materialDesign:HintAssist.Hint="مجلد النسخ الاحتياطي"
                                            Text="{Binding BackupPath, UpdateSourceTrigger=PropertyChanged}"/>
                                </StackPanel>
                            </GroupBox>

                            <GroupBox Header="إجراءات النسخ الاحتياطي" Style="{DynamicResource MaterialDesignCardGroupBox}">
                                <StackPanel Margin="16">
                                    <Button Command="{Binding CreateBackupCommand}"
                                           Style="{DynamicResource MaterialDesignRaisedButton}"
                                           Margin="0,8"
                                           HorizontalAlignment="Left">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Backup"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>
                                            <TextBlock Text="إنشاء نسخة احتياطية الآن" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Command="{Binding RestoreBackupCommand}"
                                           Style="{DynamicResource MaterialDesignOutlinedButton}"
                                           Margin="0,8"
                                           HorizontalAlignment="Left">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Restore"
                                                                   VerticalAlignment="Center"
                                                                   Margin="0,0,8,0"/>
                                            <TextBlock Text="استعادة نسخة احتياطية" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </DataTemplate>

                    <!-- Printing Settings -->
                    <DataTemplate DataType="{x:Type local:PrintingSettingsCategory}">
                        <GroupBox Header="إعدادات الطباعة" Style="{DynamicResource MaterialDesignCardGroupBox}">
                            <StackPanel Margin="16">
                                <ComboBox Style="{StaticResource FormComboBox}"
                                         materialDesign:HintAssist.Hint="الطابعة الافتراضية"
                                         ItemsSource="{Binding Printers}"
                                         SelectedItem="{Binding DefaultPrinter}"/>

                                <ComboBox Style="{StaticResource FormComboBox}"
                                         materialDesign:HintAssist.Hint="حجم الورق"
                                         ItemsSource="{Binding PaperSizes}"
                                         SelectedItem="{Binding PaperSize}"/>

                                <CheckBox Content="طباعة الفواتير تلقائياً"
                                         IsChecked="{Binding AutoPrintInvoices}"
                                         Margin="0,8"
                                         Style="{DynamicResource MaterialDesignCheckBox}"/>

                                <CheckBox Content="إظهار معاينة الطباعة"
                                         IsChecked="{Binding ShowPrintPreview}"
                                         Margin="0,8"
                                         Style="{DynamicResource MaterialDesignCheckBox}"/>
                            </StackPanel>
                        </GroupBox>
                    </DataTemplate>

                </ContentControl.Resources>
            </ContentControl>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" Grid.ColumnSpan="3"
                   Orientation="Horizontal"
                   HorizontalAlignment="Left"
                   Margin="0,24,0,0">

            <Button Command="{Binding SaveCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="100"
                   Margin="0,0,12,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ContentSave"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="حفظ" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button Command="{Binding ResetCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="120"
                   Margin="0,0,12,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Restore"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="إعادة تعيين" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>

            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="100">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"/>
                    <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3" Grid.ColumnSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
