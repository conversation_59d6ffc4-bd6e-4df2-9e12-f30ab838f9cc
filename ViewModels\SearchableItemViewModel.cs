using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.ViewModels
{
    public class SearchableItemViewModel : INotifyPropertyChanged
    {
        private object _originalItem;
        private bool _isHighlighted;
        private string _highlightedText;

        public object OriginalItem
        {
            get => _originalItem;
            set
            {
                _originalItem = value;
                OnPropertyChanged();
                UpdateDisplayProperties();
            }
        }

        public bool IsHighlighted
        {
            get => _isHighlighted;
            set
            {
                _isHighlighted = value;
                OnPropertyChanged();
            }
        }

        public string HighlightedText
        {
            get => _highlightedText;
            set
            {
                _highlightedText = value;
                OnPropertyChanged();
            }
        }

        // Properties from original item
        public string Name { get; private set; }
        public string Code { get; private set; }
        public string Phone { get; private set; }
        public int? Quantity { get; private set; }
        public decimal? Balance { get; private set; }
        public decimal? SalePrice { get; private set; }
        public string Unit { get; private set; }

        public SearchableItemViewModel(object item)
        {
            OriginalItem = item;
        }

        private void UpdateDisplayProperties()
        {
            if (OriginalItem == null) return;

            var type = OriginalItem.GetType();
            
            // Get common properties
            Name = GetPropertyValue("Name") ?? string.Empty;
            Code = GetPropertyValue("Code") ?? string.Empty;
            Phone = GetPropertyValue("Phone") ?? string.Empty;
            Unit = GetPropertyValue("Unit") ?? string.Empty;

            // Get numeric properties
            if (int.TryParse(GetPropertyValue("Quantity"), out int qty))
                Quantity = qty;
            
            if (decimal.TryParse(GetPropertyValue("Balance"), out decimal balance))
                Balance = balance;
                
            if (decimal.TryParse(GetPropertyValue("SalePrice"), out decimal price))
                SalePrice = price;

            OnPropertyChanged(nameof(Name));
            OnPropertyChanged(nameof(Code));
            OnPropertyChanged(nameof(Phone));
            OnPropertyChanged(nameof(Quantity));
            OnPropertyChanged(nameof(Balance));
            OnPropertyChanged(nameof(SalePrice));
            OnPropertyChanged(nameof(Unit));
        }

        private string GetPropertyValue(string propertyName)
        {
            if (OriginalItem == null) return null;
            
            var property = OriginalItem.GetType().GetProperty(propertyName);
            return property?.GetValue(OriginalItem)?.ToString();
        }

        public void SetHighlight(string searchTerm)
        {
            IsHighlighted = !string.IsNullOrEmpty(searchTerm) && 
                           (Name?.ToLower().Contains(searchTerm.ToLower()) == true ||
                            Code?.ToLower().Contains(searchTerm.ToLower()) == true ||
                            Phone?.ToLower().Contains(searchTerm.ToLower()) == true);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
