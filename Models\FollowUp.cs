using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج المتابعة
    /// </summary>
    public class FollowUp : INotifyPropertyChanged
    {
        private int _id;
        private int _customerId;
        private int? _contactId;
        private int? _opportunityId;
        private int? _interactionId;
        private string _title = string.Empty;
        private string _description = string.Empty;
        private FollowUpType _followUpType = FollowUpType.Call;
        private FollowUpPriority _priority = FollowUpPriority.Medium;
        private FollowUpStatus _status = FollowUpStatus.Pending;
        private DateTime _scheduledDate = DateTime.Now.AddDays(1);
        private DateTime? _completedDate;
        private string _assignedTo = string.Empty;
        private string _result = string.Empty;
        private string _nextAction = string.Empty;
        private DateTime? _nextFollowUpDate;
        private bool _isRecurring;
        private RecurrenceType _recurrenceType = RecurrenceType.None;
        private int _recurrenceInterval = 1;
        private DateTime? _recurrenceEndDate;
        private string _notes = string.Empty;
        private string _createdBy = string.Empty;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? ContactId
        {
            get => _contactId;
            set
            {
                if (_contactId != value)
                {
                    _contactId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? OpportunityId
        {
            get => _opportunityId;
            set
            {
                if (_opportunityId != value)
                {
                    _opportunityId = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? InteractionId
        {
            get => _interactionId;
            set
            {
                if (_interactionId != value)
                {
                    _interactionId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public FollowUpType FollowUpType
        {
            get => _followUpType;
            set
            {
                if (_followUpType != value)
                {
                    _followUpType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FollowUpTypeDisplay));
                    OnPropertyChanged(nameof(FollowUpTypeIcon));
                }
            }
        }

        public FollowUpPriority Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PriorityDisplay));
                    OnPropertyChanged(nameof(PriorityColor));
                }
            }
        }

        public FollowUpStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(IsPending));
                    OnPropertyChanged(nameof(IsCompleted));
                }
            }
        }

        public DateTime ScheduledDate
        {
            get => _scheduledDate;
            set
            {
                if (_scheduledDate != value)
                {
                    _scheduledDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedScheduledDate));
                    OnPropertyChanged(nameof(DaysUntilDue));
                    OnPropertyChanged(nameof(IsOverdue));
                    OnPropertyChanged(nameof(IsDueToday));
                }
            }
        }

        public DateTime? CompletedDate
        {
            get => _completedDate;
            set
            {
                if (_completedDate != value)
                {
                    _completedDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCompletedDate));
                }
            }
        }

        public string AssignedTo
        {
            get => _assignedTo;
            set
            {
                if (_assignedTo != value)
                {
                    _assignedTo = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Result
        {
            get => _result;
            set
            {
                if (_result != value)
                {
                    _result = value;
                    OnPropertyChanged();
                }
            }
        }

        public string NextAction
        {
            get => _nextAction;
            set
            {
                if (_nextAction != value)
                {
                    _nextAction = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? NextFollowUpDate
        {
            get => _nextFollowUpDate;
            set
            {
                if (_nextFollowUpDate != value)
                {
                    _nextFollowUpDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedNextFollowUpDate));
                }
            }
        }

        public bool IsRecurring
        {
            get => _isRecurring;
            set
            {
                if (_isRecurring != value)
                {
                    _isRecurring = value;
                    OnPropertyChanged();
                }
            }
        }

        public RecurrenceType RecurrenceType
        {
            get => _recurrenceType;
            set
            {
                if (_recurrenceType != value)
                {
                    _recurrenceType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(RecurrenceTypeDisplay));
                }
            }
        }

        public int RecurrenceInterval
        {
            get => _recurrenceInterval;
            set
            {
                if (_recurrenceInterval != value)
                {
                    _recurrenceInterval = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? RecurrenceEndDate
        {
            get => _recurrenceEndDate;
            set
            {
                if (_recurrenceEndDate != value)
                {
                    _recurrenceEndDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedRecurrenceEndDate));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsPending => Status == FollowUpStatus.Pending;
        public bool IsCompleted => Status == FollowUpStatus.Completed;
        public bool IsOverdue => IsPending && ScheduledDate < DateTime.Today;
        public bool IsDueToday => IsPending && ScheduledDate.Date == DateTime.Today;

        public int DaysUntilDue
        {
            get
            {
                if (!IsPending) return 0;
                return (int)(ScheduledDate.Date - DateTime.Today).TotalDays;
            }
        }

        // Display Properties
        public string FollowUpTypeDisplay
        {
            get
            {
                return FollowUpType switch
                {
                    FollowUpType.Call => "مكالمة",
                    FollowUpType.Email => "بريد إلكتروني",
                    FollowUpType.Meeting => "اجتماع",
                    FollowUpType.Visit => "زيارة",
                    FollowUpType.Proposal => "عرض سعر",
                    FollowUpType.Demo => "عرض توضيحي",
                    FollowUpType.Contract => "عقد",
                    FollowUpType.Payment => "دفعة",
                    FollowUpType.Support => "دعم",
                    FollowUpType.Other => "أخرى",
                    _ => "غير محدد"
                };
            }
        }

        public string PriorityDisplay
        {
            get
            {
                return Priority switch
                {
                    FollowUpPriority.Low => "منخفض",
                    FollowUpPriority.Medium => "متوسط",
                    FollowUpPriority.High => "عالي",
                    FollowUpPriority.Urgent => "عاجل",
                    _ => "غير محدد"
                };
            }
        }

        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    FollowUpStatus.Pending => "معلق",
                    FollowUpStatus.InProgress => "قيد التنفيذ",
                    FollowUpStatus.Completed => "مكتمل",
                    FollowUpStatus.Cancelled => "ملغي",
                    FollowUpStatus.Postponed => "مؤجل",
                    _ => "غير محدد"
                };
            }
        }

        public string RecurrenceTypeDisplay
        {
            get
            {
                return RecurrenceType switch
                {
                    RecurrenceType.None => "لا يوجد",
                    RecurrenceType.Daily => "يومي",
                    RecurrenceType.Weekly => "أسبوعي",
                    RecurrenceType.Monthly => "شهري",
                    RecurrenceType.Quarterly => "ربع سنوي",
                    RecurrenceType.Yearly => "سنوي",
                    _ => "غير محدد"
                };
            }
        }

        public string FollowUpTypeIcon
        {
            get
            {
                return FollowUpType switch
                {
                    FollowUpType.Call => "Phone",
                    FollowUpType.Email => "Email",
                    FollowUpType.Meeting => "AccountGroup",
                    FollowUpType.Visit => "MapMarker",
                    FollowUpType.Proposal => "FileDocument",
                    FollowUpType.Demo => "PlayCircle",
                    FollowUpType.Contract => "FileContract",
                    FollowUpType.Payment => "CurrencyUsd",
                    FollowUpType.Support => "Headset",
                    FollowUpType.Other => "DotsHorizontal",
                    _ => "Information"
                };
            }
        }

        public string PriorityColor
        {
            get
            {
                return Priority switch
                {
                    FollowUpPriority.Low => "Green",
                    FollowUpPriority.Medium => "Orange",
                    FollowUpPriority.High => "Red",
                    FollowUpPriority.Urgent => "Purple",
                    _ => "Gray"
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    FollowUpStatus.Pending => IsOverdue ? "Red" : IsDueToday ? "Orange" : "Blue",
                    FollowUpStatus.InProgress => "Purple",
                    FollowUpStatus.Completed => "Green",
                    FollowUpStatus.Cancelled => "Gray",
                    FollowUpStatus.Postponed => "Orange",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedScheduledDate => ScheduledDate.ToString("dd/MM/yyyy HH:mm");
        public string FormattedCompletedDate => CompletedDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";
        public string FormattedNextFollowUpDate => NextFollowUpDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedRecurrenceEndDate => RecurrenceEndDate?.ToString("dd/MM/yyyy") ?? "غير محدد";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy HH:mm");

        #endregion

        #region Methods

        public void Complete(string result = "")
        {
            Status = FollowUpStatus.Completed;
            CompletedDate = DateTime.Now;
            if (!string.IsNullOrEmpty(result))
                Result = result;
            UpdatedAt = DateTime.Now;
        }

        public void Cancel(string reason = "")
        {
            Status = FollowUpStatus.Cancelled;
            if (!string.IsNullOrEmpty(reason))
                Notes += $"\nسبب الإلغاء: {reason}";
            UpdatedAt = DateTime.Now;
        }

        public void Postpone(DateTime newDate, string reason = "")
        {
            Status = FollowUpStatus.Postponed;
            ScheduledDate = newDate;
            if (!string.IsNullOrEmpty(reason))
                Notes += $"\nسبب التأجيل: {reason}";
            UpdatedAt = DateTime.Now;
        }

        public void StartProgress()
        {
            Status = FollowUpStatus.InProgress;
            UpdatedAt = DateTime.Now;
        }

        public DateTime? GetNextRecurrenceDate()
        {
            if (!IsRecurring || RecurrenceType == RecurrenceType.None)
                return null;

            var nextDate = RecurrenceType switch
            {
                RecurrenceType.Daily => ScheduledDate.AddDays(RecurrenceInterval),
                RecurrenceType.Weekly => ScheduledDate.AddDays(7 * RecurrenceInterval),
                RecurrenceType.Monthly => ScheduledDate.AddMonths(RecurrenceInterval),
                RecurrenceType.Quarterly => ScheduledDate.AddMonths(3 * RecurrenceInterval),
                RecurrenceType.Yearly => ScheduledDate.AddYears(RecurrenceInterval),
                _ => null
            };

            if (nextDate.HasValue && RecurrenceEndDate.HasValue && nextDate > RecurrenceEndDate)
                return null;

            return nextDate;
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Enums

    public enum FollowUpType
    {
        Call,               // مكالمة
        Email,              // بريد إلكتروني
        Meeting,            // اجتماع
        Visit,              // زيارة
        Proposal,           // عرض سعر
        Demo,               // عرض توضيحي
        Contract,           // عقد
        Payment,            // دفعة
        Support,            // دعم
        Other               // أخرى
    }

    public enum FollowUpPriority
    {
        Low,                // منخفض
        Medium,             // متوسط
        High,               // عالي
        Urgent              // عاجل
    }

    public enum FollowUpStatus
    {
        Pending,            // معلق
        InProgress,         // قيد التنفيذ
        Completed,          // مكتمل
        Cancelled,          // ملغي
        Postponed           // مؤجل
    }

    public enum RecurrenceType
    {
        None,               // لا يوجد
        Daily,              // يومي
        Weekly,             // أسبوعي
        Monthly,            // شهري
        Quarterly,          // ربع سنوي
        Yearly              // سنوي
    }

    #endregion

    #region Validation

    public class FollowUpValidator : AbstractValidator<FollowUp>
    {
        public FollowUpValidator()
        {
            RuleFor(f => f.CustomerId)
                .GreaterThan(0).WithMessage("العميل مطلوب");

            RuleFor(f => f.Title)
                .NotEmpty().WithMessage("عنوان المتابعة مطلوب")
                .MaximumLength(200).WithMessage("عنوان المتابعة لا يمكن أن يتجاوز 200 حرف");

            RuleFor(f => f.ScheduledDate)
                .GreaterThanOrEqualTo(DateTime.Today).WithMessage("تاريخ المتابعة يجب أن يكون في المستقبل");

            RuleFor(f => f.RecurrenceInterval)
                .GreaterThan(0).When(f => f.IsRecurring)
                .WithMessage("فترة التكرار يجب أن تكون أكبر من صفر");

            RuleFor(f => f.RecurrenceEndDate)
                .GreaterThan(f => f.ScheduledDate).When(f => f.IsRecurring && f.RecurrenceEndDate.HasValue)
                .WithMessage("تاريخ انتهاء التكرار يجب أن يكون بعد تاريخ المتابعة");
        }
    }

    #endregion
}
