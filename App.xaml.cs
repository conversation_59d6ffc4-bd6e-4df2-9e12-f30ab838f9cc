using System;
using System.Globalization;
using System.Threading;
using System.Windows;
using System.Windows.Markup;
using SalesManagementSystem.Services;

namespace SalesManagementSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public static bool IsArabic { get; private set; } = true;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // Set application culture and UI direction
            SetApplicationCulture(IsArabic ? "ar-SA" : "en-US");

            // Initialize services
            InitializeServices();
        }

        public static void SetApplicationCulture(string cultureName)
        {
            IsArabic = cultureName.StartsWith("ar");

            // Set the culture for the current thread
            Thread.CurrentThread.CurrentCulture = new CultureInfo(cultureName);
            Thread.CurrentThread.CurrentUICulture = new CultureInfo(cultureName);

            // Set FlowDirection for XAML
            FrameworkElement.FlowDirectionProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(IsArabic ? FlowDirection.RightToLeft : FlowDirection.LeftToRight));

            // Set language for XAML
            XmlLanguage language = XmlLanguage.GetLanguage(cultureName);
            FrameworkElement.LanguageProperty.OverrideMetadata(
                typeof(FrameworkElement),
                new FrameworkPropertyMetadata(language));

            // Reload resources
            ResourceDictionary resourceDictionary = new ResourceDictionary
            {
                Source = new Uri($"/Resources/Localization/{cultureName}.xaml", UriKind.Relative)
            };

            // Replace the localization dictionary
            ResourceDictionary? oldDictionary = null;
            foreach (ResourceDictionary dict in Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Localization/"))
                {
                    oldDictionary = dict;
                    break;
                }
            }

            if (oldDictionary != null)
            {
                int index = Current.Resources.MergedDictionaries.IndexOf(oldDictionary);
                Current.Resources.MergedDictionaries.Remove(oldDictionary);
                Current.Resources.MergedDictionaries.Insert(index, resourceDictionary);
            }
            else
            {
                Current.Resources.MergedDictionaries.Add(resourceDictionary);
            }
        }

        private async void InitializeServices()
        {
            try
            {
                // تهيئة نظام التسجيل
                LoggingService.LogSystemEvent("بدء التطبيق", "تم بدء تشغيل نظام إدارة المبيعات");

                // تهيئة قاعدة البيانات
                var dbService = new DatabaseService();
                await dbService.InitializeDatabaseAsync();

                // تهيئة الإعدادات الافتراضية
                var settingsService = new SettingsService(dbService);
                await settingsService.InitializeDefaultSettingsAsync();

                // تهيئة الفئات الافتراضية
                var categoryService = new CategoryService(dbService);
                await categoryService.InitializeDefaultCategoriesAsync();

                // تنظيف ملفات السجل القديمة
                LoggingService.CleanOldLogs(30);

                LoggingService.LogSystemEvent("تهيئة الخدمات", "تم تهيئة جميع الخدمات بنجاح");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة الخدمات");
                MessageBox.Show($"خطأ في تهيئة الخدمات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}