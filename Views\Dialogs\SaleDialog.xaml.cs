using System.Windows;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views.Dialogs
{
    public partial class SaleDialog : Window
    {
        private readonly SaleDialogViewModel _viewModel;

        public Sale? Result { get; private set; }

        public SaleDialog(Sale? existingSale = null)
        {
            InitializeComponent();

            _viewModel = existingSale != null
                ? new SaleDialogViewModel(existingSale)
                : new SaleDialogViewModel();

            DataContext = _viewModel;

            // Subscribe to events
            _viewModel.SaleSaved += OnSaleSaved;
            _viewModel.RequestClose += OnRequestClose;
        }

        #region Event Handlers

        private void OnSaleSaved(Sale sale)
        {
            Result = sale;
            DialogResult = true;
        }

        private void OnRequestClose(bool result)
        {
            DialogResult = result;
            Close();
        }

        private void CustomerComboBox_AddNewRequested(object sender, object e)
        {
            _viewModel.AddCustomerCommand.Execute();
        }

        private void ProductComboBox_AddNewRequested(object sender, object e)
        {
            _viewModel.AddProductCommand.Execute();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.SaleSaved -= OnSaleSaved;
            _viewModel.RequestClose -= OnRequestClose;

            base.OnClosed(e);
        }

        #endregion
    }
}
