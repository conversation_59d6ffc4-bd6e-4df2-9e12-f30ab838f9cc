using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class AuthenticationService
    {
        private readonly DatabaseService _dbService;
        private readonly SecurityService _securityService;
        private User? _currentUser;

        public AuthenticationService(DatabaseService dbService)
        {
            _dbService = dbService;
            _securityService = new SecurityService();
        }

        public User? CurrentUser => _currentUser;
        public bool IsAuthenticated => _currentUser != null;

        public async Task<AuthenticationResult> LoginAsync(string username, string password)
        {
            try
            {
                // Get user from database
                var user = await GetUserByUsernameAsync(username);
                if (user == null)
                {
                    LoggingService.LogSecurityEvent("فشل تسجيل الدخول", $"اسم المستخدم غير موجود: {username}");
                    return new AuthenticationResult { Success = false, Message = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }

                // Check if user is locked
                if (user.IsLocked)
                {
                    LoggingService.LogSecurityEvent("محاولة دخول لحساب مقفل", $"المستخدم: {username}");
                    return new AuthenticationResult { Success = false, Message = "الحساب مقفل. يرجى الاتصال بالمدير" };
                }

                // Check if user is active
                if (!user.IsActive)
                {
                    LoggingService.LogSecurityEvent("محاولة دخول لحساب غير نشط", $"المستخدم: {username}");
                    return new AuthenticationResult { Success = false, Message = "الحساب غير نشط" };
                }

                // Verify password
                if (!_securityService.VerifyPassword(password, user.PasswordHash, user.Salt))
                {
                    await HandleFailedLoginAsync(user);
                    LoggingService.LogSecurityEvent("فشل تسجيل الدخول", $"كلمة مرور خاطئة للمستخدم: {username}");
                    return new AuthenticationResult { Success = false, Message = "اسم المستخدم أو كلمة المرور غير صحيحة" };
                }

                // Check if password needs to be changed
                if (user.RequiresPasswordChange)
                {
                    LoggingService.LogSecurityEvent("مطلوب تغيير كلمة المرور", $"المستخدم: {username}");
                    return new AuthenticationResult
                    {
                        Success = true,
                        RequiresPasswordChange = true,
                        User = user,
                        Message = "يجب تغيير كلمة المرور"
                    };
                }

                // Successful login
                await HandleSuccessfulLoginAsync(user);
                _currentUser = user;

                LoggingService.LogSecurityEvent("تسجيل دخول ناجح", $"المستخدم: {username}");
                return new AuthenticationResult { Success = true, User = user, Message = "تم تسجيل الدخول بنجاح" };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تسجيل الدخول للمستخدم: {username}");
                return new AuthenticationResult { Success = false, Message = "حدث خطأ أثناء تسجيل الدخول" };
            }
        }

        public async Task<bool> ChangePasswordAsync(string currentPassword, string newPassword)
        {
            try
            {
                if (_currentUser == null)
                    return false;

                // Verify current password
                if (!_securityService.VerifyPassword(currentPassword, _currentUser.PasswordHash, _currentUser.Salt))
                {
                    LoggingService.LogSecurityEvent("فشل تغيير كلمة المرور", $"كلمة المرور الحالية خاطئة للمستخدم: {_currentUser.Username}");
                    return false;
                }

                // Validate new password
                var passwordValidator = new PasswordValidator();
                var validationResult = passwordValidator.Validate(newPassword);
                if (!validationResult.IsValid)
                {
                    return false;
                }

                // Hash new password
                var (hash, salt) = _securityService.HashPassword(newPassword);

                // Update user password
                _currentUser.PasswordHash = hash;
                _currentUser.Salt = salt;
                _currentUser.LastPasswordChangeDate = DateTime.Now;
                _currentUser.UpdatedAt = DateTime.Now;

                // Save to database
                await _dbService.UpdateAsync("Users", _currentUser);

                LoggingService.LogSecurityEvent("تغيير كلمة المرور", $"المستخدم: {_currentUser.Username}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تغيير كلمة المرور للمستخدم: {_currentUser?.Username}");
                return false;
            }
        }

        public void Logout()
        {
            if (_currentUser != null)
            {
                LoggingService.LogSecurityEvent("تسجيل خروج", $"المستخدم: {_currentUser.Username}");
                _currentUser = null;
            }
        }

        public async Task<bool> CreateUserAsync(User user, string password)
        {
            try
            {
                // Check if username already exists
                var existingUser = await GetUserByUsernameAsync(user.Username);
                if (existingUser != null)
                {
                    return false;
                }

                // Validate password
                var passwordValidator = new PasswordValidator();
                var validationResult = passwordValidator.Validate(password);
                if (!validationResult.IsValid)
                {
                    return false;
                }

                // Hash password
                var (hash, salt) = _securityService.HashPassword(password);
                user.PasswordHash = hash;
                user.Salt = salt;
                user.CreatedAt = DateTime.Now;
                user.LastPasswordChangeDate = DateTime.Now;

                // Save to database
                var result = await _dbService.InsertAsync<User>("Users", user);
                if (result is User savedUser)
                {
                    user.Id = savedUser.Id;
                }

                LoggingService.LogSecurityEvent("إنشاء مستخدم جديد", $"المستخدم: {user.Username}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء المستخدم: {user.Username}");
                return false;
            }
        }

        public async Task<bool> LockUserAsync(int userId)
        {
            try
            {
                var sql = "SELECT * FROM Users WHERE Id = @Id";
                var users = await _dbService.QueryAsync<User>(sql, new { Id = userId });
                var user = users.FirstOrDefault();
                if (user == null) return false;

                user.IsLocked = true;
                user.UpdatedAt = DateTime.Now;

                await _dbService.UpdateAsync("Users", user);
                LoggingService.LogSecurityEvent("قفل المستخدم", $"المستخدم: {user.Username}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في قفل المستخدم: {userId}");
                return false;
            }
        }

        public async Task<bool> UnlockUserAsync(int userId)
        {
            try
            {
                var sql = "SELECT * FROM Users WHERE Id = @Id";
                var users = await _dbService.QueryAsync<User>(sql, new { Id = userId });
                var user = users.FirstOrDefault();
                if (user == null) return false;

                user.IsLocked = false;
                user.FailedLoginAttempts = 0;
                user.UpdatedAt = DateTime.Now;

                await _dbService.UpdateAsync("Users", user);
                LoggingService.LogSecurityEvent("إلغاء قفل المستخدم", $"المستخدم: {user.Username}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إلغاء قفل المستخدم: {userId}");
                return false;
            }
        }

        public bool HasPermission(string permissionName)
        {
            return _currentUser?.HasPermission(permissionName) ?? false;
        }

        public bool HasAnyPermission(params string[] permissionNames)
        {
            return _currentUser?.HasAnyPermission(permissionNames) ?? false;
        }

        public bool HasAllPermissions(params string[] permissionNames)
        {
            return _currentUser?.HasAllPermissions(permissionNames) ?? false;
        }

        private async Task<User?> GetUserByUsernameAsync(string username)
        {
            var sql = "SELECT * FROM Users WHERE Username = @Username";
            var users = await _dbService.QueryAsync<User>(sql, new { Username = username });
            return users.FirstOrDefault();
        }

        private async Task HandleFailedLoginAsync(User user)
        {
            user.FailedLoginAttempts++;

            // Lock user after 5 failed attempts
            if (user.FailedLoginAttempts >= 5)
            {
                user.IsLocked = true;
                LoggingService.LogSecurityEvent("قفل المستخدم تلقائياً", $"المستخدم: {user.Username} - محاولات فاشلة: {user.FailedLoginAttempts}");
            }

            user.UpdatedAt = DateTime.Now;
            await _dbService.UpdateAsync("Users", user);
        }

        private async Task HandleSuccessfulLoginAsync(User user)
        {
            user.LastLoginDate = DateTime.Now;
            user.FailedLoginAttempts = 0;
            user.UpdatedAt = DateTime.Now;

            await _dbService.UpdateAsync("Users", user);
        }
    }

    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
        public bool RequiresPasswordChange { get; set; }
    }
}
