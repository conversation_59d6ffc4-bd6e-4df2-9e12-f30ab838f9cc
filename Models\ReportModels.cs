using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace SalesManagementSystem.Models
{
    #region Advanced Report Models

    public class SaleReportItem : INotifyPropertyChanged
    {
        private int _id;
        private DateTime _saleDate;
        private decimal _totalAmount;
        private decimal _discountAmount;
        private decimal _taxAmount;
        private decimal _netAmount;
        private string _customerName = string.Empty;
        private string _customerPhone = string.Empty;
        private string _salespersonName = string.Empty;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public DateTime SaleDate
        {
            get => _saleDate;
            set => SetProperty(ref _saleDate, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set => SetProperty(ref _discountAmount, value);
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set => SetProperty(ref _taxAmount, value);
        }

        public decimal NetAmount
        {
            get => _netAmount;
            set => SetProperty(ref _netAmount, value);
        }

        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public string CustomerPhone
        {
            get => _customerPhone;
            set => SetProperty(ref _customerPhone, value);
        }

        public string SalespersonName
        {
            get => _salespersonName;
            set => SetProperty(ref _salespersonName, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    public class ProductSalesReport : INotifyPropertyChanged
    {
        private int _productId;
        private string _productName = string.Empty;
        private string _productCode = string.Empty;
        private string _category = string.Empty;
        private int _totalQuantitySold;
        private decimal _totalRevenue;
        private decimal _averagePrice;
        private int _numberOfOrders;

        public int ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);
        }

        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        public string ProductCode
        {
            get => _productCode;
            set => SetProperty(ref _productCode, value);
        }

        public string Category
        {
            get => _category;
            set => SetProperty(ref _category, value);
        }

        public int TotalQuantitySold
        {
            get => _totalQuantitySold;
            set => SetProperty(ref _totalQuantitySold, value);
        }

        public decimal TotalRevenue
        {
            get => _totalRevenue;
            set => SetProperty(ref _totalRevenue, value);
        }

        public decimal AveragePrice
        {
            get => _averagePrice;
            set => SetProperty(ref _averagePrice, value);
        }

        public int NumberOfOrders
        {
            get => _numberOfOrders;
            set => SetProperty(ref _numberOfOrders, value);
        }

        public decimal RevenuePercentage { get; set; }
        public decimal QuantityPercentage { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    public class CustomerSalesReport : INotifyPropertyChanged
    {
        private int _customerId;
        private string _customerName = string.Empty;
        private string _customerPhone = string.Empty;
        private string _customerEmail = string.Empty;
        private int _totalOrders;
        private decimal _totalSpent;
        private decimal _averageOrderValue;
        private DateTime? _lastOrderDate;
        private DateTime? _firstOrderDate;

        public int CustomerId
        {
            get => _customerId;
            set => SetProperty(ref _customerId, value);
        }

        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public string CustomerPhone
        {
            get => _customerPhone;
            set => SetProperty(ref _customerPhone, value);
        }

        public string CustomerEmail
        {
            get => _customerEmail;
            set => SetProperty(ref _customerEmail, value);
        }

        public int TotalOrders
        {
            get => _totalOrders;
            set => SetProperty(ref _totalOrders, value);
        }

        public decimal TotalSpent
        {
            get => _totalSpent;
            set => SetProperty(ref _totalSpent, value);
        }

        public decimal AverageOrderValue
        {
            get => _averageOrderValue;
            set => SetProperty(ref _averageOrderValue, value);
        }

        public DateTime? LastOrderDate
        {
            get => _lastOrderDate;
            set => SetProperty(ref _lastOrderDate, value);
        }

        public DateTime? FirstOrderDate
        {
            get => _firstOrderDate;
            set => SetProperty(ref _firstOrderDate, value);
        }

        public string CustomerType => TotalSpent switch
        {
            >= 10000 => "عميل ذهبي",
            >= 5000 => "عميل فضي",
            >= 1000 => "عميل برونزي",
            _ => "عميل عادي"
        };

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    public class InventoryReportItem : INotifyPropertyChanged
    {
        private int _productId;
        private string _productName = string.Empty;
        private string _productCode = string.Empty;
        private string _category = string.Empty;
        private int _currentStock;
        private int _minimumStock;
        private decimal _costPrice;
        private decimal _salePrice;
        private decimal _totalCostValue;
        private decimal _totalSaleValue;
        private string _stockStatus = string.Empty;

        public int ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);
        }

        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        public string ProductCode
        {
            get => _productCode;
            set => SetProperty(ref _productCode, value);
        }

        public string Category
        {
            get => _category;
            set => SetProperty(ref _category, value);
        }

        public int CurrentStock
        {
            get => _currentStock;
            set => SetProperty(ref _currentStock, value);
        }

        public int MinimumStock
        {
            get => _minimumStock;
            set => SetProperty(ref _minimumStock, value);
        }

        public decimal CostPrice
        {
            get => _costPrice;
            set => SetProperty(ref _costPrice, value);
        }

        public decimal SalePrice
        {
            get => _salePrice;
            set => SetProperty(ref _salePrice, value);
        }

        public decimal TotalCostValue
        {
            get => _totalCostValue;
            set => SetProperty(ref _totalCostValue, value);
        }

        public decimal TotalSaleValue
        {
            get => _totalSaleValue;
            set => SetProperty(ref _totalSaleValue, value);
        }

        public string StockStatus
        {
            get => _stockStatus;
            set => SetProperty(ref _stockStatus, value);
        }

        public decimal ProfitMargin => SalePrice > 0 ? Math.Round(((SalePrice - CostPrice) / SalePrice) * 100, 2) : 0;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    public class ChartDataPoint
    {
        public string Label { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public string Color { get; set; } = "#2196F3";
    }

    public class ReportFilter
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? CustomerId { get; set; }
        public int? ProductId { get; set; }
        public int? CategoryId { get; set; }
        public int? EmployeeId { get; set; }
        public string? PaymentMethod { get; set; }
        public string? PaymentStatus { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; } = true;
        public int PageSize { get; set; } = 50;
        public int PageNumber { get; set; } = 1;
    }

    #endregion
}
