using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي المتقدمة
    /// </summary>
    public class AdvancedBackupService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly Timer _schedulerTimer;
        private readonly Dictionary<int, CancellationTokenSource> _runningJobs;

        public AdvancedBackupService(DatabaseService dbService, NotificationService notificationService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _runningJobs = new Dictionary<int, CancellationTokenSource>();
            
            // تشغيل المجدول كل دقيقة
            _schedulerTimer = new Timer(CheckScheduledJobs, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
        }

        #region Backup Job Management

        /// <summary>
        /// إنشاء مهمة نسخ احتياطي جديدة
        /// </summary>
        public async Task<int> CreateBackupJobAsync(BackupJob job)
        {
            try
            {
                // توليد كود المهمة إذا لم يكن موجود
                if (string.IsNullOrEmpty(job.JobCode))
                {
                    job.JobCode = await GenerateJobCodeAsync();
                }

                const string sql = @"
                    INSERT INTO BackupJobs (
                        JobName, JobCode, BackupType, Frequency, Status, SourcePath, DestinationPath,
                        CloudProvider, CloudCredentials, IsEncrypted, EncryptionKey, IsCompressed,
                        CompressionLevel, RetentionDays, MaxBackupCopies, NextRunTime, EstimatedDuration,
                        EstimatedSize, IncludeFilters, ExcludeFilters, VerifyIntegrity, SendNotifications,
                        NotificationEmails, CreatedBy, CreatedAt
                    ) VALUES (
                        @JobName, @JobCode, @BackupType, @Frequency, @Status, @SourcePath, @DestinationPath,
                        @CloudProvider, @CloudCredentials, @IsEncrypted, @EncryptionKey, @IsCompressed,
                        @CompressionLevel, @RetentionDays, @MaxBackupCopies, @NextRunTime, @EstimatedDuration,
                        @EstimatedSize, @IncludeFilters, @ExcludeFilters, @VerifyIntegrity, @SendNotifications,
                        @NotificationEmails, @CreatedBy, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    job.JobName,
                    job.JobCode,
                    BackupType = job.BackupType.ToString(),
                    Frequency = job.Frequency.ToString(),
                    Status = job.Status.ToString(),
                    job.SourcePath,
                    job.DestinationPath,
                    job.CloudProvider,
                    job.CloudCredentials,
                    job.IsEncrypted,
                    job.EncryptionKey,
                    job.IsCompressed,
                    CompressionLevel = job.CompressionLevel.ToString(),
                    job.RetentionDays,
                    job.MaxBackupCopies,
                    NextRunTime = job.NextRunTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    EstimatedDuration = job.EstimatedDuration.ToString(),
                    job.EstimatedSize,
                    job.IncludeFilters,
                    job.ExcludeFilters,
                    job.VerifyIntegrity,
                    job.SendNotifications,
                    job.NotificationEmails,
                    job.CreatedBy,
                    CreatedAt = job.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var jobId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                job.Id = jobId;

                LoggingService.LogInfo($"تم إنشاء مهمة نسخ احتياطي جديدة: {job.JobName}");
                return jobId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء مهمة النسخ الاحتياطي: {job?.JobName}");
                throw;
            }
        }

        /// <summary>
        /// تشغيل مهمة النسخ الاحتياطي
        /// </summary>
        public async Task<bool> RunBackupJobAsync(int jobId)
        {
            try
            {
                var job = await GetBackupJobByIdAsync(jobId);
                if (job == null)
                    return false;

                // التحقق من أن المهمة ليست قيد التشغيل
                if (_runningJobs.ContainsKey(jobId))
                    return false;

                var cancellationTokenSource = new CancellationTokenSource();
                _runningJobs[jobId] = cancellationTokenSource;

                // تشغيل المهمة في مهمة منفصلة
                _ = Task.Run(async () => await ExecuteBackupJobAsync(job, cancellationTokenSource.Token));

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تشغيل مهمة النسخ الاحتياطي: {jobId}");
                return false;
            }
        }

        /// <summary>
        /// إيقاف مهمة النسخ الاحتياطي
        /// </summary>
        public async Task<bool> StopBackupJobAsync(int jobId)
        {
            try
            {
                if (_runningJobs.TryGetValue(jobId, out var cancellationTokenSource))
                {
                    cancellationTokenSource.Cancel();
                    _runningJobs.Remove(jobId);
                    
                    var job = await GetBackupJobByIdAsync(jobId);
                    if (job != null)
                    {
                        job.Cancel();
                        await UpdateBackupJobAsync(job);
                    }
                    
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إيقاف مهمة النسخ الاحتياطي: {jobId}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع مهام النسخ الاحتياطي
        /// </summary>
        public async Task<IEnumerable<BackupJob>> GetAllBackupJobsAsync()
        {
            try
            {
                const string sql = "SELECT * FROM BackupJobs ORDER BY CreatedAt DESC";
                var jobsData = await _dbService.QueryAsync<dynamic>(sql);
                return jobsData.Select(MapToBackupJob);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على مهام النسخ الاحتياطي");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مهمة نسخ احتياطي بالمعرف
        /// </summary>
        public async Task<BackupJob?> GetBackupJobByIdAsync(int jobId)
        {
            try
            {
                const string sql = "SELECT * FROM BackupJobs WHERE Id = @Id";
                var jobData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = jobId });
                
                if (jobData != null)
                {
                    return MapToBackupJob(jobData);
                }
                
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على مهمة النسخ الاحتياطي: {jobId}");
                throw;
            }
        }

        /// <summary>
        /// تحديث مهمة النسخ الاحتياطي
        /// </summary>
        public async Task UpdateBackupJobAsync(BackupJob job)
        {
            try
            {
                const string sql = @"
                    UPDATE BackupJobs SET
                        JobName = @JobName,
                        BackupType = @BackupType,
                        Frequency = @Frequency,
                        Status = @Status,
                        SourcePath = @SourcePath,
                        DestinationPath = @DestinationPath,
                        CloudProvider = @CloudProvider,
                        IsEncrypted = @IsEncrypted,
                        IsCompressed = @IsCompressed,
                        CompressionLevel = @CompressionLevel,
                        RetentionDays = @RetentionDays,
                        MaxBackupCopies = @MaxBackupCopies,
                        NextRunTime = @NextRunTime,
                        LastRunTime = @LastRunTime,
                        LastSuccessTime = @LastSuccessTime,
                        ActualDuration = @ActualDuration,
                        ActualSize = @ActualSize,
                        ErrorMessage = @ErrorMessage,
                        SuccessCount = @SuccessCount,
                        FailureCount = @FailureCount,
                        SuccessRate = @SuccessRate,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                var parameters = new
                {
                    job.Id,
                    job.JobName,
                    BackupType = job.BackupType.ToString(),
                    Frequency = job.Frequency.ToString(),
                    Status = job.Status.ToString(),
                    job.SourcePath,
                    job.DestinationPath,
                    job.CloudProvider,
                    job.IsEncrypted,
                    job.IsCompressed,
                    CompressionLevel = job.CompressionLevel.ToString(),
                    job.RetentionDays,
                    job.MaxBackupCopies,
                    NextRunTime = job.NextRunTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    LastRunTime = job.LastRunTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    LastSuccessTime = job.LastSuccessTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    ActualDuration = job.ActualDuration?.ToString(),
                    job.ActualSize,
                    job.ErrorMessage,
                    job.SuccessCount,
                    job.FailureCount,
                    job.SuccessRate,
                    UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _dbService.ExecuteAsync(sql, parameters);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث مهمة النسخ الاحتياطي: {job.JobName}");
                throw;
            }
        }

        /// <summary>
        /// حذف مهمة النسخ الاحتياطي
        /// </summary>
        public async Task<bool> DeleteBackupJobAsync(int jobId)
        {
            try
            {
                // إيقاف المهمة إذا كانت قيد التشغيل
                await StopBackupJobAsync(jobId);

                // حذف المهمة من قاعدة البيانات
                const string sql = "DELETE FROM BackupJobs WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = jobId });

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف مهمة النسخ الاحتياطي: {jobId}");
                return false;
            }
        }

        #endregion

        #region Restore Operations

        /// <summary>
        /// إنشاء مهمة استعادة جديدة
        /// </summary>
        public async Task<int> CreateRestoreJobAsync(RestoreJob restoreJob)
        {
            try
            {
                if (string.IsNullOrEmpty(restoreJob.RestoreCode))
                {
                    restoreJob.RestoreCode = await GenerateRestoreCodeAsync();
                }

                const string sql = @"
                    INSERT INTO RestoreJobs (
                        RestoreName, RestoreCode, RestoreType, Status, BackupJobId, BackupHistoryId,
                        BackupFilePath, RestoreDestination, OverwriteExisting, VerifyIntegrity,
                        CreateRestorePoint, RestorePointDate, SelectedFiles, SelectedTables,
                        RequestedAt, EstimatedDuration, TotalSize, TotalFiles, RequestedBy,
                        RequiresApproval, BusinessJustification, Priority
                    ) VALUES (
                        @RestoreName, @RestoreCode, @RestoreType, @Status, @BackupJobId, @BackupHistoryId,
                        @BackupFilePath, @RestoreDestination, @OverwriteExisting, @VerifyIntegrity,
                        @CreateRestorePoint, @RestorePointDate, @SelectedFiles, @SelectedTables,
                        @RequestedAt, @EstimatedDuration, @TotalSize, @TotalFiles, @RequestedBy,
                        @RequiresApproval, @BusinessJustification, @Priority
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    restoreJob.RestoreName,
                    restoreJob.RestoreCode,
                    RestoreType = restoreJob.RestoreType.ToString(),
                    Status = restoreJob.Status.ToString(),
                    restoreJob.BackupJobId,
                    restoreJob.BackupHistoryId,
                    restoreJob.BackupFilePath,
                    restoreJob.RestoreDestination,
                    restoreJob.OverwriteExisting,
                    restoreJob.VerifyIntegrity,
                    restoreJob.CreateRestorePoint,
                    RestorePointDate = restoreJob.RestorePointDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    restoreJob.SelectedFiles,
                    restoreJob.SelectedTables,
                    RequestedAt = restoreJob.RequestedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    EstimatedDuration = restoreJob.EstimatedDuration.ToString(),
                    restoreJob.TotalSize,
                    restoreJob.TotalFiles,
                    restoreJob.RequestedBy,
                    restoreJob.RequiresApproval,
                    restoreJob.BusinessJustification,
                    Priority = restoreJob.Priority.ToString()
                };

                var restoreId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                restoreJob.Id = restoreId;

                LoggingService.LogInfo($"تم إنشاء مهمة استعادة جديدة: {restoreJob.RestoreName}");
                return restoreId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء مهمة الاستعادة: {restoreJob?.RestoreName}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private async Task<string> GenerateJobCodeAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM BackupJobs";
                var count = await _dbService.QuerySingleAsync<int>(sql);
                return $"BKP{DateTime.Now:yyyyMMdd}{(count + 1):D4}";
            }
            catch
            {
                return $"BKP{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private async Task<string> GenerateRestoreCodeAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM RestoreJobs";
                var count = await _dbService.QuerySingleAsync<int>(sql);
                return $"RST{DateTime.Now:yyyyMMdd}{(count + 1):D4}";
            }
            catch
            {
                return $"RST{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private void CheckScheduledJobs(object state)
        {
            try
            {
                _ = Task.Run(async () =>
                {
                    var scheduledJobs = await GetScheduledJobsAsync();
                    foreach (var job in scheduledJobs)
                    {
                        if (job.NextRunTime <= DateTime.Now && !_runningJobs.ContainsKey(job.Id))
                        {
                            await RunBackupJobAsync(job.Id);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص المهام المجدولة");
            }
        }

        private BackupJob MapToBackupJob(dynamic data)
        {
            var job = new BackupJob
            {
                Id = data.Id,
                JobName = data.JobName ?? "",
                JobCode = data.JobCode ?? "",
                SourcePath = data.SourcePath ?? "",
                DestinationPath = data.DestinationPath ?? "",
                CloudProvider = data.CloudProvider ?? "",
                IsEncrypted = data.IsEncrypted,
                IsCompressed = data.IsCompressed,
                RetentionDays = data.RetentionDays,
                MaxBackupCopies = data.MaxBackupCopies,
                EstimatedSize = data.EstimatedSize,
                ActualSize = data.ActualSize,
                SuccessCount = data.SuccessCount,
                FailureCount = data.FailureCount,
                SuccessRate = data.SuccessRate,
                ErrorMessage = data.ErrorMessage ?? "",
                CreatedBy = data.CreatedBy ?? ""
            };

            // Parse enums
            if (Enum.TryParse<BackupType>(data.BackupType?.ToString(), out var backupType))
                job.BackupType = backupType;

            if (Enum.TryParse<BackupFrequency>(data.Frequency?.ToString(), out var frequency))
                job.Frequency = frequency;

            if (Enum.TryParse<BackupStatus>(data.Status?.ToString(), out var status))
                job.Status = status;

            if (Enum.TryParse<CompressionLevel>(data.CompressionLevel?.ToString(), out var compressionLevel))
                job.CompressionLevel = compressionLevel;

            // Parse dates
            if (DateTime.TryParse(data.NextRunTime?.ToString(), out var nextRunTime))
                job.NextRunTime = nextRunTime;

            if (DateTime.TryParse(data.LastRunTime?.ToString(), out var lastRunTime))
                job.LastRunTime = lastRunTime;

            if (DateTime.TryParse(data.LastSuccessTime?.ToString(), out var lastSuccessTime))
                job.LastSuccessTime = lastSuccessTime;

            if (DateTime.TryParse(data.CreatedAt?.ToString(), out var createdAt))
                job.CreatedAt = createdAt;

            // Parse TimeSpan
            if (TimeSpan.TryParse(data.EstimatedDuration?.ToString(), out var estimatedDuration))
                job.EstimatedDuration = estimatedDuration;

            if (TimeSpan.TryParse(data.ActualDuration?.ToString(), out var actualDuration))
                job.ActualDuration = actualDuration;

            return job;
        }

        private async Task<IEnumerable<BackupJob>> GetScheduledJobsAsync()
        {
            try
            {
                const string sql = "SELECT * FROM BackupJobs WHERE Status = 'Scheduled' AND NextRunTime <= @Now";
                var jobsData = await _dbService.QueryAsync<dynamic>(sql, new { Now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") });
                return jobsData.Select(MapToBackupJob);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المهام المجدولة");
                return new List<BackupJob>();
            }
        }

        private async Task ExecuteBackupJobAsync(BackupJob job, CancellationToken cancellationToken)
        {
            // Implementation would go here...
            await Task.Delay(1000, cancellationToken); // Placeholder
        }

        #endregion

        #region Disposal

        public void Dispose()
        {
            _schedulerTimer?.Dispose();
            foreach (var tokenSource in _runningJobs.Values)
            {
                tokenSource?.Cancel();
                tokenSource?.Dispose();
            }
            _runningJobs.Clear();
        }

        #endregion
    }
}
