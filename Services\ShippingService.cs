using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة الشحن
    /// </summary>
    public class ShippingService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;

        public ShippingService(DatabaseService dbService, NotificationService notificationService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        }

        #region Shipping Methods Management

        /// <summary>
        /// الحصول على جميع طرق الشحن النشطة
        /// </summary>
        public async Task<IEnumerable<ShippingMethod>> GetActiveShippingMethodsAsync()
        {
            try
            {
                const string sql = "SELECT * FROM ShippingMethods WHERE IsActive = 1 ORDER BY SortOrder, Name";
                var methodsData = await _dbService.QueryAsync<dynamic>(sql);
                return methodsData.Select(MapToShippingMethod);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على طرق الشحن النشطة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على طرق الشحن المتاحة للعنوان
        /// </summary>
        public async Task<IEnumerable<ShippingMethod>> GetAvailableShippingMethodsAsync(Address address, decimal orderTotal = 0, decimal weight = 0)
        {
            try
            {
                var allMethods = await GetActiveShippingMethodsAsync();
                var availableMethods = new List<ShippingMethod>();

                foreach (var method in allMethods)
                {
                    if (method.CanShipTo(address))
                    {
                        // حساب تكلفة الشحن
                        var shippingCost = method.CalculateShippingCost(orderTotal, weight);
                        method.BaseCost = shippingCost;
                        
                        availableMethods.Add(method);
                    }
                }

                return availableMethods.OrderBy(m => m.BaseCost);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على طرق الشحن المتاحة");
                throw;
            }
        }

        /// <summary>
        /// إنشاء طريقة شحن جديدة
        /// </summary>
        public async Task<int> CreateShippingMethodAsync(ShippingMethod shippingMethod)
        {
            try
            {
                const string sql = @"
                    INSERT INTO ShippingMethods (
                        Name, Code, Description, ShippingType, BaseCost, CostPerKg, CostPerKm,
                        FreeShippingThreshold, EstimatedDaysMin, EstimatedDaysMax, MaxWeight,
                        MaxDimensions, IsActive, RequiresSignature, AllowCashOnDelivery,
                        AllowInsurance, InsuranceRate, TrackingUrl, CarrierName, CarrierCode,
                        ApiEndpoint, ApiKey, SupportedCountries, SupportedCities, SortOrder,
                        CreatedAt, CreatedBy
                    ) VALUES (
                        @Name, @Code, @Description, @ShippingType, @BaseCost, @CostPerKg, @CostPerKm,
                        @FreeShippingThreshold, @EstimatedDaysMin, @EstimatedDaysMax, @MaxWeight,
                        @MaxDimensions, @IsActive, @RequiresSignature, @AllowCashOnDelivery,
                        @AllowInsurance, @InsuranceRate, @TrackingUrl, @CarrierName, @CarrierCode,
                        @ApiEndpoint, @ApiKey, @SupportedCountries, @SupportedCities, @SortOrder,
                        @CreatedAt, @CreatedBy
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    shippingMethod.Name,
                    shippingMethod.Code,
                    shippingMethod.Description,
                    ShippingType = shippingMethod.ShippingType.ToString(),
                    shippingMethod.BaseCost,
                    shippingMethod.CostPerKg,
                    shippingMethod.CostPerKm,
                    shippingMethod.FreeShippingThreshold,
                    shippingMethod.EstimatedDaysMin,
                    shippingMethod.EstimatedDaysMax,
                    shippingMethod.MaxWeight,
                    shippingMethod.MaxDimensions,
                    shippingMethod.IsActive,
                    shippingMethod.RequiresSignature,
                    shippingMethod.AllowCashOnDelivery,
                    shippingMethod.AllowInsurance,
                    shippingMethod.InsuranceRate,
                    shippingMethod.TrackingUrl,
                    shippingMethod.CarrierName,
                    shippingMethod.CarrierCode,
                    shippingMethod.ApiEndpoint,
                    shippingMethod.ApiKey,
                    shippingMethod.SupportedCountries,
                    shippingMethod.SupportedCities,
                    shippingMethod.SortOrder,
                    CreatedAt = shippingMethod.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    shippingMethod.CreatedBy
                };

                var methodId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                shippingMethod.Id = methodId;

                LoggingService.LogInfo($"تم إنشاء طريقة شحن جديدة: {shippingMethod.Name}");
                return methodId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء طريقة الشحن: {shippingMethod?.Name}");
                throw;
            }
        }

        /// <summary>
        /// تحديث طريقة الشحن
        /// </summary>
        public async Task UpdateShippingMethodAsync(ShippingMethod shippingMethod)
        {
            try
            {
                shippingMethod.UpdatedAt = DateTime.Now;

                const string sql = @"
                    UPDATE ShippingMethods SET
                        Name = @Name,
                        Description = @Description,
                        BaseCost = @BaseCost,
                        CostPerKg = @CostPerKg,
                        CostPerKm = @CostPerKm,
                        FreeShippingThreshold = @FreeShippingThreshold,
                        EstimatedDaysMin = @EstimatedDaysMin,
                        EstimatedDaysMax = @EstimatedDaysMax,
                        MaxWeight = @MaxWeight,
                        MaxDimensions = @MaxDimensions,
                        IsActive = @IsActive,
                        RequiresSignature = @RequiresSignature,
                        AllowCashOnDelivery = @AllowCashOnDelivery,
                        AllowInsurance = @AllowInsurance,
                        InsuranceRate = @InsuranceRate,
                        SupportedCountries = @SupportedCountries,
                        SupportedCities = @SupportedCities,
                        SortOrder = @SortOrder,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(sql, new
                {
                    shippingMethod.Id,
                    shippingMethod.Name,
                    shippingMethod.Description,
                    shippingMethod.BaseCost,
                    shippingMethod.CostPerKg,
                    shippingMethod.CostPerKm,
                    shippingMethod.FreeShippingThreshold,
                    shippingMethod.EstimatedDaysMin,
                    shippingMethod.EstimatedDaysMax,
                    shippingMethod.MaxWeight,
                    shippingMethod.MaxDimensions,
                    shippingMethod.IsActive,
                    shippingMethod.RequiresSignature,
                    shippingMethod.AllowCashOnDelivery,
                    shippingMethod.AllowInsurance,
                    shippingMethod.InsuranceRate,
                    shippingMethod.SupportedCountries,
                    shippingMethod.SupportedCities,
                    shippingMethod.SortOrder,
                    UpdatedAt = shippingMethod.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                });

                LoggingService.LogInfo($"تم تحديث طريقة الشحن: {shippingMethod.Name}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث طريقة الشحن: {shippingMethod?.Name}");
                throw;
            }
        }

        #endregion

        #region Address Management

        /// <summary>
        /// إنشاء عنوان جديد
        /// </summary>
        public async Task<int> CreateAddressAsync(Address address)
        {
            try
            {
                const string sql = @"
                    INSERT INTO Addresses (
                        CustomerId, AddressType, FirstName, LastName, Company, AddressLine1,
                        AddressLine2, City, State, PostalCode, Country, Phone, Email,
                        IsDefault, IsActive, Notes, CreatedAt
                    ) VALUES (
                        @CustomerId, @AddressType, @FirstName, @LastName, @Company, @AddressLine1,
                        @AddressLine2, @City, @State, @PostalCode, @Country, @Phone, @Email,
                        @IsDefault, @IsActive, @Notes, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    address.CustomerId,
                    AddressType = address.AddressType.ToString(),
                    address.FirstName,
                    address.LastName,
                    address.Company,
                    address.AddressLine1,
                    address.AddressLine2,
                    address.City,
                    address.State,
                    address.PostalCode,
                    address.Country,
                    address.Phone,
                    address.Email,
                    address.IsDefault,
                    address.IsActive,
                    address.Notes,
                    CreatedAt = address.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var addressId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                address.Id = addressId;

                // إذا كان العنوان افتراضي، قم بإلغاء الافتراضية من العناوين الأخرى
                if (address.IsDefault)
                {
                    await SetDefaultAddressAsync(address.CustomerId, addressId, address.AddressType);
                }

                LoggingService.LogInfo($"تم إنشاء عنوان جديد للعميل: {address.CustomerId}");
                return addressId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء العنوان للعميل: {address?.CustomerId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على عناوين العميل
        /// </summary>
        public async Task<IEnumerable<Address>> GetCustomerAddressesAsync(int customerId, AddressType? addressType = null)
        {
            try
            {
                var sql = "SELECT * FROM Addresses WHERE CustomerId = @CustomerId AND IsActive = 1";
                var parameters = new Dictionary<string, object> { ["CustomerId"] = customerId };

                if (addressType.HasValue)
                {
                    sql += " AND (AddressType = @AddressType OR AddressType = 'Both')";
                    parameters["AddressType"] = addressType.ToString();
                }

                sql += " ORDER BY IsDefault DESC, CreatedAt DESC";

                var addressesData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return addressesData.Select(MapToAddress);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على عناوين العميل: {customerId}");
                throw;
            }
        }

        /// <summary>
        /// تعيين العنوان الافتراضي
        /// </summary>
        public async Task SetDefaultAddressAsync(int customerId, int addressId, AddressType addressType)
        {
            try
            {
                // إلغاء الافتراضية من جميع العناوين الأخرى
                const string resetSql = @"
                    UPDATE Addresses SET IsDefault = 0 
                    WHERE CustomerId = @CustomerId AND AddressType = @AddressType AND Id != @AddressId";

                await _dbService.ExecuteAsync(resetSql, new
                {
                    CustomerId = customerId,
                    AddressType = addressType.ToString(),
                    AddressId = addressId
                });

                // تعيين العنوان الجديد كافتراضي
                const string setSql = "UPDATE Addresses SET IsDefault = 1 WHERE Id = @AddressId";
                await _dbService.ExecuteAsync(setSql, new { AddressId = addressId });

                LoggingService.LogInfo($"تم تعيين العنوان الافتراضي للعميل: {customerId}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تعيين العنوان الافتراضي: {addressId}");
                throw;
            }
        }

        #endregion

        #region Shipping Calculation

        /// <summary>
        /// حساب تكلفة الشحن
        /// </summary>
        public async Task<ShippingQuote> CalculateShippingAsync(ShippingCalculationRequest request)
        {
            try
            {
                var availableMethods = await GetAvailableShippingMethodsAsync(
                    request.ShippingAddress, 
                    request.OrderTotal, 
                    request.TotalWeight);

                var quotes = new List<ShippingOption>();

                foreach (var method in availableMethods)
                {
                    var cost = method.CalculateShippingCost(request.OrderTotal, request.TotalWeight, request.Distance);
                    var insuranceCost = request.RequireInsurance ? method.CalculateInsuranceCost(request.OrderTotal) : 0;
                    var estimatedDelivery = method.EstimateDeliveryDate(DateTime.Now);

                    quotes.Add(new ShippingOption
                    {
                        MethodId = method.Id,
                        MethodName = method.Name,
                        MethodCode = method.Code,
                        ShippingType = method.ShippingType,
                        Cost = cost,
                        InsuranceCost = insuranceCost,
                        TotalCost = cost + insuranceCost,
                        EstimatedDelivery = estimatedDelivery,
                        EstimatedDays = method.EstimatedDaysMax,
                        RequiresSignature = method.RequiresSignature,
                        AllowCashOnDelivery = method.AllowCashOnDelivery,
                        HasTracking = method.HasTracking
                    });
                }

                return new ShippingQuote
                {
                    IsSuccess = quotes.Any(),
                    Options = quotes.OrderBy(q => q.TotalCost).ToList(),
                    ErrorMessage = quotes.Any() ? null : "لا توجد طرق شحن متاحة لهذا العنوان"
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حساب تكلفة الشحن");
                return new ShippingQuote
                {
                    IsSuccess = false,
                    ErrorMessage = "حدث خطأ في حساب تكلفة الشحن"
                };
            }
        }

        /// <summary>
        /// إنشاء شحنة جديدة
        /// </summary>
        public async Task<int> CreateShipmentAsync(Shipment shipment)
        {
            try
            {
                shipment.TrackingNumber = await GenerateTrackingNumberAsync();

                const string sql = @"
                    INSERT INTO Shipments (
                        OrderId, ShippingMethodId, TrackingNumber, Status, ShippingCost,
                        InsuranceCost, TotalCost, ShippingAddress, EstimatedDelivery,
                        ActualDelivery, Notes, CreatedAt, CreatedBy
                    ) VALUES (
                        @OrderId, @ShippingMethodId, @TrackingNumber, @Status, @ShippingCost,
                        @InsuranceCost, @TotalCost, @ShippingAddress, @EstimatedDelivery,
                        @ActualDelivery, @Notes, @CreatedAt, @CreatedBy
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    shipment.OrderId,
                    shipment.ShippingMethodId,
                    shipment.TrackingNumber,
                    Status = shipment.Status.ToString(),
                    shipment.ShippingCost,
                    shipment.InsuranceCost,
                    shipment.TotalCost,
                    shipment.ShippingAddress,
                    EstimatedDelivery = shipment.EstimatedDelivery?.ToString("yyyy-MM-dd HH:mm:ss"),
                    ActualDelivery = shipment.ActualDelivery?.ToString("yyyy-MM-dd HH:mm:ss"),
                    shipment.Notes,
                    CreatedAt = shipment.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    shipment.CreatedBy
                };

                var shipmentId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                shipment.Id = shipmentId;

                // إرسال إشعار
                await _notificationService.SendNotificationAsync(
                    "شحنة جديدة",
                    $"تم إنشاء شحنة جديدة برقم التتبع: {shipment.TrackingNumber}",
                    NotificationType.Info
                );

                LoggingService.LogInfo($"تم إنشاء شحنة جديدة: {shipment.TrackingNumber}");
                return shipmentId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء الشحنة للطلب: {shipment?.OrderId}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private async Task<string> GenerateTrackingNumberAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM Shipments WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);
                
                var today = DateTime.Now;
                return $"TRK{today:yyyyMMdd}{(todayCount + 1):D6}";
            }
            catch
            {
                return $"TRK{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        private ShippingMethod MapToShippingMethod(dynamic data)
        {
            var method = new ShippingMethod
            {
                Id = data.Id,
                Name = data.Name ?? "",
                Code = data.Code ?? "",
                Description = data.Description ?? "",
                BaseCost = data.BaseCost,
                CostPerKg = data.CostPerKg,
                CostPerKm = data.CostPerKm,
                FreeShippingThreshold = data.FreeShippingThreshold,
                EstimatedDaysMin = data.EstimatedDaysMin,
                EstimatedDaysMax = data.EstimatedDaysMax,
                MaxWeight = data.MaxWeight,
                MaxDimensions = data.MaxDimensions,
                IsActive = data.IsActive,
                RequiresSignature = data.RequiresSignature,
                AllowCashOnDelivery = data.AllowCashOnDelivery,
                AllowInsurance = data.AllowInsurance,
                InsuranceRate = data.InsuranceRate,
                TrackingUrl = data.TrackingUrl ?? "",
                CarrierName = data.CarrierName ?? "",
                CarrierCode = data.CarrierCode ?? "",
                ApiEndpoint = data.ApiEndpoint ?? "",
                ApiKey = data.ApiKey ?? "",
                SupportedCountries = data.SupportedCountries ?? "",
                SupportedCities = data.SupportedCities ?? "",
                SortOrder = data.SortOrder,
                CreatedBy = data.CreatedBy ?? ""
            };

            // Parse enums
            if (Enum.TryParse<ShippingType>(data.ShippingType?.ToString(), out var shippingType))
                method.ShippingType = shippingType;

            // Parse dates
            if (DateTime.TryParse(data.CreatedAt?.ToString(), out var createdAt))
                method.CreatedAt = createdAt;

            if (DateTime.TryParse(data.UpdatedAt?.ToString(), out var updatedAt))
                method.UpdatedAt = updatedAt;

            return method;
        }

        private Address MapToAddress(dynamic data)
        {
            var address = new Address
            {
                Id = data.Id,
                CustomerId = data.CustomerId,
                FirstName = data.FirstName ?? "",
                LastName = data.LastName ?? "",
                Company = data.Company ?? "",
                AddressLine1 = data.AddressLine1 ?? "",
                AddressLine2 = data.AddressLine2 ?? "",
                City = data.City ?? "",
                State = data.State ?? "",
                PostalCode = data.PostalCode ?? "",
                Country = data.Country ?? "",
                Phone = data.Phone ?? "",
                Email = data.Email ?? "",
                IsDefault = data.IsDefault,
                IsActive = data.IsActive,
                Notes = data.Notes ?? ""
            };

            // Parse enums
            if (Enum.TryParse<AddressType>(data.AddressType?.ToString(), out var addressType))
                address.AddressType = addressType;

            // Parse dates
            if (DateTime.TryParse(data.CreatedAt?.ToString(), out var createdAt))
                address.CreatedAt = createdAt;

            if (DateTime.TryParse(data.UpdatedAt?.ToString(), out var updatedAt))
                address.UpdatedAt = updatedAt;

            return address;
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// طلب حساب الشحن
    /// </summary>
    public class ShippingCalculationRequest
    {
        public Address ShippingAddress { get; set; } = new();
        public decimal OrderTotal { get; set; }
        public decimal TotalWeight { get; set; }
        public decimal Distance { get; set; }
        public bool RequireInsurance { get; set; }
    }

    /// <summary>
    /// عرض أسعار الشحن
    /// </summary>
    public class ShippingQuote
    {
        public bool IsSuccess { get; set; }
        public List<ShippingOption> Options { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// خيار الشحن
    /// </summary>
    public class ShippingOption
    {
        public int MethodId { get; set; }
        public string MethodName { get; set; } = string.Empty;
        public string MethodCode { get; set; } = string.Empty;
        public ShippingType ShippingType { get; set; }
        public decimal Cost { get; set; }
        public decimal InsuranceCost { get; set; }
        public decimal TotalCost { get; set; }
        public DateTime EstimatedDelivery { get; set; }
        public int EstimatedDays { get; set; }
        public bool RequiresSignature { get; set; }
        public bool AllowCashOnDelivery { get; set; }
        public bool HasTracking { get; set; }

        public string FormattedCost => $"{TotalCost:C}";
        public string FormattedEstimatedDelivery => EstimatedDelivery.ToString("dd/MM/yyyy");
    }

    /// <summary>
    /// الشحنة
    /// </summary>
    public class Shipment
    {
        public int Id { get; set; }
        public string OrderId { get; set; } = string.Empty;
        public int ShippingMethodId { get; set; }
        public string TrackingNumber { get; set; } = string.Empty;
        public ShipmentStatus Status { get; set; } = ShipmentStatus.Pending;
        public decimal ShippingCost { get; set; }
        public decimal InsuranceCost { get; set; }
        public decimal TotalCost { get; set; }
        public string ShippingAddress { get; set; } = string.Empty;
        public DateTime? EstimatedDelivery { get; set; }
        public DateTime? ActualDelivery { get; set; }
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// حالة الشحنة
    /// </summary>
    public enum ShipmentStatus
    {
        Pending,        // في الانتظار
        Processing,     // قيد المعالجة
        Shipped,        // تم الشحن
        InTransit,      // في الطريق
        OutForDelivery, // خارج للتسليم
        Delivered,      // تم التسليم
        Failed,         // فشل التسليم
        Returned        // مرتجع
    }

    #endregion
}
