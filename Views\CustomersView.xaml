<UserControl x:Class="SalesManagementSystem.Views.CustomersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0" 
                        materialDesign:HintAssist.Hint="{DynamicResource Search}"
                        materialDesign:TextFieldAssist.HasClearButton="True"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Margin="0,0,15,0"
                        TextChanged="SearchTextBox_TextChanged"/>

                <!-- Add Customer Button -->
                <Button x:Name="AddCustomerButton" Grid.Column="1" 
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Click="AddCustomerButton_Click"
                       Margin="0,0,10,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountPlus" Margin="0,0,5,0"/>
                        <TextBlock Text="{DynamicResource AddCustomer}"/>
                    </StackPanel>
                </Button>

                <!-- Refresh Button -->
                <Button x:Name="RefreshButton" Grid.Column="2" 
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Click="RefreshButton_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Customers DataGrid -->
        <materialDesign:Card Grid.Row="1" Padding="0">
            <DataGrid x:Name="CustomersDataGrid" 
                     AutoGenerateColumns="False"
                     CanUserAddRows="False"
                     CanUserDeleteRows="False"
                     IsReadOnly="True"
                     SelectionMode="Single"
                     GridLinesVisibility="Horizontal"
                     HeadersVisibility="Column"
                     materialDesign:DataGridAssist.CellPadding="8"
                     materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                
                <DataGrid.Columns>
                    <!-- Customer Name -->
                    <DataGridTextColumn Header="{DynamicResource CustomerName}" 
                                      Binding="{Binding Name}" 
                                      Width="*"/>
                    
                    <!-- Phone -->
                    <DataGridTextColumn Header="{DynamicResource Phone}" 
                                      Binding="{Binding Phone}" 
                                      Width="150"/>
                    
                    <!-- Email -->
                    <DataGridTextColumn Header="{DynamicResource Email}" 
                                      Binding="{Binding Email}" 
                                      Width="200"/>
                    
                    <!-- Address -->
                    <DataGridTextColumn Header="{DynamicResource Address}" 
                                      Binding="{Binding Address}" 
                                      Width="*"/>
                    
                    <!-- Total Purchases -->
                    <DataGridTextColumn Header="{DynamicResource TotalPurchases}" 
                                      Binding="{Binding TotalPurchases, StringFormat=C}" 
                                      Width="120"/>
                    
                    <!-- Actions -->
                    <DataGridTemplateColumn Header="{DynamicResource Actions}" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="{DynamicResource Edit}"
                                           Click="EditCustomer_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Pencil" Foreground="{StaticResource PrimaryHueMidBrush}"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="{DynamicResource Delete}"
                                           Click="DeleteCustomer_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="Delete" Foreground="#F44336"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}" 
                                           ToolTip="عرض المشتريات"
                                           Click="ViewPurchases_Click"
                                           Tag="{Binding}">
                                        <materialDesign:PackIcon Kind="ShoppingCart" Foreground="#4CAF50"/>
                                    </Button>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
