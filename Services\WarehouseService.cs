using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة المخازن المتعددة
    /// </summary>
    public class WarehouseService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;

        public WarehouseService(DatabaseService dbService, NotificationService notificationService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        }

        #region Warehouse Management

        /// <summary>
        /// إضافة مخزن جديد
        /// </summary>
        public async Task<int> AddWarehouseAsync(Warehouse warehouse)
        {
            try
            {
                // توليد كود المخزن إذا لم يكن موجود
                if (string.IsNullOrEmpty(warehouse.Code))
                {
                    warehouse.Code = await GenerateWarehouseCodeAsync();
                }

                const string sql = @"
                    INSERT INTO Warehouses (
                        Code, Name, Description, Type, Status, Address, City, Phone, Email,
                        ManagerName, TotalCapacity, UsedCapacity, AvailableCapacity, IsDefault,
                        Notes, CreatedBy, CreatedAt
                    ) VALUES (
                        @Code, @Name, @Description, @Type, @Status, @Address, @City, @Phone, @Email,
                        @ManagerName, @TotalCapacity, @UsedCapacity, @AvailableCapacity, @IsDefault,
                        @Notes, @CreatedBy, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    warehouse.Code,
                    warehouse.Name,
                    warehouse.Description,
                    Type = warehouse.Type.ToString(),
                    Status = warehouse.Status.ToString(),
                    warehouse.Address,
                    warehouse.City,
                    warehouse.Phone,
                    warehouse.Email,
                    warehouse.ManagerName,
                    warehouse.TotalCapacity,
                    warehouse.UsedCapacity,
                    warehouse.AvailableCapacity,
                    warehouse.IsDefault,
                    warehouse.Notes,
                    warehouse.CreatedBy,
                    CreatedAt = warehouse.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var warehouseId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                warehouse.Id = warehouseId;

                // إرسال إشعار
                await SendWarehouseNotificationAsync(warehouse, "تم إضافة مخزن جديد");

                LoggingService.LogInfo($"تم إضافة مخزن جديد: {warehouse.Name}");
                return warehouseId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة المخزن: {warehouse?.Name}");
                throw;
            }
        }

        /// <summary>
        /// تحديث مخزن
        /// </summary>
        public async Task<bool> UpdateWarehouseAsync(Warehouse warehouse)
        {
            try
            {
                warehouse.UpdatedAt = DateTime.Now;

                const string sql = @"
                    UPDATE Warehouses SET
                        Name = @Name, Description = @Description, Type = @Type, Status = @Status,
                        Address = @Address, City = @City, Phone = @Phone, Email = @Email,
                        ManagerName = @ManagerName, TotalCapacity = @TotalCapacity,
                        UsedCapacity = @UsedCapacity, AvailableCapacity = @AvailableCapacity,
                        IsDefault = @IsDefault, Notes = @Notes, UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                var parameters = new
                {
                    warehouse.Id,
                    warehouse.Name,
                    warehouse.Description,
                    Type = warehouse.Type.ToString(),
                    Status = warehouse.Status.ToString(),
                    warehouse.Address,
                    warehouse.City,
                    warehouse.Phone,
                    warehouse.Email,
                    warehouse.ManagerName,
                    warehouse.TotalCapacity,
                    warehouse.UsedCapacity,
                    warehouse.AvailableCapacity,
                    warehouse.IsDefault,
                    warehouse.Notes,
                    UpdatedAt = warehouse.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم تحديث المخزن: {warehouse.Name}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث المخزن: {warehouse?.Id}");
                throw;
            }
        }

        /// <summary>
        /// حذف مخزن
        /// </summary>
        public async Task<bool> DeleteWarehouseAsync(int warehouseId)
        {
            try
            {
                // التحقق من وجود مخزون في المخزن
                const string checkSql = "SELECT COUNT(*) FROM InventoryItems WHERE WarehouseId = @WarehouseId";
                var inventoryCount = await _dbService.QuerySingleAsync<int>(checkSql, new { WarehouseId = warehouseId });

                if (inventoryCount > 0)
                {
                    throw new InvalidOperationException("لا يمكن حذف المخزن لوجود مخزون به");
                }

                // حذف المواقع أولاً
                await _dbService.ExecuteAsync("DELETE FROM WarehouseLocations WHERE WarehouseId = @WarehouseId", 
                    new { WarehouseId = warehouseId });

                // حذف المخزن
                const string sql = "DELETE FROM Warehouses WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = warehouseId });

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم حذف المخزن: {warehouseId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف المخزن: {warehouseId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مخزن بالمعرف
        /// </summary>
        public async Task<Warehouse?> GetWarehouseByIdAsync(int warehouseId)
        {
            try
            {
                const string sql = "SELECT * FROM Warehouses WHERE Id = @Id";
                var warehouseData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = warehouseId });

                if (warehouseData != null)
                {
                    var warehouse = MapToWarehouse(warehouseData);
                    
                    // تحميل المواقع
                    warehouse.Locations = new System.Collections.ObjectModel.ObservableCollection<WarehouseLocation>(
                        await GetWarehouseLocationsAsync(warehouseId));

                    // تحميل المخزون
                    warehouse.Inventory = new System.Collections.ObjectModel.ObservableCollection<InventoryItem>(
                        await GetWarehouseInventoryAsync(warehouseId));

                    return warehouse;
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على المخزن: {warehouseId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع المخازن
        /// </summary>
        public async Task<IEnumerable<Warehouse>> GetAllWarehousesAsync()
        {
            try
            {
                const string sql = "SELECT * FROM Warehouses ORDER BY IsDefault DESC, Name ASC";
                var warehousesData = await _dbService.QueryAsync<dynamic>(sql);
                return warehousesData.Select(MapToWarehouse);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المخازن");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المخازن النشطة
        /// </summary>
        public async Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync()
        {
            try
            {
                const string sql = "SELECT * FROM Warehouses WHERE Status = 'Active' ORDER BY IsDefault DESC, Name ASC";
                var warehousesData = await _dbService.QueryAsync<dynamic>(sql);
                return warehousesData.Select(MapToWarehouse);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المخازن النشطة");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المخزن الافتراضي
        /// </summary>
        public async Task<Warehouse?> GetDefaultWarehouseAsync()
        {
            try
            {
                const string sql = "SELECT * FROM Warehouses WHERE IsDefault = 1 LIMIT 1";
                var warehouseData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql);

                if (warehouseData != null)
                {
                    return MapToWarehouse(warehouseData);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المخزن الافتراضي");
                throw;
            }
        }

        /// <summary>
        /// تعيين مخزن كافتراضي
        /// </summary>
        public async Task<bool> SetDefaultWarehouseAsync(int warehouseId)
        {
            try
            {
                // إزالة الافتراضي من جميع المخازن
                await _dbService.ExecuteAsync("UPDATE Warehouses SET IsDefault = 0");

                // تعيين المخزن الجديد كافتراضي
                const string sql = "UPDATE Warehouses SET IsDefault = 1 WHERE Id = @Id";
                var rowsAffected = await _dbService.ExecuteAsync(sql, new { Id = warehouseId });

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم تعيين المخزن {warehouseId} كافتراضي");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تعيين المخزن الافتراضي: {warehouseId}");
                throw;
            }
        }

        /// <summary>
        /// البحث في المخازن
        /// </summary>
        public async Task<IEnumerable<Warehouse>> SearchWarehousesAsync(string searchTerm, WarehouseStatus? status = null, WarehouseType? type = null)
        {
            try
            {
                var sql = @"
                    SELECT * FROM Warehouses 
                    WHERE (Code LIKE @SearchTerm OR Name LIKE @SearchTerm OR City LIKE @SearchTerm)";

                var parameters = new Dictionary<string, object>
                {
                    ["SearchTerm"] = $"%{searchTerm}%"
                };

                if (status.HasValue)
                {
                    sql += " AND Status = @Status";
                    parameters["Status"] = status.ToString();
                }

                if (type.HasValue)
                {
                    sql += " AND Type = @Type";
                    parameters["Type"] = type.ToString();
                }

                sql += " ORDER BY IsDefault DESC, Name ASC";

                var warehousesData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return warehousesData.Select(MapToWarehouse);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في البحث في المخازن: {searchTerm}");
                throw;
            }
        }

        #endregion

        #region Warehouse Locations

        /// <summary>
        /// إضافة موقع مخزن
        /// </summary>
        public async Task<int> AddWarehouseLocationAsync(WarehouseLocation location)
        {
            try
            {
                // توليد كود الموقع إذا لم يكن موجود
                if (string.IsNullOrEmpty(location.Code))
                {
                    location.Code = await GenerateLocationCodeAsync(location.WarehouseId);
                }

                // توليد الباركود
                location.GenerateBarcode();

                const string sql = @"
                    INSERT INTO WarehouseLocations (
                        WarehouseId, Code, Name, Description, Type, Status, Aisle, Rack, Shelf, Bin,
                        Capacity, UsedCapacity, AvailableCapacity, Barcode, Notes, CreatedAt
                    ) VALUES (
                        @WarehouseId, @Code, @Name, @Description, @Type, @Status, @Aisle, @Rack, @Shelf, @Bin,
                        @Capacity, @UsedCapacity, @AvailableCapacity, @Barcode, @Notes, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    location.WarehouseId,
                    location.Code,
                    location.Name,
                    location.Description,
                    Type = location.Type.ToString(),
                    Status = location.Status.ToString(),
                    location.Aisle,
                    location.Rack,
                    location.Shelf,
                    location.Bin,
                    location.Capacity,
                    location.UsedCapacity,
                    location.AvailableCapacity,
                    location.Barcode,
                    location.Notes,
                    CreatedAt = location.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var locationId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                location.Id = locationId;

                LoggingService.LogInfo($"تم إضافة موقع مخزن جديد: {location.Name}");
                return locationId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة موقع المخزن: {location?.Name}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مواقع المخزن
        /// </summary>
        public async Task<IEnumerable<WarehouseLocation>> GetWarehouseLocationsAsync(int warehouseId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM WarehouseLocations 
                    WHERE WarehouseId = @WarehouseId 
                    ORDER BY Aisle, Rack, Shelf, Bin";

                var locationsData = await _dbService.QueryAsync<dynamic>(sql, new { WarehouseId = warehouseId });
                return locationsData.Select(MapToWarehouseLocation);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على مواقع المخزن: {warehouseId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مخزون المخزن
        /// </summary>
        public async Task<IEnumerable<InventoryItem>> GetWarehouseInventoryAsync(int warehouseId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InventoryItems 
                    WHERE WarehouseId = @WarehouseId 
                    ORDER BY LastUpdated DESC";

                var inventoryData = await _dbService.QueryAsync<dynamic>(sql, new { WarehouseId = warehouseId });
                return inventoryData.Select(MapToInventoryItem);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على مخزون المخزن: {warehouseId}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// توليد كود مخزن جديد
        /// </summary>
        private async Task<string> GenerateWarehouseCodeAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM Warehouses";
                var count = await _dbService.QuerySingleAsync<int>(sql);
                return $"WH{(count + 1):D3}";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد كود المخزن");
                return $"WH{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// توليد كود موقع جديد
        /// </summary>
        private async Task<string> GenerateLocationCodeAsync(int warehouseId)
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM WarehouseLocations WHERE WarehouseId = @WarehouseId";
                var count = await _dbService.QuerySingleAsync<int>(sql, new { WarehouseId = warehouseId });
                return $"LOC{warehouseId:D3}{(count + 1):D3}";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد كود الموقع");
                return $"LOC{warehouseId:D3}{DateTime.Now:HHmmss}";
            }
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج المخزن
        /// </summary>
        private Warehouse MapToWarehouse(dynamic data)
        {
            return new Warehouse
            {
                Id = data.Id,
                Code = data.Code ?? "",
                Name = data.Name ?? "",
                Description = data.Description ?? "",
                Type = Enum.TryParse<WarehouseType>(data.Type, out var type) ? type : WarehouseType.Main,
                Status = Enum.TryParse<WarehouseStatus>(data.Status, out var status) ? status : WarehouseStatus.Active,
                Address = data.Address ?? "",
                City = data.City ?? "",
                Phone = data.Phone ?? "",
                Email = data.Email ?? "",
                ManagerName = data.ManagerName ?? "",
                TotalCapacity = data.TotalCapacity ?? 0,
                UsedCapacity = data.UsedCapacity ?? 0,
                AvailableCapacity = data.AvailableCapacity ?? 0,
                IsDefault = data.IsDefault == 1,
                Notes = data.Notes ?? "",
                CreatedBy = data.CreatedBy ?? "",
                CreatedAt = DateTime.TryParse(data.CreatedAt, out var createdAt) ? createdAt : DateTime.Now,
                UpdatedAt = DateTime.TryParse(data.UpdatedAt, out var updatedAt) ? updatedAt : null
            };
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج موقع المخزن
        /// </summary>
        private WarehouseLocation MapToWarehouseLocation(dynamic data)
        {
            return new WarehouseLocation
            {
                Id = data.Id,
                WarehouseId = data.WarehouseId,
                Code = data.Code ?? "",
                Name = data.Name ?? "",
                Description = data.Description ?? "",
                Type = Enum.TryParse<LocationType>(data.Type, out var type) ? type : LocationType.Shelf,
                Status = Enum.TryParse<LocationStatus>(data.Status, out var status) ? status : LocationStatus.Available,
                Aisle = data.Aisle ?? "",
                Rack = data.Rack ?? "",
                Shelf = data.Shelf ?? "",
                Bin = data.Bin ?? "",
                Capacity = data.Capacity ?? 0,
                UsedCapacity = data.UsedCapacity ?? 0,
                AvailableCapacity = data.AvailableCapacity ?? 0,
                Barcode = data.Barcode ?? "",
                Notes = data.Notes ?? "",
                CreatedAt = DateTime.TryParse(data.CreatedAt, out var createdAt) ? createdAt : DateTime.Now,
                UpdatedAt = DateTime.TryParse(data.UpdatedAt, out var updatedAt) ? updatedAt : null
            };
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج عنصر المخزون
        /// </summary>
        private InventoryItem MapToInventoryItem(dynamic data)
        {
            return new InventoryItem
            {
                Id = data.Id,
                ProductId = data.ProductId,
                WarehouseId = data.WarehouseId,
                LocationId = data.LocationId,
                Quantity = data.Quantity ?? 0,
                UnitCost = data.UnitCost ?? 0,
                TotalValue = data.TotalValue ?? 0,
                ReservedQuantity = data.ReservedQuantity ?? 0,
                AvailableQuantity = data.AvailableQuantity ?? 0,
                MinStockLevel = data.MinStockLevel ?? 0,
                MaxStockLevel = data.MaxStockLevel ?? 0,
                ReorderPoint = data.ReorderPoint ?? 0,
                Volume = data.Volume ?? 0,
                Weight = data.Weight ?? 0,
                BatchNumber = data.BatchNumber ?? "",
                SerialNumber = data.SerialNumber ?? "",
                ExpiryDate = DateTime.TryParse(data.ExpiryDate, out var expiryDate) ? expiryDate : null,
                LastUpdated = DateTime.TryParse(data.LastUpdated, out var lastUpdated) ? lastUpdated : DateTime.Now,
                Notes = data.Notes ?? ""
            };
        }

        /// <summary>
        /// إرسال إشعار المخزن
        /// </summary>
        private async Task SendWarehouseNotificationAsync(Warehouse warehouse, string message)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "إشعار مخزن",
                    Message = $"{message}: {warehouse.Name}",
                    Type = "Warehouse",
                    Priority = "Normal",
                    ActionUrl = $"/warehouses/{warehouse.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار المخزن");
            }
        }

        #endregion
    }
}
