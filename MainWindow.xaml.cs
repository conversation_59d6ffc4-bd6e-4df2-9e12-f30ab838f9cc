using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using MahApps.Metro.Controls;
using SalesManagementSystem.Views;
using SalesManagementSystem.Services;
using SalesManagementSystem.Models;

namespace SalesManagementSystem
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : MetroWindow, INotifyPropertyChanged
    {
        private bool _isDarkTheme = false;
        private NotificationService _notificationService = null!;
        private ToastManager _toastManager = null!;
        private int _unreadNotificationCount;
        private bool _hasUnreadNotifications;

        public int UnreadNotificationCount
        {
            get => _unreadNotificationCount;
            set
            {
                if (_unreadNotificationCount != value)
                {
                    _unreadNotificationCount = value;
                    OnPropertyChanged();
                    HasUnreadNotifications = value > 0;
                }
            }
        }

        public bool HasUnreadNotifications
        {
            get => _hasUnreadNotifications;
            set
            {
                if (_hasUnreadNotifications != value)
                {
                    _hasUnreadNotifications = value;
                    OnPropertyChanged();
                }
            }
        }

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            // Initialize notification services
            InitializeNotificationServices();

            // Set initial page
            NavigateTo("Dashboard");

            // Apply styles
            ApplyStyles();
        }

        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button)
            {
                string pageName = button.Name.Replace("Button", "");
                NavigateTo(pageName);
            }
        }

        private void NavigateTo(string pageName)
        {
            // Update page title
            PageTitle.Text = (string)FindResource(pageName);

            // Navigate to the corresponding page
            UserControl? page = pageName switch
            {
                "Dashboard" => new DashboardView(),
                "Products" => new ProductsView(),
                "Sales" => new SalesView(),
                "Customers" => new CustomersView(),
                "Suppliers" => new SuppliersView(),
                "Employees" => new EmployeesView(),
                "Expenses" => new ExpensesView(),
                "Reports" => new ReportsView(),
                "Settings" => new SettingsView(),
                _ => new DashboardView()
            };

            if (page != null)
            {
                MainFrame.Content = page;
            }
        }

        private UserControl CreatePlaceholderView(string text)
        {
            var userControl = new UserControl();
            userControl.Content = new TextBlock
            {
                Text = text,
                FontSize = 24,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            return userControl;
        }

        public void NavigateToPage(string pageName)
        {
            NavigateTo(pageName);
        }

        private void ToggleLanguage_Click(object sender, RoutedEventArgs e)
        {
            // Toggle between Arabic and English
            App.SetApplicationCulture(App.IsArabic ? "en-US" : "ar-SA");

            // Update page title after language change
            string currentPage = PageTitle.Text;
            foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
            {
                foreach (object key in dict.Keys)
                {
                    if (dict[key] as string == currentPage)
                    {
                        PageTitle.Text = (string)FindResource(key);
                        break;
                    }
                }
            }
        }

        private void ToggleTheme_Click(object sender, RoutedEventArgs e)
        {
            _isDarkTheme = !_isDarkTheme;

            // Change theme
            ResourceDictionary themeDictionary = new ResourceDictionary
            {
                Source = new Uri($"/Resources/Themes/{(_isDarkTheme ? "Dark" : "Light")}Theme.xaml", UriKind.Relative)
            };

            // Replace the theme dictionary
            ResourceDictionary? oldDictionary = null;
            foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Themes/"))
                {
                    oldDictionary = dict;
                    break;
                }
            }

            if (oldDictionary != null)
            {
                int index = Application.Current.Resources.MergedDictionaries.IndexOf(oldDictionary);
                Application.Current.Resources.MergedDictionaries.Remove(oldDictionary);
                Application.Current.Resources.MergedDictionaries.Insert(index, themeDictionary);
            }
            else
            {
                Application.Current.Resources.MergedDictionaries.Add(themeDictionary);
            }
        }

        private void ApplyStyles()
        {
            // Add menu button style
            Style menuButtonStyle = new Style(typeof(Button));
            menuButtonStyle.Setters.Add(new Setter(Button.BackgroundProperty, System.Windows.Media.Brushes.Transparent));
            menuButtonStyle.Setters.Add(new Setter(Button.ForegroundProperty, System.Windows.Media.Brushes.White));
            menuButtonStyle.Setters.Add(new Setter(Button.BorderThicknessProperty, new Thickness(0)));
            menuButtonStyle.Setters.Add(new Setter(Button.HeightProperty, 50.0));
            menuButtonStyle.Setters.Add(new Setter(Button.FontSizeProperty, 14.0));
            menuButtonStyle.Setters.Add(new Setter(Button.HorizontalContentAlignmentProperty, HorizontalAlignment.Left));
            menuButtonStyle.Setters.Add(new Setter(Button.PaddingProperty, new Thickness(20, 0, 0, 0)));
            menuButtonStyle.Setters.Add(new Setter(Button.MarginProperty, new Thickness(0, 5, 0, 0)));

            // Add hover trigger
            Trigger hoverTrigger = new Trigger { Property = Button.IsMouseOverProperty, Value = true };
            hoverTrigger.Setters.Add(new Setter(Button.BackgroundProperty, new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(50, 255, 255, 255))));
            menuButtonStyle.Triggers.Add(hoverTrigger);

            // Register the style
            this.Resources.Add("MenuButtonStyle", menuButtonStyle);
        }

        #region Notification Methods

        private void InitializeNotificationServices()
        {
            try
            {
                var dbService = new DatabaseService();
                var settingsService = new SettingsService(dbService);

                // إنشاء خدمة الإشعارات
                _notificationService = new NotificationService(dbService, settingsService);

                // إنشاء مدير Toast
                _toastManager = new ToastManager(ToastContainer);

                // ربط الأحداث
                _notificationService.NotificationReceived += OnNotificationReceived;
                _notificationService.NotificationsChanged += OnNotificationsChanged;
                _notificationService.ToastNotificationRequested += OnToastNotificationRequested;
                _toastManager.ActionRequested += OnToastActionRequested;

                // تهيئة الخدمة
                _ = _notificationService.InitializeAsync();

                // تحديث العداد
                UpdateNotificationCount();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تهيئة خدمات الإشعارات");
            }
        }

        private void OnNotificationReceived(object? sender, Notification notification)
        {
            UpdateNotificationCount();
        }

        private void OnNotificationsChanged(object? sender, EventArgs e)
        {
            UpdateNotificationCount();
        }

        private void OnToastNotificationRequested(object? sender, NotificationToastEventArgs e)
        {
            _toastManager.ShowToast(e.Notification);
        }

        private void OnToastActionRequested(object? sender, ToastActionEventArgs e)
        {
            try
            {
                switch (e.Action)
                {
                    case ToastAction.View:
                        // فتح الإشعار أو تنفيذ الإجراء
                        if (!string.IsNullOrEmpty(e.Notification.ActionUrl))
                        {
                            // تنفيذ الإجراء المطلوب
                            HandleNotificationAction(e.Notification);
                        }
                        break;

                    case ToastAction.Snooze:
                        // تأجيل الإشعار
                        ShowSnoozeDialog(e.Notification);
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تنفيذ إجراء Toast: {e.Notification.Id}");
            }
        }

        private void HandleNotificationAction(Notification notification)
        {
            // تنفيذ الإجراءات حسب نوع الإشعار
            switch (notification.Type?.ToLower())
            {
                case "lowstock":
                case "outofstock":
                    NavigateTo("Products");
                    break;
                case "paymentdue":
                    NavigateTo("Customers");
                    break;
                case "supplierpaymentdue":
                    NavigateTo("Suppliers");
                    break;
                default:
                    // فتح مركز الإشعارات
                    NotificationCenterPopup.IsOpen = true;
                    break;
            }
        }

        private void ShowSnoozeDialog(Notification notification)
        {
            try
            {
                var snoozeDialog = new SnoozeNotificationDialog(notification);
                snoozeDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في عرض نافذة التأجيل");
                MessageBox.Show("خطأ في عرض نافذة التأجيل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void UpdateNotificationCount()
        {
            try
            {
                var count = await _notificationService.GetUnreadNotificationCountAsync();
                UnreadNotificationCount = count;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث عداد الإشعارات");
            }
        }

        private void NotificationCenter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                NotificationCenterPopup.IsOpen = !NotificationCenterPopup.IsOpen;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فتح مركز الإشعارات");
                MessageBox.Show("خطأ في فتح مركز الإشعارات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region Cleanup

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // تنظيف الموارد
                if (_notificationService != null)
                {
                    _notificationService.NotificationReceived -= OnNotificationReceived;
                    _notificationService.NotificationsChanged -= OnNotificationsChanged;
                    _notificationService.ToastNotificationRequested -= OnToastNotificationRequested;
                    _notificationService.Dispose();
                }

                if (_toastManager != null)
                {
                    _toastManager.ActionRequested -= OnToastActionRequested;
                    _toastManager.Dispose();
                }

                NotificationCenterControl?.Dispose();
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تنظيف موارد النافذة الرئيسية");
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        #endregion
    }
}