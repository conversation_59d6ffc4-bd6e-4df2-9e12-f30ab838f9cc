using System;
using System.Windows;
using System.Windows.Controls;
using MahApps.Metro.Controls;
using SalesManagementSystem.Views;

namespace SalesManagementSystem
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : MetroWindow
    {
        private bool _isDarkTheme = false;

        public MainWindow()
        {
            InitializeComponent();

            // Set initial page
            NavigateTo("Dashboard");

            // Apply styles
            ApplyStyles();
        }

        private void NavigationButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button)
            {
                string pageName = button.Name.Replace("Button", "");
                NavigateTo(pageName);
            }
        }

        private void NavigateTo(string pageName)
        {
            // Update page title
            PageTitle.Text = (string)FindResource(pageName);

            // Navigate to the corresponding page
            UserControl? page = pageName switch
            {
                "Dashboard" => new DashboardView(),
                "Products" => new ProductsView(),
                "Sales" => new SalesView(),
                "Customers" => new CustomersView(),
                "Suppliers" => new SuppliersView(),
                "Employees" => new EmployeesView(),
                "Expenses" => new ExpensesView(),
                "Reports" => new ReportsView(),
                "Settings" => new SettingsView(),
                _ => new DashboardView()
            };

            if (page != null)
            {
                MainFrame.Content = page;
            }
        }

        private UserControl CreatePlaceholderView(string text)
        {
            var userControl = new UserControl();
            userControl.Content = new TextBlock
            {
                Text = text,
                FontSize = 24,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            return userControl;
        }

        public void NavigateToPage(string pageName)
        {
            NavigateTo(pageName);
        }

        private void ToggleLanguage_Click(object sender, RoutedEventArgs e)
        {
            // Toggle between Arabic and English
            App.SetApplicationCulture(App.IsArabic ? "en-US" : "ar-SA");

            // Update page title after language change
            string currentPage = PageTitle.Text;
            foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
            {
                foreach (object key in dict.Keys)
                {
                    if (dict[key] as string == currentPage)
                    {
                        PageTitle.Text = (string)FindResource(key);
                        break;
                    }
                }
            }
        }

        private void ToggleTheme_Click(object sender, RoutedEventArgs e)
        {
            _isDarkTheme = !_isDarkTheme;

            // Change theme
            ResourceDictionary themeDictionary = new ResourceDictionary
            {
                Source = new Uri($"/Resources/Themes/{(_isDarkTheme ? "Dark" : "Light")}Theme.xaml", UriKind.Relative)
            };

            // Replace the theme dictionary
            ResourceDictionary? oldDictionary = null;
            foreach (ResourceDictionary dict in Application.Current.Resources.MergedDictionaries)
            {
                if (dict.Source != null && dict.Source.OriginalString.Contains("/Resources/Themes/"))
                {
                    oldDictionary = dict;
                    break;
                }
            }

            if (oldDictionary != null)
            {
                int index = Application.Current.Resources.MergedDictionaries.IndexOf(oldDictionary);
                Application.Current.Resources.MergedDictionaries.Remove(oldDictionary);
                Application.Current.Resources.MergedDictionaries.Insert(index, themeDictionary);
            }
            else
            {
                Application.Current.Resources.MergedDictionaries.Add(themeDictionary);
            }
        }

        private void ApplyStyles()
        {
            // Add menu button style
            Style menuButtonStyle = new Style(typeof(Button));
            menuButtonStyle.Setters.Add(new Setter(Button.BackgroundProperty, System.Windows.Media.Brushes.Transparent));
            menuButtonStyle.Setters.Add(new Setter(Button.ForegroundProperty, System.Windows.Media.Brushes.White));
            menuButtonStyle.Setters.Add(new Setter(Button.BorderThicknessProperty, new Thickness(0)));
            menuButtonStyle.Setters.Add(new Setter(Button.HeightProperty, 50.0));
            menuButtonStyle.Setters.Add(new Setter(Button.FontSizeProperty, 14.0));
            menuButtonStyle.Setters.Add(new Setter(Button.HorizontalContentAlignmentProperty, HorizontalAlignment.Left));
            menuButtonStyle.Setters.Add(new Setter(Button.PaddingProperty, new Thickness(20, 0, 0, 0)));
            menuButtonStyle.Setters.Add(new Setter(Button.MarginProperty, new Thickness(0, 5, 0, 0)));

            // Add hover trigger
            Trigger hoverTrigger = new Trigger { Property = Button.IsMouseOverProperty, Value = true };
            hoverTrigger.Setters.Add(new Setter(Button.BackgroundProperty, new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(50, 255, 255, 255))));
            menuButtonStyle.Triggers.Add(hoverTrigger);

            // Register the style
            this.Resources.Add("MenuButtonStyle", menuButtonStyle);
        }
    }
}