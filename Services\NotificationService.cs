using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using System.Timers;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class NotificationService
    {
        private readonly DatabaseService _dbService;
        private readonly SettingsService _settingsService;
        private readonly ObservableCollection<Notification> _notifications;
        private readonly Timer _scheduledNotificationTimer;
        private readonly DispatcherTimer _toastTimer;

        public event EventHandler<Notification>? NotificationReceived;
        public event EventHandler? NotificationsChanged;
        public event EventHandler<NotificationToastEventArgs>? ToastNotificationRequested;

        public ObservableCollection<Notification> Notifications => _notifications;
        public int UnreadCount => _notifications.Count(n => !n.IsRead);

        public NotificationService(DatabaseService dbService, SettingsService settingsService)
        {
            _dbService = dbService;
            _settingsService = settingsService;
            _notifications = new ObservableCollection<Notification>();

            // إعداد مؤقت الإشعارات المجدولة
            _scheduledNotificationTimer = new Timer(TimeSpan.FromMinutes(1).TotalMilliseconds);
            _scheduledNotificationTimer.Elapsed += CheckScheduledNotifications;
            _scheduledNotificationTimer.Start();

            // إعداد مؤقت Toast
            _toastTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
        }

        public async Task InitializeAsync()
        {
            // Load unread notifications from database
            await LoadNotificationsAsync();
        }

        private async Task LoadNotificationsAsync()
        {
            try
            {
                const string sql = @"SELECT * FROM Notifications
                                   WHERE IsRead = 0
                                   ORDER BY CreatedAt DESC
                                   LIMIT 50";

                var notifications = await _dbService.QueryAsync<Notification>(sql);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Clear();
                    foreach (var notification in notifications)
                    {
                        _notifications.Add(notification);
                    }
                });

                NotificationsChanged?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading notifications: {ex.Message}");
            }
        }

        public async Task<IEnumerable<Notification>> GetAllNotificationsAsync(int limit = 100, int offset = 0)
        {
            const string sql = @"SELECT * FROM Notifications
                               ORDER BY CreatedAt DESC
                               LIMIT @Limit OFFSET @Offset";

            return await _dbService.QueryAsync<Notification>(sql, new { Limit = limit, Offset = offset });
        }

        public async Task<IEnumerable<Notification>> GetUnreadNotificationsAsync()
        {
            const string sql = @"SELECT * FROM Notifications
                               WHERE IsRead = 0
                               ORDER BY CreatedAt DESC";

            return await _dbService.QueryAsync<Notification>(sql);
        }

        public async Task<int> GetUnreadNotificationCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Notifications WHERE IsRead = 0";
            return await _dbService.QuerySingleOrDefaultAsync<int>(sql);
        }

        public async Task<Notification> GetNotificationByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Notifications WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<Notification>(sql, new { Id = id });
        }

        public async Task<bool> AddNotificationAsync(Notification notification)
        {
            // Validate notification
            var validator = new NotificationValidator();
            var validationResult = validator.Validate(notification);
            if (!validationResult.IsValid)
            {
                throw new ValidationException(validationResult.Errors);
            }

            // Set creation time
            notification.CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            // Insert notification
            const string sql = @"INSERT INTO Notifications (Title, Message, Type, IsRead, CreatedAt)
                               VALUES (@Title, @Message, @Type, @IsRead, @CreatedAt);
                               SELECT last_insert_rowid();";

            int id = await _dbService.QuerySingleOrDefaultAsync<int>(sql, notification);
            notification.Id = id;

            if (id > 0)
            {
                // Add to in-memory collection
                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Insert(0, notification);
                });

                // Raise events
                NotificationReceived?.Invoke(this, notification);
                NotificationsChanged?.Invoke(this, EventArgs.Empty);

                // Show toast notification if enabled
                await ShowToastNotificationAsync(notification);

                return true;
            }

            return false;
        }

        public async Task<bool> MarkAsReadAsync(int id)
        {
            const string sql = "UPDATE Notifications SET IsRead = 1 WHERE Id = @Id";
            int result = await _dbService.ExecuteAsync(sql, new { Id = id });

            if (result > 0)
            {
                // Update in-memory collection
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var notification = _notifications.FirstOrDefault(n => n.Id == id);
                    if (notification != null)
                    {
                        notification.IsRead = true;
                        _notifications.Remove(notification);
                    }
                });

                NotificationsChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }

            return false;
        }

        public async Task<bool> MarkAllAsReadAsync()
        {
            const string sql = "UPDATE Notifications SET IsRead = 1 WHERE IsRead = 0";
            int result = await _dbService.ExecuteAsync(sql);

            if (result > 0)
            {
                // Update in-memory collection
                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Clear();
                });

                NotificationsChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }

            return false;
        }

        public async Task<bool> DeleteNotificationAsync(int id)
        {
            const string sql = "DELETE FROM Notifications WHERE Id = @Id";
            int result = await _dbService.ExecuteAsync(sql, new { Id = id });

            if (result > 0)
            {
                // Update in-memory collection
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var notification = _notifications.FirstOrDefault(n => n.Id == id);
                    if (notification != null)
                    {
                        _notifications.Remove(notification);
                    }
                });

                NotificationsChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }

            return false;
        }

        public async Task<bool> DeleteAllNotificationsAsync()
        {
            const string sql = "DELETE FROM Notifications";
            int result = await _dbService.ExecuteAsync(sql);

            if (result > 0)
            {
                // Update in-memory collection
                Application.Current.Dispatcher.Invoke(() =>
                {
                    _notifications.Clear();
                });

                NotificationsChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }

            return false;
        }

        public async Task<bool> DeleteOldNotificationsAsync(int daysToKeep = 30)
        {
            string cutoffDate = DateTime.Now.AddDays(-daysToKeep).ToString("yyyy-MM-dd HH:mm:ss");
            const string sql = "DELETE FROM Notifications WHERE CreatedAt < @CutoffDate";
            int result = await _dbService.ExecuteAsync(sql, new { CutoffDate = cutoffDate });

            if (result > 0)
            {
                // Reload notifications
                await LoadNotificationsAsync();
                return true;
            }

            return false;
        }

        #region Specific Notification Types

        public async Task NotifyLowStockAsync(Product product)
        {
            bool showNotifications = await _settingsService.GetShowLowStockNotificationsAsync();
            if (!showNotifications)
            {
                return;
            }

            int threshold = await _settingsService.GetLowStockThresholdAsync();
            if (product.Quantity <= threshold)
            {
                var notification = new Notification
                {
                    Title = "Low Stock Alert",
                    Message = $"Product '{product.Name}' is running low on stock. Current quantity: {product.Quantity}",
                    Type = "LowStock",
                    IsRead = false
                };

                await AddNotificationAsync(notification);
            }
        }

        public async Task NotifyOutOfStockAsync(Product product)
        {
            var notification = new Notification
            {
                Title = "Out of Stock Alert",
                Message = $"Product '{product.Name}' is out of stock!",
                Type = "OutOfStock",
                IsRead = false
            };

            await AddNotificationAsync(notification);
        }

        public async Task NotifyPaymentDueAsync(Customer customer, decimal amount, DateTime dueDate)
        {
            var notification = new Notification
            {
                Title = "Payment Due",
                Message = $"Payment of {amount:C} from customer '{customer.Name}' is due on {dueDate.ToString("yyyy-MM-dd")}.",
                Type = "PaymentDue",
                IsRead = false
            };

            await AddNotificationAsync(notification);
        }

        public async Task NotifySupplierPaymentDueAsync(Supplier supplier, decimal amount, DateTime dueDate)
        {
            var notification = new Notification
            {
                Title = "Supplier Payment Due",
                Message = $"Payment of {amount:C} to supplier '{supplier.Name}' is due on {dueDate.ToString("yyyy-MM-dd")}.",
                Type = "SupplierPaymentDue",
                IsRead = false
            };

            await AddNotificationAsync(notification);
        }

        public async Task NotifyBackupSuccessAsync(string backupPath)
        {
            var notification = new Notification
            {
                Title = "Backup Successful",
                Message = $"Database backup was successfully created at {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.",
                Type = "BackupSuccess",
                IsRead = false
            };

            await AddNotificationAsync(notification);
        }

        public async Task NotifyBackupFailureAsync(string errorMessage)
        {
            var notification = new Notification
            {
                Title = "Backup Failed",
                Message = $"Database backup failed: {errorMessage}",
                Type = "BackupFailure",
                IsRead = false
            };

            await AddNotificationAsync(notification);
        }

        public async Task NotifySystemMessageAsync(string title, string message)
        {
            var notification = new Notification
            {
                Title = title,
                Message = message,
                Type = "System",
                IsRead = false
            };

            await AddNotificationAsync(notification);
        }

        #endregion

        private async Task ShowToastNotificationAsync(Notification notification)
        {
            try
            {
                // التحقق من إعدادات Toast
                bool showToast = await _settingsService.GetShowToastNotificationsAsync();
                if (!showToast) return;

                // إثارة حدث Toast
                ToastNotificationRequested?.Invoke(this, new NotificationToastEventArgs(notification));

                LoggingService.LogInfo($"Toast Notification: {notification.Title}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في عرض Toast notification");
            }
        }

        private async void CheckScheduledNotifications(object sender, ElapsedEventArgs e)
        {
            try
            {
                const string sql = @"SELECT * FROM Notifications
                                   WHERE IsScheduled = 1
                                   AND ScheduledTime <= @CurrentTime
                                   AND IsRead = 0";

                var currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                var scheduledNotifications = await _dbService.QueryAsync<Notification>(sql, new { CurrentTime = currentTime });

                foreach (var notification in scheduledNotifications)
                {
                    // تحويل الإشعار المجدول إلى إشعار عادي
                    notification.IsScheduled = false;
                    await UpdateNotificationAsync(notification);

                    // إضافة للمجموعة وإثارة الأحداث
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _notifications.Insert(0, notification);
                    });

                    NotificationReceived?.Invoke(this, notification);
                    NotificationsChanged?.Invoke(this, EventArgs.Empty);

                    // عرض Toast
                    await ShowToastNotificationAsync(notification);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في فحص الإشعارات المجدولة");
            }
        }

        private async Task<bool> ShouldShowToastAsync(NotificationType type, NotificationPriority priority)
        {
            // التحقق من إعدادات المستخدم
            bool showToast = await _settingsService.GetShowToastNotificationsAsync();
            if (!showToast) return false;

            // التحقق من أولوية الإشعار
            if (priority == NotificationPriority.Low)
            {
                bool showLowPriority = await _settingsService.GetShowLowPriorityToastAsync();
                return showLowPriority;
            }

            return true;
        }

        private async Task UpdateNotificationAsync(Notification notification)
        {
            const string sql = @"UPDATE Notifications
                               SET Title = @Title, Message = @Message, Type = @Type,
                                   IsRead = @IsRead, IsScheduled = @IsScheduled,
                                   ScheduledTime = @ScheduledTime
                               WHERE Id = @Id";

            await _dbService.ExecuteAsync(sql, notification);
        }

        public async Task<IEnumerable<Notification>> GetNotificationsByTypeAsync(NotificationType type)
        {
            const string sql = @"SELECT * FROM Notifications
                               WHERE Type = @Type
                               ORDER BY CreatedAt DESC";

            return await _dbService.QueryAsync<Notification>(sql, new { Type = type.ToString() });
        }

        public async Task<IEnumerable<Notification>> GetNotificationsByPriorityAsync(NotificationPriority priority)
        {
            const string sql = @"SELECT * FROM Notifications
                               WHERE Priority = @Priority
                               ORDER BY CreatedAt DESC";

            return await _dbService.QueryAsync<Notification>(sql, new { Priority = priority.ToString() });
        }

        public async Task SnoozeNotificationAsync(int id, TimeSpan snoozeTime)
        {
            var notification = await GetNotificationByIdAsync(id);
            if (notification != null)
            {
                notification.ScheduledTime = DateTime.Now.Add(snoozeTime).ToString("yyyy-MM-dd HH:mm:ss");
                notification.IsScheduled = true;
                notification.IsRead = true; // إخفاء مؤقتاً

                await UpdateNotificationAsync(notification);

                // إزالة من المجموعة المرئية
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var existingNotification = _notifications.FirstOrDefault(n => n.Id == id);
                    if (existingNotification != null)
                    {
                        _notifications.Remove(existingNotification);
                    }
                });

                NotificationsChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        public void Dispose()
        {
            _scheduledNotificationTimer?.Stop();
            _scheduledNotificationTimer?.Dispose();
            _toastTimer?.Stop();
        }
    }


}