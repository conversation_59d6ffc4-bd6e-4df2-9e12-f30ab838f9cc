using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class ChangePasswordViewModel : BaseViewModel
    {
        #region Services

        private readonly AuthenticationService _authService;
        private readonly SecurityService _securityService;

        #endregion

        #region Properties

        private User _user;
        public User User
        {
            get => _user;
            set => SetProperty(ref _user, value);
        }

        private string _currentPassword = string.Empty;
        public string CurrentPassword
        {
            get => _currentPassword;
            set
            {
                if (SetProperty(ref _currentPassword, value))
                {
                    ChangePasswordCommand.RaiseCanExecuteChanged();
                    ClearError();
                }
            }
        }

        private string _newPassword = string.Empty;
        public string NewPassword
        {
            get => _newPassword;
            set
            {
                if (SetProperty(ref _newPassword, value))
                {
                    UpdatePasswordStrength();
                    UpdatePasswordMatch();
                    ChangePasswordCommand.RaiseCanExecuteChanged();
                    ClearError();
                }
            }
        }

        private string _confirmPassword = string.Empty;
        public string ConfirmPassword
        {
            get => _confirmPassword;
            set
            {
                if (SetProperty(ref _confirmPassword, value))
                {
                    UpdatePasswordMatch();
                    ChangePasswordCommand.RaiseCanExecuteChanged();
                    ClearError();
                }
            }
        }

        private bool _requireCurrentPassword = true;
        public bool RequireCurrentPassword
        {
            get => _requireCurrentPassword;
            set => SetProperty(ref _requireCurrentPassword, value);
        }

        // Password Strength Properties
        private int _passwordStrengthScore;
        public int PasswordStrengthScore
        {
            get => _passwordStrengthScore;
            set => SetProperty(ref _passwordStrengthScore, value);
        }

        private string _passwordStrengthText = string.Empty;
        public string PasswordStrengthText
        {
            get => _passwordStrengthText;
            set => SetProperty(ref _passwordStrengthText, value);
        }

        private Color _passwordStrengthColor = Colors.Gray;
        public Color PasswordStrengthColor
        {
            get => _passwordStrengthColor;
            set => SetProperty(ref _passwordStrengthColor, value);
        }

        private bool _showPasswordStrength;
        public bool ShowPasswordStrength
        {
            get => _showPasswordStrength;
            set => SetProperty(ref _showPasswordStrength, value);
        }

        private ObservableCollection<PasswordRequirement> _passwordRequirements = new();
        public ObservableCollection<PasswordRequirement> PasswordRequirements
        {
            get => _passwordRequirements;
            set => SetProperty(ref _passwordRequirements, value);
        }

        // Password Match Properties
        private bool _showPasswordMatch;
        public bool ShowPasswordMatch
        {
            get => _showPasswordMatch;
            set => SetProperty(ref _showPasswordMatch, value);
        }

        private string _passwordMatchText = string.Empty;
        public string PasswordMatchText
        {
            get => _passwordMatchText;
            set => SetProperty(ref _passwordMatchText, value);
        }

        private string _passwordMatchIcon = "Check";
        public string PasswordMatchIcon
        {
            get => _passwordMatchIcon;
            set => SetProperty(ref _passwordMatchIcon, value);
        }

        private Brush _passwordMatchColor = Brushes.Green;
        public Brush PasswordMatchColor
        {
            get => _passwordMatchColor;
            set => SetProperty(ref _passwordMatchColor, value);
        }

        public bool CanChangePassword => !IsLoading &&
            (!RequireCurrentPassword || !string.IsNullOrWhiteSpace(CurrentPassword)) &&
            !string.IsNullOrWhiteSpace(NewPassword) &&
            !string.IsNullOrWhiteSpace(ConfirmPassword) &&
            NewPassword == ConfirmPassword &&
            PasswordStrengthScore >= 3;

        #endregion

        #region Commands

        private DelegateCommand? _changePasswordCommand;
        public DelegateCommand ChangePasswordCommand => _changePasswordCommand ??= new DelegateCommand(ChangePassword, () => CanChangePassword);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        #endregion

        #region Constructor

        public ChangePasswordViewModel(User user)
        {
            _user = user;
            var dbService = new DatabaseService();
            _authService = new AuthenticationService(dbService);
            _securityService = new SecurityService();

            // Check if current password is required (for existing users)
            RequireCurrentPassword = user.Id > 0;

            InitializePasswordRequirements();
        }

        #endregion

        #region Methods

        private void InitializePasswordRequirements()
        {
            PasswordRequirements.Clear();
            PasswordRequirements.Add(new PasswordRequirement { Text = "8 أحرف على الأقل", Icon = "Close", Color = Brushes.Red });
            PasswordRequirements.Add(new PasswordRequirement { Text = "أحرف كبيرة وصغيرة", Icon = "Close", Color = Brushes.Red });
            PasswordRequirements.Add(new PasswordRequirement { Text = "أرقام", Icon = "Close", Color = Brushes.Red });
            PasswordRequirements.Add(new PasswordRequirement { Text = "رموز خاصة", Icon = "Close", Color = Brushes.Red });
        }

        private void UpdatePasswordStrength()
        {
            if (string.IsNullOrEmpty(NewPassword))
            {
                ShowPasswordStrength = false;
                return;
            }

            ShowPasswordStrength = true;
            var strength = _securityService.ValidatePasswordStrength(NewPassword);

            PasswordStrengthScore = strength.Score;
            PasswordStrengthText = strength.Level;
            PasswordStrengthColor = (Color)ColorConverter.ConvertFromString(strength.Color);

            // Update requirements
            UpdatePasswordRequirements();
        }

        private void UpdatePasswordRequirements()
        {
            if (string.IsNullOrEmpty(NewPassword)) return;

            // Length check
            PasswordRequirements[0].Icon = NewPassword.Length >= 8 ? "Check" : "Close";
            PasswordRequirements[0].Color = NewPassword.Length >= 8 ? Brushes.Green : Brushes.Red;

            // Case check
            bool hasUpper = NewPassword.Any(char.IsUpper);
            bool hasLower = NewPassword.Any(char.IsLower);
            PasswordRequirements[1].Icon = hasUpper && hasLower ? "Check" : "Close";
            PasswordRequirements[1].Color = hasUpper && hasLower ? Brushes.Green : Brushes.Red;

            // Digit check
            bool hasDigit = NewPassword.Any(char.IsDigit);
            PasswordRequirements[2].Icon = hasDigit ? "Check" : "Close";
            PasswordRequirements[2].Color = hasDigit ? Brushes.Green : Brushes.Red;

            // Special character check
            bool hasSpecial = NewPassword.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c));
            PasswordRequirements[3].Icon = hasSpecial ? "Check" : "Close";
            PasswordRequirements[3].Color = hasSpecial ? Brushes.Green : Brushes.Red;
        }

        private void UpdatePasswordMatch()
        {
            if (string.IsNullOrEmpty(NewPassword) || string.IsNullOrEmpty(ConfirmPassword))
            {
                ShowPasswordMatch = false;
                return;
            }

            ShowPasswordMatch = true;
            bool passwordsMatch = NewPassword == ConfirmPassword;

            PasswordMatchText = passwordsMatch ? "كلمات المرور متطابقة" : "كلمات المرور غير متطابقة";
            PasswordMatchIcon = passwordsMatch ? "Check" : "Close";
            PasswordMatchColor = passwordsMatch ? Brushes.Green : Brushes.Red;
        }

        #endregion

        #region Command Handlers

        private async void ChangePassword()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تغيير كلمة المرور...";
                ClearError();

                bool success;
                if (RequireCurrentPassword)
                {
                    success = await _authService.ChangePasswordAsync(CurrentPassword, NewPassword);
                }
                else
                {
                    // For new users or admin reset
                    success = await CreateNewPasswordAsync();
                }

                if (success)
                {
                    LoggingService.LogSecurityEvent("تغيير كلمة المرور", $"المستخدم: {User.Username}");
                    PasswordChanged?.Invoke();
                }
                else
                {
                    SetError("فشل في تغيير كلمة المرور. تأكد من صحة كلمة المرور الحالية.");
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تغيير كلمة المرور: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في تغيير كلمة المرور للمستخدم: {User.Username}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task<bool> CreateNewPasswordAsync()
        {
            try
            {
                // Hash new password
                var (hash, salt) = _securityService.HashPassword(NewPassword);

                // Update user password
                User.PasswordHash = hash;
                User.Salt = salt;
                User.LastPasswordChangeDate = DateTime.Now;
                User.UpdatedAt = DateTime.Now;

                // Save to database
                var dbService = new DatabaseService();
                await dbService.UpdateAsync("Users", User);

                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء كلمة مرور جديدة للمستخدم: {User.Username}");
                return false;
            }
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion

        #region Events

        public event Action? PasswordChanged;
        public event Action<bool>? RequestClose;

        #endregion
    }

    public class PasswordRequirement
    {
        public string Text { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public Brush Color { get; set; } = Brushes.Gray;
    }
}
