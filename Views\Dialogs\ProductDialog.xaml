<Window x:Class="SalesManagementSystem.Views.Dialogs.ProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="700" Width="550"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>

        <Style x:Key="FormTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="FormComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="{Binding HeaderIcon}"
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="{Binding WindowTitle}"
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center"
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Form Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Product Code -->
                <TextBox x:Name="CodeTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="كود المنتج *"
                        Text="{Binding Product.Code, UpdateSourceTrigger=PropertyChanged}"
                        IsEnabled="{Binding IsCodeEditable}"/>

                <!-- Product Name -->
                <TextBox x:Name="NameTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="اسم المنتج *"
                        Text="{Binding Product.Name, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- Category -->
                <ComboBox x:Name="CategoryComboBox"
                         Style="{StaticResource FormComboBox}"
                         materialDesign:HintAssist.Hint="الفئة *"
                         ItemsSource="{Binding Categories}"
                         SelectedItem="{Binding SelectedCategory}"
                         DisplayMemberPath="Name"/>

                <!-- Description -->
                <TextBox x:Name="DescriptionTextBox"
                        Style="{StaticResource FormTextBox}"
                        materialDesign:HintAssist.Hint="الوصف"
                        Text="{Binding Product.Description, UpdateSourceTrigger=PropertyChanged}"
                        Height="80"
                        TextWrapping="Wrap"
                        AcceptsReturn="True"
                        VerticalScrollBarVisibility="Auto"/>

                <!-- Price Section -->
                <GroupBox Header="الأسعار"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,16,0,8">
                    <StackPanel Margin="16">
                        <!-- Purchase Price -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="سعر الشراء *"
                                Text="{Binding Product.PurchasePrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Sale Price -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="سعر البيع *"
                                Text="{Binding Product.SalePrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"/>

                        <!-- Profit Margin Display -->
                        <StackPanel Orientation="Horizontal" Margin="0,8">
                            <TextBlock Text="هامش الربح:"
                                      VerticalAlignment="Center"
                                      Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding ProfitMargin, StringFormat=F2}"
                                      FontWeight="Bold"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                                      VerticalAlignment="Center"/>
                            <TextBlock Text="%"
                                      VerticalAlignment="Center"
                                      Margin="4,0,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>

                <!-- Stock Section -->
                <GroupBox Header="المخزون"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,8">
                    <StackPanel Margin="16">
                        <!-- Current Quantity -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الكمية الحالية *"
                                Text="{Binding Product.Quantity, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Minimum Quantity -->
                        <TextBox Style="{StaticResource FormTextBox}"
                                materialDesign:HintAssist.Hint="الحد الأدنى للكمية *"
                                Text="{Binding Product.MinQuantity, UpdateSourceTrigger=PropertyChanged}"/>

                        <!-- Unit -->
                        <ComboBox Style="{StaticResource FormComboBox}"
                                 materialDesign:HintAssist.Hint="الوحدة"
                                 ItemsSource="{Binding Units}"
                                 Text="{Binding Product.Unit, UpdateSourceTrigger=PropertyChanged}"
                                 IsEditable="True"/>

                        <!-- Low Stock Warning -->
                        <Border Background="{DynamicResource MaterialDesignSelection}"
                               CornerRadius="4"
                               Padding="12,8"
                               Margin="0,8"
                               Visibility="{Binding IsLowStock, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle"
                                                       Foreground="Orange"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="تحذير: الكمية أقل من الحد الأدنى"
                                          Foreground="Orange"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </GroupBox>

                <!-- Additional Options -->
                <GroupBox Header="خيارات إضافية"
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,8">
                    <StackPanel Margin="16">
                        <!-- Barcode -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="8"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                    Style="{StaticResource FormTextBox}"
                                    materialDesign:HintAssist.Hint="الباركود"
                                    Text="{Binding Product.Barcode, UpdateSourceTrigger=PropertyChanged}"/>

                            <Button Grid.Column="2"
                                   Command="{Binding GenerateBarcodeCommand}"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Width="120"
                                   ToolTip="توليد باركود">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Barcode"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,4,0"/>
                                    <TextBlock Text="توليد" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>

                        <!-- Barcode Display -->
                        <Expander Header="عرض الباركود"
                                 Style="{DynamicResource MaterialDesignExpander}"
                                 Margin="0,8"
                                 Visibility="{Binding HasBarcode, Converter={StaticResource BoolToVisConverter}}">
                            <Border Background="White"
                                   BorderBrush="{DynamicResource MaterialDesignDivider}"
                                   BorderThickness="1"
                                   CornerRadius="4"
                                   Padding="16"
                                   HorizontalAlignment="Center"
                                   Margin="8">
                                <StackPanel HorizontalAlignment="Center">
                                    <Image Source="{Binding BarcodeImage}"
                                          Stretch="None"
                                          HorizontalAlignment="Center"
                                          Margin="0,0,0,8"/>
                                    <TextBlock Text="{Binding Product.Barcode}"
                                              HorizontalAlignment="Center"
                                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"/>
                                </StackPanel>
                            </Border>
                        </Expander>

                        <!-- Is Active -->
                        <CheckBox Content="المنتج نشط"
                                 IsChecked="{Binding Product.IsActive}"
                                 Margin="0,8"
                                 Style="{DynamicResource MaterialDesignCheckBox}"/>

                        <!-- Track Stock -->
                        <CheckBox Content="تتبع المخزون"
                                 IsChecked="{Binding Product.TrackStock}"
                                 Margin="0,8"
                                 Style="{DynamicResource MaterialDesignCheckBox}"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2"
                   Orientation="Horizontal"
                   HorizontalAlignment="Left"
                   Margin="0,24,0,0">

            <Button Command="{Binding SaveCommand}"
                   Style="{DynamicResource MaterialDesignRaisedButton}"
                   Width="100"
                   Margin="0,0,12,0">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="حفظ" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding CancelCommand}"
                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                   Width="100">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="إلغاء" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>

        <!-- Loading Overlay -->
        <Grid Grid.RowSpan="3"
              Background="#80000000"
              Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="48" Height="48"/>
                <TextBlock Text="{Binding LoadingMessage}"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
