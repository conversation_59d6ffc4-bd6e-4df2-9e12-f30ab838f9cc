using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views.Dialogs;

namespace SalesManagementSystem.Views
{
    public partial class ProductsView : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private List<ProductViewModel> _allProducts = new();
        private List<Category> _categories = new();

        public ProductsView()
        {
            InitializeComponent();

            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);

            Loaded += ProductsView_Loaded;
        }

        private async void ProductsView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // Load categories for filter
                await LoadCategoriesAsync();

                // Load products
                await LoadProductsAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCategoriesAsync()
        {
            _categories = (await _productService.GetAllCategoriesAsync()).ToList();

            // Add "All Categories" option
            var allCategoriesItem = new Category { Id = 0, Name = "جميع الفئات" };
            _categories.Insert(0, allCategoriesItem);

            CategoryComboBox.ItemsSource = _categories;
            CategoryComboBox.DisplayMemberPath = "Name";
            CategoryComboBox.SelectedValuePath = "Id";
            CategoryComboBox.SelectedIndex = 0;
        }

        private async Task LoadProductsAsync()
        {
            var products = await _productService.GetAllProductsAsync();

            _allProducts = products.Select(p => new ProductViewModel
            {
                Id = p.Id,
                Code = p.Code,
                Name = p.Name,
                Description = p.Description,
                CategoryId = p.CategoryId,
                CategoryName = p.CategoryName ?? "غير محدد",
                PurchasePrice = p.PurchasePrice,
                SellingPrice = p.SalePrice,
                Quantity = p.Quantity,
                MinQuantity = p.MinQuantity,
                IsLowStock = p.Quantity <= p.MinQuantity,
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            }).ToList();

            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var filteredProducts = _allProducts.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredProducts = filteredProducts.Where(p =>
                    p.Name.ToLower().Contains(searchTerm) ||
                    p.Code.ToLower().Contains(searchTerm) ||
                    p.CategoryName.ToLower().Contains(searchTerm));
            }

            // Apply category filter
            if (CategoryComboBox.SelectedValue != null && (int)CategoryComboBox.SelectedValue != 0)
            {
                var selectedCategoryId = (int)CategoryComboBox.SelectedValue;
                filteredProducts = filteredProducts.Where(p => p.CategoryId == selectedCategoryId);
            }

            ProductsDataGrid.ItemsSource = filteredProducts.ToList();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProductDialog();
            if (dialog.ShowDialog() == true)
            {
                await LoadProductsAsync();
            }
        }

        private async void EditProduct_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductViewModel product)
            {
                var productToEdit = new Product
                {
                    Id = product.Id,
                    Code = product.Code,
                    Name = product.Name,
                    Description = product.Description,
                    CategoryId = product.CategoryId,
                    PurchasePrice = product.PurchasePrice,
                    SalePrice = product.SellingPrice,
                    Quantity = product.Quantity,
                    MinQuantity = product.MinQuantity,
                    CreatedAt = product.CreatedAt,
                    UpdatedAt = product.UpdatedAt
                };

                var dialog = new ProductDialog(productToEdit);
                if (dialog.ShowDialog() == true)
                {
                    await LoadProductsAsync();
                }
            }
        }

        private async void DeleteProduct_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductViewModel product)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف المنتج '{product.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _productService.DeleteProductAsync(product.Id);
                        await LoadProductsAsync();

                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }
    }

    public class ProductViewModel
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int? CategoryId { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public decimal PurchasePrice { get; set; }
        public decimal SellingPrice { get; set; }
        public int Quantity { get; set; }
        public int MinQuantity { get; set; }
        public bool IsLowStock { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
}
