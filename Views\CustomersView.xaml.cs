using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.Views
{
    public partial class CustomersView : UserControl
    {
        private readonly DatabaseService _dbService;
        private readonly CustomerService _customerService;
        private List<Customer> _allCustomers = new();

        public CustomersView()
        {
            InitializeComponent();
            
            _dbService = new DatabaseService();
            _customerService = new CustomerService(_dbService);
            
            Loaded += CustomersView_Loaded;
        }

        private async void CustomersView_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadCustomersAsync();
        }

        private async Task LoadCustomersAsync()
        {
            try
            {
                _allCustomers = (await _customerService.GetAllCustomersAsync()).ToList();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredCustomers = _allCustomers.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredCustomers = filteredCustomers.Where(c => 
                    c.Name.ToLower().Contains(searchTerm) ||
                    (c.Phone?.ToLower().Contains(searchTerm) ?? false) ||
                    (c.Email?.ToLower().Contains(searchTerm) ?? false));
            }

            CustomersDataGrid.ItemsSource = filteredCustomers.ToList();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("إضافة عميل جديد - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Customer customer)
            {
                MessageBox.Show($"تعديل العميل: {customer.Name} - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void DeleteCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Customer customer)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف العميل '{customer.Name}'؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _customerService.DeleteCustomerAsync(customer.Id);
                        await LoadCustomersAsync();
                        
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح", 
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", 
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void ViewPurchases_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Customer customer)
            {
                MessageBox.Show($"عرض مشتريات العميل: {customer.Name} - سيتم تطوير هذه الميزة قريباً", "قريباً", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadCustomersAsync();
        }
    }
}
