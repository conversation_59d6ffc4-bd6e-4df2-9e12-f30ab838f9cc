using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج الطلب الإلكتروني
    /// </summary>
    public class OnlineOrder : INotifyPropertyChanged
    {
        private int _id;
        private int _storeId;
        private string _orderNumber = string.Empty;
        private int? _customerId;
        private string _customerEmail = string.Empty;
        private string _customerPhone = string.Empty;
        private OrderStatus _status = OrderStatus.Pending;
        private PaymentStatus _paymentStatus = PaymentStatus.Pending;
        private ShippingStatus _shippingStatus = ShippingStatus.NotShipped;
        private decimal _subtotal;
        private decimal _taxAmount;
        private decimal _shippingAmount;
        private decimal _discountAmount;
        private decimal _totalAmount;
        private string _currency = "SAR";
        private string _paymentMethod = string.Empty;
        private string _paymentReference = string.Empty;
        private string _shippingMethod = string.Empty;
        private decimal _shippingCost;
        private string _trackingNumber = string.Empty;
        private string _couponCode = string.Empty;
        private decimal _couponDiscount;
        private string _notes = string.Empty;
        private string _adminNotes = string.Empty;
        private DateTime _orderDate = DateTime.Now;
        private DateTime? _shippedDate;
        private DateTime? _deliveredDate;
        private DateTime? _cancelledDate;
        private string _cancelReason = string.Empty;
        private BillingAddress _billingAddress = new();
        private ShippingAddress _shippingAddress = new();
        private ObservableCollection<OrderItem> _items = new();
        private ObservableCollection<OrderStatusHistory> _statusHistory = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public int StoreId
        {
            get => _storeId;
            set
            {
                if (_storeId != value)
                {
                    _storeId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OrderNumber
        {
            get => _orderNumber;
            set
            {
                if (_orderNumber != value)
                {
                    _orderNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public int? CustomerId
        {
            get => _customerId;
            set
            {
                if (_customerId != value)
                {
                    _customerId = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(IsGuestOrder));
                }
            }
        }

        public string CustomerEmail
        {
            get => _customerEmail;
            set
            {
                if (_customerEmail != value)
                {
                    _customerEmail = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CustomerPhone
        {
            get => _customerPhone;
            set
            {
                if (_customerPhone != value)
                {
                    _customerPhone = value;
                    OnPropertyChanged();
                }
            }
        }

        public OrderStatus Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusDisplay));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanCancel));
                    OnPropertyChanged(nameof(CanShip));
                    OnPropertyChanged(nameof(CanComplete));
                }
            }
        }

        public PaymentStatus PaymentStatus
        {
            get => _paymentStatus;
            set
            {
                if (_paymentStatus != value)
                {
                    _paymentStatus = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PaymentStatusDisplay));
                    OnPropertyChanged(nameof(PaymentStatusColor));
                    OnPropertyChanged(nameof(IsPaid));
                }
            }
        }

        public ShippingStatus ShippingStatus
        {
            get => _shippingStatus;
            set
            {
                if (_shippingStatus != value)
                {
                    _shippingStatus = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ShippingStatusDisplay));
                    OnPropertyChanged(nameof(ShippingStatusColor));
                    OnPropertyChanged(nameof(IsShipped));
                    OnPropertyChanged(nameof(IsDelivered));
                }
            }
        }

        public decimal Subtotal
        {
            get => _subtotal;
            set
            {
                if (_subtotal != value)
                {
                    _subtotal = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedSubtotal));
                }
            }
        }

        public decimal TaxAmount
        {
            get => _taxAmount;
            set
            {
                if (_taxAmount != value)
                {
                    _taxAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTaxAmount));
                }
            }
        }

        public decimal ShippingAmount
        {
            get => _shippingAmount;
            set
            {
                if (_shippingAmount != value)
                {
                    _shippingAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedShippingAmount));
                }
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                if (_discountAmount != value)
                {
                    _discountAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDiscountAmount));
                    OnPropertyChanged(nameof(HasDiscount));
                }
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                if (_totalAmount != value)
                {
                    _totalAmount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedTotalAmount));
                }
            }
        }

        public string Currency
        {
            get => _currency;
            set
            {
                if (_currency != value)
                {
                    _currency = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PaymentMethod
        {
            get => _paymentMethod;
            set
            {
                if (_paymentMethod != value)
                {
                    _paymentMethod = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(PaymentMethodDisplay));
                }
            }
        }

        public string PaymentReference
        {
            get => _paymentReference;
            set
            {
                if (_paymentReference != value)
                {
                    _paymentReference = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ShippingMethod
        {
            get => _shippingMethod;
            set
            {
                if (_shippingMethod != value)
                {
                    _shippingMethod = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal ShippingCost
        {
            get => _shippingCost;
            set
            {
                if (_shippingCost != value)
                {
                    _shippingCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedShippingCost));
                }
            }
        }

        public string TrackingNumber
        {
            get => _trackingNumber;
            set
            {
                if (_trackingNumber != value)
                {
                    _trackingNumber = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasTracking));
                }
            }
        }

        public string CouponCode
        {
            get => _couponCode;
            set
            {
                if (_couponCode != value)
                {
                    _couponCode = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasCoupon));
                }
            }
        }

        public decimal CouponDiscount
        {
            get => _couponDiscount;
            set
            {
                if (_couponDiscount != value)
                {
                    _couponDiscount = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCouponDiscount));
                }
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                if (_notes != value)
                {
                    _notes = value;
                    OnPropertyChanged();
                }
            }
        }

        public string AdminNotes
        {
            get => _adminNotes;
            set
            {
                if (_adminNotes != value)
                {
                    _adminNotes = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime OrderDate
        {
            get => _orderDate;
            set
            {
                if (_orderDate != value)
                {
                    _orderDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedOrderDate));
                    OnPropertyChanged(nameof(DaysAgo));
                }
            }
        }

        public DateTime? ShippedDate
        {
            get => _shippedDate;
            set
            {
                if (_shippedDate != value)
                {
                    _shippedDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedShippedDate));
                }
            }
        }

        public DateTime? DeliveredDate
        {
            get => _deliveredDate;
            set
            {
                if (_deliveredDate != value)
                {
                    _deliveredDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedDeliveredDate));
                    OnPropertyChanged(nameof(DeliveryDuration));
                }
            }
        }

        public DateTime? CancelledDate
        {
            get => _cancelledDate;
            set
            {
                if (_cancelledDate != value)
                {
                    _cancelledDate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCancelledDate));
                }
            }
        }

        public string CancelReason
        {
            get => _cancelReason;
            set
            {
                if (_cancelReason != value)
                {
                    _cancelReason = value;
                    OnPropertyChanged();
                }
            }
        }

        public BillingAddress BillingAddress
        {
            get => _billingAddress;
            set
            {
                if (_billingAddress != value)
                {
                    _billingAddress = value;
                    OnPropertyChanged();
                }
            }
        }

        public ShippingAddress ShippingAddress
        {
            get => _shippingAddress;
            set
            {
                if (_shippingAddress != value)
                {
                    _shippingAddress = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<OrderItem> Items
        {
            get => _items;
            set
            {
                if (_items != value)
                {
                    _items = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ItemsCount));
                    OnPropertyChanged(nameof(TotalQuantity));
                }
            }
        }

        public ObservableCollection<OrderStatusHistory> StatusHistory
        {
            get => _statusHistory;
            set
            {
                if (_statusHistory != value)
                {
                    _statusHistory = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public bool IsGuestOrder => !CustomerId.HasValue;
        public bool IsPaid => PaymentStatus == PaymentStatus.Paid;
        public bool IsShipped => ShippingStatus == ShippingStatus.Shipped || ShippingStatus == ShippingStatus.Delivered;
        public bool IsDelivered => ShippingStatus == ShippingStatus.Delivered;
        public bool HasDiscount => DiscountAmount > 0;
        public bool HasCoupon => !string.IsNullOrEmpty(CouponCode);
        public bool HasTracking => !string.IsNullOrEmpty(TrackingNumber);
        public int ItemsCount => Items?.Count ?? 0;
        public int TotalQuantity => Items?.Sum(i => i.Quantity) ?? 0;

        public bool CanCancel => Status == OrderStatus.Pending || Status == OrderStatus.Processing;
        public bool CanShip => Status == OrderStatus.Processing && IsPaid;
        public bool CanComplete => Status == OrderStatus.Shipped;

        public int DaysAgo => (int)(DateTime.Now - OrderDate).TotalDays;

        public string DeliveryDuration
        {
            get
            {
                if (!ShippedDate.HasValue || !DeliveredDate.HasValue) return "غير محدد";
                var duration = DeliveredDate.Value - ShippedDate.Value;
                return $"{duration.Days} يوم";
            }
        }

        // Display Properties
        public string StatusDisplay
        {
            get
            {
                return Status switch
                {
                    OrderStatus.Pending => "معلق",
                    OrderStatus.Processing => "قيد المعالجة",
                    OrderStatus.Shipped => "تم الشحن",
                    OrderStatus.Delivered => "تم التسليم",
                    OrderStatus.Cancelled => "ملغي",
                    OrderStatus.Refunded => "مسترد",
                    _ => "غير محدد"
                };
            }
        }

        public string PaymentStatusDisplay
        {
            get
            {
                return PaymentStatus switch
                {
                    PaymentStatus.Pending => "معلق",
                    PaymentStatus.Paid => "مدفوع",
                    PaymentStatus.Failed => "فشل",
                    PaymentStatus.Refunded => "مسترد",
                    PaymentStatus.PartiallyRefunded => "مسترد جزئياً",
                    _ => "غير محدد"
                };
            }
        }

        public string ShippingStatusDisplay
        {
            get
            {
                return ShippingStatus switch
                {
                    ShippingStatus.NotShipped => "لم يتم الشحن",
                    ShippingStatus.Processing => "قيد التجهيز",
                    ShippingStatus.Shipped => "تم الشحن",
                    ShippingStatus.InTransit => "في الطريق",
                    ShippingStatus.Delivered => "تم التسليم",
                    ShippingStatus.Failed => "فشل التسليم",
                    _ => "غير محدد"
                };
            }
        }

        public string PaymentMethodDisplay
        {
            get
            {
                return PaymentMethod switch
                {
                    "credit_card" => "بطاقة ائتمان",
                    "debit_card" => "بطاقة خصم",
                    "bank_transfer" => "تحويل بنكي",
                    "cash_on_delivery" => "الدفع عند الاستلام",
                    "apple_pay" => "Apple Pay",
                    "stc_pay" => "STC Pay",
                    "mada" => "مدى",
                    _ => PaymentMethod
                };
            }
        }

        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    OrderStatus.Pending => "Orange",
                    OrderStatus.Processing => "Blue",
                    OrderStatus.Shipped => "Purple",
                    OrderStatus.Delivered => "Green",
                    OrderStatus.Cancelled => "Red",
                    OrderStatus.Refunded => "Gray",
                    _ => "Gray"
                };
            }
        }

        public string PaymentStatusColor
        {
            get
            {
                return PaymentStatus switch
                {
                    PaymentStatus.Pending => "Orange",
                    PaymentStatus.Paid => "Green",
                    PaymentStatus.Failed => "Red",
                    PaymentStatus.Refunded => "Gray",
                    PaymentStatus.PartiallyRefunded => "Orange",
                    _ => "Gray"
                };
            }
        }

        public string ShippingStatusColor
        {
            get
            {
                return ShippingStatus switch
                {
                    ShippingStatus.NotShipped => "Gray",
                    ShippingStatus.Processing => "Orange",
                    ShippingStatus.Shipped => "Blue",
                    ShippingStatus.InTransit => "Purple",
                    ShippingStatus.Delivered => "Green",
                    ShippingStatus.Failed => "Red",
                    _ => "Gray"
                };
            }
        }

        // Formatted Properties
        public string FormattedSubtotal => Subtotal.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTaxAmount => TaxAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedShippingAmount => ShippingAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedDiscountAmount => DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedTotalAmount => TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedShippingCost => ShippingCost.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedCouponDiscount => CouponDiscount.ToString("C", new System.Globalization.CultureInfo("ar-SA"));
        public string FormattedOrderDate => OrderDate.ToString("dd/MM/yyyy HH:mm");
        public string FormattedShippedDate => ShippedDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";
        public string FormattedDeliveredDate => DeliveredDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";
        public string FormattedCancelledDate => CancelledDate?.ToString("dd/MM/yyyy HH:mm") ?? "غير محدد";

        #endregion

        #region Methods

        public void CalculateTotals()
        {
            Subtotal = Items.Sum(i => i.Total);
            TotalAmount = Subtotal + TaxAmount + ShippingAmount - DiscountAmount - CouponDiscount;
        }

        public void AddItem(OrderItem item)
        {
            Items.Add(item);
            CalculateTotals();
        }

        public void RemoveItem(OrderItem item)
        {
            Items.Remove(item);
            CalculateTotals();
        }

        public void UpdateStatus(OrderStatus newStatus, string note = "")
        {
            var oldStatus = Status;
            Status = newStatus;

            // Add to history
            StatusHistory.Add(new OrderStatusHistory
            {
                OrderId = Id,
                FromStatus = oldStatus.ToString(),
                ToStatus = newStatus.ToString(),
                Note = note,
                ChangedAt = DateTime.Now
            });

            // Update dates based on status
            switch (newStatus)
            {
                case OrderStatus.Shipped:
                    ShippedDate = DateTime.Now;
                    ShippingStatus = ShippingStatus.Shipped;
                    break;
                case OrderStatus.Delivered:
                    DeliveredDate = DateTime.Now;
                    ShippingStatus = ShippingStatus.Delivered;
                    break;
                case OrderStatus.Cancelled:
                    CancelledDate = DateTime.Now;
                    break;
            }
        }

        public void MarkAsPaid(string paymentReference = "")
        {
            PaymentStatus = PaymentStatus.Paid;
            PaymentReference = paymentReference;
        }

        public void Cancel(string reason)
        {
            UpdateStatus(OrderStatus.Cancelled, reason);
            CancelReason = reason;
        }

        public void Ship(string trackingNumber = "", string shippingMethod = "")
        {
            UpdateStatus(OrderStatus.Shipped);
            TrackingNumber = trackingNumber;
            if (!string.IsNullOrEmpty(shippingMethod))
                ShippingMethod = shippingMethod;
        }

        public void Deliver()
        {
            UpdateStatus(OrderStatus.Delivered);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    public class OrderItem
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductSku { get; set; } = string.Empty;
        public string ProductImage { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Total { get; set; }
        public string VariantInfo { get; set; } = string.Empty;
    }

    public class BillingAddress
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Address1 { get; set; } = string.Empty;
        public string Address2 { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
    }

    public class ShippingAddress
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Company { get; set; } = string.Empty;
        public string Address1 { get; set; } = string.Empty;
        public string Address2 { get; set; } = string.Empty;
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
    }

    public class OrderStatusHistory
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public string FromStatus { get; set; } = string.Empty;
        public string ToStatus { get; set; } = string.Empty;
        public string Note { get; set; } = string.Empty;
        public DateTime ChangedAt { get; set; } = DateTime.Now;
        public string ChangedBy { get; set; } = string.Empty;
    }

    #endregion

    #region Enums

    public enum OrderStatus
    {
        Pending,        // معلق
        Processing,     // قيد المعالجة
        Shipped,        // تم الشحن
        Delivered,      // تم التسليم
        Cancelled,      // ملغي
        Refunded        // مسترد
    }

    public enum PaymentStatus
    {
        Pending,            // معلق
        Paid,               // مدفوع
        Failed,             // فشل
        Refunded,           // مسترد
        PartiallyRefunded   // مسترد جزئياً
    }

    public enum ShippingStatus
    {
        NotShipped,     // لم يتم الشحن
        Processing,     // قيد التجهيز
        Shipped,        // تم الشحن
        InTransit,      // في الطريق
        Delivered,      // تم التسليم
        Failed          // فشل التسليم
    }

    #endregion

    #region Validation

    public class OnlineOrderValidator : AbstractValidator<OnlineOrder>
    {
        public OnlineOrderValidator()
        {
            RuleFor(o => o.OrderNumber)
                .NotEmpty().WithMessage("رقم الطلب مطلوب")
                .MaximumLength(50).WithMessage("رقم الطلب لا يمكن أن يتجاوز 50 حرف");

            RuleFor(o => o.CustomerEmail)
                .NotEmpty().WithMessage("البريد الإلكتروني مطلوب")
                .EmailAddress().WithMessage("البريد الإلكتروني غير صحيح");

            RuleFor(o => o.TotalAmount)
                .GreaterThan(0).WithMessage("إجمالي الطلب يجب أن يكون أكبر من صفر");

            RuleFor(o => o.Items)
                .NotEmpty().WithMessage("يجب إضافة عنصر واحد على الأقل للطلب");

            RuleFor(o => o.BillingAddress.FirstName)
                .NotEmpty().WithMessage("الاسم الأول مطلوب في عنوان الفوترة");

            RuleFor(o => o.BillingAddress.Address1)
                .NotEmpty().WithMessage("العنوان مطلوب في عنوان الفوترة");

            RuleFor(o => o.ShippingAddress.FirstName)
                .NotEmpty().WithMessage("الاسم الأول مطلوب في عنوان الشحن");

            RuleFor(o => o.ShippingAddress.Address1)
                .NotEmpty().WithMessage("العنوان مطلوب في عنوان الشحن");
        }
    }

    #endregion
}
