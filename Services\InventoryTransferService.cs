using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة نقل المخزون بين المخازن
    /// </summary>
    public class InventoryTransferService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;
        private readonly InventoryService _inventoryService;

        public InventoryTransferService(DatabaseService dbService, NotificationService notificationService, InventoryService inventoryService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
            _inventoryService = inventoryService ?? throw new ArgumentNullException(nameof(inventoryService));
        }

        #region Transfer Management

        /// <summary>
        /// إنشاء طلب نقل جديد
        /// </summary>
        public async Task<int> CreateTransferAsync(InventoryTransfer transfer)
        {
            try
            {
                // توليد رقم النقل
                if (string.IsNullOrEmpty(transfer.TransferNumber))
                {
                    transfer.TransferNumber = await GenerateTransferNumberAsync();
                }

                // حساب القيمة الإجمالية
                transfer.CalculateTotalValue();

                const string sql = @"
                    INSERT INTO InventoryTransfers (
                        TransferNumber, TransferType, Status, FromWarehouseId, ToWarehouseId,
                        FromLocationId, ToLocationId, RequestDate, ScheduledDate, Reason,
                        Notes, RequestedBy, TotalValue, CreatedAt
                    ) VALUES (
                        @TransferNumber, @TransferType, @Status, @FromWarehouseId, @ToWarehouseId,
                        @FromLocationId, @ToLocationId, @RequestDate, @ScheduledDate, @Reason,
                        @Notes, @RequestedBy, @TotalValue, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    transfer.TransferNumber,
                    TransferType = transfer.TransferType.ToString(),
                    Status = transfer.Status.ToString(),
                    transfer.FromWarehouseId,
                    transfer.ToWarehouseId,
                    transfer.FromLocationId,
                    transfer.ToLocationId,
                    RequestDate = transfer.RequestDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    ScheduledDate = transfer.ScheduledDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    transfer.Reason,
                    transfer.Notes,
                    transfer.RequestedBy,
                    transfer.TotalValue,
                    CreatedAt = transfer.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var transferId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                transfer.Id = transferId;

                // إضافة عناصر النقل
                foreach (var item in transfer.Items)
                {
                    item.TransferId = transferId;
                    await AddTransferItemAsync(item);
                }

                // إرسال إشعار
                await SendTransferNotificationAsync(transfer, "تم إنشاء طلب نقل جديد");

                LoggingService.LogInfo($"تم إنشاء طلب نقل جديد: {transfer.TransferNumber}");
                return transferId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء طلب النقل: {transfer?.TransferNumber}");
                throw;
            }
        }

        /// <summary>
        /// تحديث طلب النقل
        /// </summary>
        public async Task<bool> UpdateTransferAsync(InventoryTransfer transfer)
        {
            try
            {
                transfer.UpdatedAt = DateTime.Now;
                transfer.CalculateTotalValue();

                const string sql = @"
                    UPDATE InventoryTransfers SET
                        TransferType = @TransferType, Status = @Status, FromWarehouseId = @FromWarehouseId,
                        ToWarehouseId = @ToWarehouseId, FromLocationId = @FromLocationId, ToLocationId = @ToLocationId,
                        RequestDate = @RequestDate, ScheduledDate = @ScheduledDate, ShippedDate = @ShippedDate,
                        ReceivedDate = @ReceivedDate, Reason = @Reason, Notes = @Notes, RequestedBy = @RequestedBy,
                        ApprovedBy = @ApprovedBy, ShippedBy = @ShippedBy, ReceivedBy = @ReceivedBy,
                        TotalValue = @TotalValue, TrackingNumber = @TrackingNumber, CarrierName = @CarrierName,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                var parameters = new
                {
                    transfer.Id,
                    TransferType = transfer.TransferType.ToString(),
                    Status = transfer.Status.ToString(),
                    transfer.FromWarehouseId,
                    transfer.ToWarehouseId,
                    transfer.FromLocationId,
                    transfer.ToLocationId,
                    RequestDate = transfer.RequestDate.ToString("yyyy-MM-dd HH:mm:ss"),
                    ScheduledDate = transfer.ScheduledDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    ShippedDate = transfer.ShippedDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    ReceivedDate = transfer.ReceivedDate?.ToString("yyyy-MM-dd HH:mm:ss"),
                    transfer.Reason,
                    transfer.Notes,
                    transfer.RequestedBy,
                    transfer.ApprovedBy,
                    transfer.ShippedBy,
                    transfer.ReceivedBy,
                    transfer.TotalValue,
                    transfer.TrackingNumber,
                    transfer.CarrierName,
                    UpdatedAt = transfer.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);

                if (rowsAffected > 0)
                {
                    LoggingService.LogInfo($"تم تحديث طلب النقل: {transfer.TransferNumber}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث طلب النقل: {transfer?.Id}");
                throw;
            }
        }

        /// <summary>
        /// الموافقة على طلب النقل
        /// </summary>
        public async Task<bool> ApproveTransferAsync(int transferId, string approvedBy)
        {
            try
            {
                var transfer = await GetTransferByIdAsync(transferId);
                if (transfer == null || transfer.Status != TransferStatus.Pending)
                    return false;

                // التحقق من توفر المخزون
                foreach (var item in transfer.Items)
                {
                    var availableQuantity = await _inventoryService.GetAvailableQuantityAsync(item.ProductId, transfer.FromWarehouseId);
                    if (availableQuantity < item.Quantity)
                    {
                        throw new InvalidOperationException($"الكمية المتاحة للمنتج {item.ProductName} غير كافية");
                    }
                }

                // حجز المخزون
                foreach (var item in transfer.Items)
                {
                    await _inventoryService.ReserveInventoryAsync(item.ProductId, transfer.FromWarehouseId, item.Quantity);
                }

                transfer.Approve(approvedBy);
                var success = await UpdateTransferAsync(transfer);

                if (success)
                {
                    await SendTransferNotificationAsync(transfer, "تم الموافقة على طلب النقل");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الموافقة على طلب النقل: {transferId}");
                throw;
            }
        }

        /// <summary>
        /// شحن طلب النقل
        /// </summary>
        public async Task<bool> ShipTransferAsync(int transferId, string shippedBy, string trackingNumber = "", string carrierName = "")
        {
            try
            {
                var transfer = await GetTransferByIdAsync(transferId);
                if (transfer == null || transfer.Status != TransferStatus.Approved)
                    return false;

                transfer.Ship(shippedBy, trackingNumber, carrierName);
                var success = await UpdateTransferAsync(transfer);

                if (success)
                {
                    await SendTransferNotificationAsync(transfer, "تم شحن طلب النقل");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في شحن طلب النقل: {transferId}");
                throw;
            }
        }

        /// <summary>
        /// استلام طلب النقل
        /// </summary>
        public async Task<bool> ReceiveTransferAsync(int transferId, string receivedBy)
        {
            try
            {
                var transfer = await GetTransferByIdAsync(transferId);
                if (transfer == null || transfer.Status != TransferStatus.Shipped)
                    return false;

                // تنفيذ النقل الفعلي للمخزون
                foreach (var item in transfer.Items)
                {
                    // خصم من المخزن المصدر
                    await _inventoryService.RemoveInventoryAsync(item.ProductId, transfer.FromWarehouseId, 
                        transfer.FromLocationId, item.Quantity);

                    // إضافة للمخزن الوجهة
                    await _inventoryService.AddInventoryAsync(item.ProductId, transfer.ToWarehouseId, 
                        transfer.ToLocationId, item.Quantity, item.UnitCost);
                }

                transfer.Receive(receivedBy);
                var success = await UpdateTransferAsync(transfer);

                if (success)
                {
                    await SendTransferNotificationAsync(transfer, "تم استلام طلب النقل بنجاح");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في استلام طلب النقل: {transferId}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء طلب النقل
        /// </summary>
        public async Task<bool> CancelTransferAsync(int transferId, string reason = "")
        {
            try
            {
                var transfer = await GetTransferByIdAsync(transferId);
                if (transfer == null || transfer.Status == TransferStatus.Received)
                    return false;

                // إلغاء حجز المخزون إذا كان موافق عليه
                if (transfer.Status == TransferStatus.Approved || transfer.Status == TransferStatus.Shipped)
                {
                    foreach (var item in transfer.Items)
                    {
                        await _inventoryService.ReleaseReservedInventoryAsync(item.ProductId, transfer.FromWarehouseId, item.Quantity);
                    }
                }

                transfer.Cancel(reason);
                var success = await UpdateTransferAsync(transfer);

                if (success)
                {
                    await SendTransferNotificationAsync(transfer, "تم إلغاء طلب النقل");
                }

                return success;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إلغاء طلب النقل: {transferId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على طلب النقل بالمعرف
        /// </summary>
        public async Task<InventoryTransfer?> GetTransferByIdAsync(int transferId)
        {
            try
            {
                const string sql = "SELECT * FROM InventoryTransfers WHERE Id = @Id";
                var transferData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = transferId });

                if (transferData != null)
                {
                    var transfer = MapToInventoryTransfer(transferData);
                    
                    // تحميل عناصر النقل
                    transfer.Items = new System.Collections.ObjectModel.ObservableCollection<InventoryTransferItem>(
                        await GetTransferItemsAsync(transferId));

                    return transfer;
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على طلب النقل: {transferId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على جميع طلبات النقل
        /// </summary>
        public async Task<IEnumerable<InventoryTransfer>> GetAllTransfersAsync(int limit = 100, int offset = 0)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InventoryTransfers 
                    ORDER BY CreatedAt DESC 
                    LIMIT @Limit OFFSET @Offset";

                var transfersData = await _dbService.QueryAsync<dynamic>(sql, new { Limit = limit, Offset = offset });
                return transfersData.Select(MapToInventoryTransfer);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على طلبات النقل");
                throw;
            }
        }

        /// <summary>
        /// البحث في طلبات النقل
        /// </summary>
        public async Task<IEnumerable<InventoryTransfer>> SearchTransfersAsync(string searchTerm, TransferStatus? status = null, TransferType? type = null)
        {
            try
            {
                var sql = @"
                    SELECT * FROM InventoryTransfers 
                    WHERE (TransferNumber LIKE @SearchTerm OR Reason LIKE @SearchTerm OR Notes LIKE @SearchTerm)";

                var parameters = new Dictionary<string, object>
                {
                    ["SearchTerm"] = $"%{searchTerm}%"
                };

                if (status.HasValue)
                {
                    sql += " AND Status = @Status";
                    parameters["Status"] = status.ToString();
                }

                if (type.HasValue)
                {
                    sql += " AND TransferType = @TransferType";
                    parameters["TransferType"] = type.ToString();
                }

                sql += " ORDER BY CreatedAt DESC";

                var transfersData = await _dbService.QueryAsync<dynamic>(sql, parameters);
                return transfersData.Select(MapToInventoryTransfer);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في البحث في طلبات النقل: {searchTerm}");
                throw;
            }
        }

        #endregion

        #region Transfer Items

        /// <summary>
        /// إضافة عنصر نقل
        /// </summary>
        public async Task<int> AddTransferItemAsync(InventoryTransferItem item)
        {
            try
            {
                const string sql = @"
                    INSERT INTO InventoryTransferItems (
                        TransferId, ProductId, ProductName, ProductCode, Quantity, UnitCost, TotalValue, Notes
                    ) VALUES (
                        @TransferId, @ProductId, @ProductName, @ProductCode, @Quantity, @UnitCost, @TotalValue, @Notes
                    );
                    SELECT last_insert_rowid();";

                var itemId = await _dbService.QuerySingleAsync<int>(sql, item);
                item.Id = itemId;

                return itemId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إضافة عنصر النقل");
                throw;
            }
        }

        /// <summary>
        /// الحصول على عناصر النقل
        /// </summary>
        public async Task<IEnumerable<InventoryTransferItem>> GetTransferItemsAsync(int transferId)
        {
            try
            {
                const string sql = "SELECT * FROM InventoryTransferItems WHERE TransferId = @TransferId";
                return await _dbService.QueryAsync<InventoryTransferItem>(sql, new { TransferId = transferId });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على عناصر النقل: {transferId}");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// توليد رقم نقل جديد
        /// </summary>
        private async Task<string> GenerateTransferNumberAsync()
        {
            try
            {
                const string sql = "SELECT COUNT(*) FROM InventoryTransfers WHERE DATE(CreatedAt) = DATE('now')";
                var todayCount = await _dbService.QuerySingleAsync<int>(sql);
                
                var today = DateTime.Now;
                return $"TRF-{today:yyyyMMdd}-{(todayCount + 1):D4}";
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في توليد رقم النقل");
                return $"TRF-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج النقل
        /// </summary>
        private InventoryTransfer MapToInventoryTransfer(dynamic data)
        {
            return new InventoryTransfer
            {
                Id = data.Id,
                TransferNumber = data.TransferNumber ?? "",
                TransferType = Enum.TryParse<TransferType>(data.TransferType, out var type) ? type : TransferType.WarehouseToWarehouse,
                Status = Enum.TryParse<TransferStatus>(data.Status, out var status) ? status : TransferStatus.Pending,
                FromWarehouseId = data.FromWarehouseId,
                ToWarehouseId = data.ToWarehouseId,
                FromLocationId = data.FromLocationId,
                ToLocationId = data.ToLocationId,
                RequestDate = DateTime.TryParse(data.RequestDate, out var requestDate) ? requestDate : DateTime.Now,
                ScheduledDate = DateTime.TryParse(data.ScheduledDate, out var scheduledDate) ? scheduledDate : null,
                ShippedDate = DateTime.TryParse(data.ShippedDate, out var shippedDate) ? shippedDate : null,
                ReceivedDate = DateTime.TryParse(data.ReceivedDate, out var receivedDate) ? receivedDate : null,
                Reason = data.Reason ?? "",
                Notes = data.Notes ?? "",
                RequestedBy = data.RequestedBy ?? "",
                ApprovedBy = data.ApprovedBy ?? "",
                ShippedBy = data.ShippedBy ?? "",
                ReceivedBy = data.ReceivedBy ?? "",
                TotalValue = data.TotalValue ?? 0,
                TrackingNumber = data.TrackingNumber ?? "",
                CarrierName = data.CarrierName ?? "",
                CreatedAt = DateTime.TryParse(data.CreatedAt, out var createdAt) ? createdAt : DateTime.Now,
                UpdatedAt = DateTime.TryParse(data.UpdatedAt, out var updatedAt) ? updatedAt : null
            };
        }

        /// <summary>
        /// إرسال إشعار النقل
        /// </summary>
        private async Task SendTransferNotificationAsync(InventoryTransfer transfer, string message)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "إشعار نقل مخزون",
                    Message = $"{message}: {transfer.TransferNumber}",
                    Type = "InventoryTransfer",
                    Priority = transfer.Status == TransferStatus.Pending ? "High" : "Normal",
                    ActionUrl = $"/transfers/{transfer.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار النقل");
            }
        }

        #endregion
    }
}
