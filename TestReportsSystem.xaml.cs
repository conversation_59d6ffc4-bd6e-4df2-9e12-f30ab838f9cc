using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Services;
using SalesManagementSystem.Views;

namespace SalesManagementSystem
{
    public partial class TestReportsSystem : Window
    {
        private readonly ReportService _reportService;
        private readonly ExportService _exportService;
        private readonly ChartService _chartService;

        public TestReportsSystem()
        {
            InitializeComponent();

            var dbService = new DatabaseService();
            _reportService = new ReportService(dbService);
            _exportService = new ExportService();
            _chartService = new ChartService(_reportService);

            AddResult("نظام التقارير المتقدم جاهز للاختبار");
        }

        #region Report Generation Tests

        private async void SalesReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار تقرير المبيعات...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;

                var salesReport = await _reportService.GetSalesReportAsync(startDate, endDate);

                AddResult($"✅ تم إنشاء تقرير المبيعات بنجاح");
                AddResult($"   الفترة: {startDate:dd/MM/yyyy} - {endDate:dd/MM/yyyy}");
                AddResult($"   إجمالي المبيعات: {salesReport.TotalSales:C}");
                AddResult($"   عدد المبيعات: {salesReport.SalesByMonth.Count}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تقرير المبيعات: {ex.Message}");
            }
        }

        private async void InventoryReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار تقرير المخزون...");

                var inventoryReport = await _reportService.GetInventoryReportAsync();

                AddResult($"✅ تم إنشاء تقرير المخزون بنجاح");
                AddResult($"   إجمالي المنتجات: {inventoryReport.Count}");
                AddResult($"   المنتجات منخفضة المخزون: {inventoryReport.Count(p => p.CurrentStock <= p.MinimumStock)}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تقرير المخزون: {ex.Message}");
            }
        }

        private async void ProfitLossButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار تقرير الأرباح والخسائر...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;

                var profitLoss = await _reportService.GetProfitLossReportAsync(startDate, endDate);

                AddResult($"✅ تم إنشاء تقرير الأرباح والخسائر بنجاح");
                AddResult($"   إجمالي الإيرادات: {profitLoss.TotalRevenue:C}");
                AddResult($"   إجمالي التكاليف: {profitLoss.TotalCOGS:C}");
                AddResult($"   صافي الربح: {profitLoss.NetProfit:C}");
                AddResult($"   هامش الربح: {profitLoss.NetProfitMargin:F2}%");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تقرير الأرباح والخسائر: {ex.Message}");
            }
        }

        private async void CustomerReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار تقرير العملاء...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;

                var customerReport = await _reportService.GetCustomerSalesReportAsync(startDate, endDate);

                AddResult($"✅ تم إنشاء تقرير العملاء بنجاح");
                AddResult($"   عدد العملاء: {customerReport.Count}");
                if (customerReport.Any())
                {
                    var topCustomer = customerReport.First();
                    AddResult($"   أفضل عميل: {topCustomer.CustomerName} ({topCustomer.TotalSpent:C})");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تقرير العملاء: {ex.Message}");
            }
        }

        private async void ProductReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار تقرير المنتجات...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;

                var productReport = await _reportService.GetProductSalesReportAsync(startDate, endDate);

                AddResult($"✅ تم إنشاء تقرير المنتجات بنجاح");
                AddResult($"   عدد المنتجات: {productReport.Count}");
                if (productReport.Any())
                {
                    var topProduct = productReport.First();
                    AddResult($"   أفضل منتج: {topProduct.ProductName} ({topProduct.TotalRevenue:C})");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تقرير المنتجات: {ex.Message}");
            }
        }

        private async void TrendAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🔍 اختبار تحليل الاتجاهات...");

                var trendAnalysis = await _reportService.GetTrendAnalysisReportAsync(6);

                AddResult($"✅ تم إنشاء تحليل الاتجاهات بنجاح");
                AddResult($"   معدل نمو المبيعات: {trendAnalysis.SalesGrowthRate:F2}%");
                AddResult($"   معدل نمو الأرباح: {trendAnalysis.ProfitGrowthRate:F2}%");
                AddResult($"   عدد الأشهر المحللة: {trendAnalysis.SalesTrend.Count}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تحليل الاتجاهات: {ex.Message}");
            }
        }

        #endregion

        #region Export Tests

        private async void ExportExcelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("📊 اختبار تصدير Excel...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;
                var salesReport = await _reportService.GetSalesReportAsync(startDate, endDate);

                var success = await _exportService.ExportSalesReportToExcelAsync(salesReport);

                if (success)
                {
                    AddResult("✅ تم تصدير Excel بنجاح");
                }
                else
                {
                    AddResult("❌ فشل في تصدير Excel");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تصدير Excel: {ex.Message}");
            }
        }

        private async void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("📄 اختبار تصدير PDF...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;
                var salesReport = await _reportService.GetSalesReportAsync(startDate, endDate);

                var success = await _exportService.ExportToPdfAsync(salesReport, "تقرير المبيعات");

                if (success)
                {
                    AddResult("✅ تم تصدير PDF بنجاح");
                }
                else
                {
                    AddResult("❌ فشل في تصدير PDF");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تصدير PDF: {ex.Message}");
            }
        }

        private async void ExportCsvButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("📋 اختبار تصدير CSV...");

                var inventoryReport = await _reportService.GetInventoryReportAsync();

                var success = await _exportService.ExportToCsvAsync<InventoryReportItem>(inventoryReport, "تقرير المخزون");

                if (success)
                {
                    AddResult("✅ تم تصدير CSV بنجاح");
                }
                else
                {
                    AddResult("❌ فشل في تصدير CSV");
                }
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في تصدير CSV: {ex.Message}");
            }
        }

        #endregion

        #region Chart Tests

        private async void LineChartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("📈 اختبار الرسم الخطي...");

                var chartData = await _chartService.GetMonthlySalesChartAsync(DateTime.Now.Year);

                AddResult($"✅ تم إنشاء الرسم الخطي بنجاح");
                AddResult($"   عدد السلاسل: {chartData.Count}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في الرسم الخطي: {ex.Message}");
            }
        }

        private async void PieChartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🥧 اختبار الرسم الدائري...");

                var startDate = DateTime.Today.AddMonths(-1);
                var endDate = DateTime.Today;
                var chartData = await _chartService.GetTopProductsPieChartAsync(startDate, endDate);

                AddResult($"✅ تم إنشاء الرسم الدائري بنجاح");
                AddResult($"   عدد الشرائح: {chartData.Count}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في الرسم الدائري: {ex.Message}");
            }
        }

        private async void BarChartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("📊 اختبار رسم الأعمدة...");

                var chartData = await _chartService.GetProfitLossChartAsync(DateTime.Now.Year);

                AddResult($"✅ تم إنشاء رسم الأعمدة بنجاح");
                AddResult($"   عدد السلاسل: {chartData.Count}");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في رسم الأعمدة: {ex.Message}");
            }
        }

        #endregion

        #region Advanced Features

        private void OpenReportsWindowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🪟 فتح نافذة التقارير...");

                var reportsWindow = new ReportsWindow();
                reportsWindow.Show();

                AddResult("✅ تم فتح نافذة التقارير بنجاح");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في فتح نافذة التقارير: {ex.Message}");
            }
        }

        private async void GenerateTestDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AddResult("🎲 إنشاء بيانات تجريبية...");

                // This would generate test data for reports
                await Task.Delay(1000); // Simulate data generation

                AddResult("✅ تم إنشاء البيانات التجريبية بنجاح");
                AddResult("   تم إنشاء مبيعات وهمية للشهر الماضي");
                AddResult("   تم إنشاء منتجات وعملاء وهميين");
            }
            catch (Exception ex)
            {
                AddResult($"❌ خطأ في إنشاء البيانات التجريبية: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        private void AddResult(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var newText = $"[{timestamp}] {message}\n";

            ResultsTextBox.Text += newText;
            ResultsTextBox.ScrollToEnd();
        }

        #endregion
    }
}
