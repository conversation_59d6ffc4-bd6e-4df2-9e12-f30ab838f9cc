using System;
using System.Collections;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using MaterialDesignThemes.Wpf;

namespace SalesManagementSystem.Controls
{
    public partial class SearchableComboBox : UserControl, INotifyPropertyChanged
    {
        #region Dependency Properties

        public static readonly DependencyProperty ItemsSourceProperty =
            DependencyProperty.Register(nameof(ItemsSource), typeof(IEnumerable), typeof(SearchableComboBox),
                new PropertyMetadata(null, OnItemsSourceChanged));

        public static readonly DependencyProperty SelectedItemProperty =
            DependencyProperty.Register(nameof(SelectedItem), typeof(object), typeof(SearchableComboBox),
                new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemChanged));

        public static readonly DependencyProperty DisplayMemberPathProperty =
            DependencyProperty.Register(nameof(DisplayMemberPath), typeof(string), typeof(SearchableComboBox),
                new PropertyMetadata("Name"));

        public static readonly DependencyProperty HintProperty =
            DependencyProperty.Register(nameof(Hint), typeof(string), typeof(SearchableComboBox),
                new PropertyMetadata("اختر عنصر..."));

        public static readonly DependencyProperty ActionButtonIconProperty =
            DependencyProperty.Register(nameof(ActionButtonIcon), typeof(PackIconKind), typeof(SearchableComboBox),
                new PropertyMetadata(PackIconKind.Plus));

        public static readonly DependencyProperty ActionButtonTooltipProperty =
            DependencyProperty.Register(nameof(ActionButtonTooltip), typeof(string), typeof(SearchableComboBox),
                new PropertyMetadata("إضافة جديد"));

        public static readonly DependencyProperty EnableAddNewProperty =
            DependencyProperty.Register(nameof(EnableAddNew), typeof(bool), typeof(SearchableComboBox),
                new PropertyMetadata(true));

        #endregion

        #region Properties

        public IEnumerable ItemsSource
        {
            get => (IEnumerable)GetValue(ItemsSourceProperty);
            set => SetValue(ItemsSourceProperty, value);
        }

        public object SelectedItem
        {
            get => GetValue(SelectedItemProperty);
            set => SetValue(SelectedItemProperty, value);
        }

        public string DisplayMemberPath
        {
            get => (string)GetValue(DisplayMemberPathProperty);
            set => SetValue(DisplayMemberPathProperty, value);
        }

        public string Hint
        {
            get => (string)GetValue(HintProperty);
            set => SetValue(HintProperty, value);
        }

        public PackIconKind ActionButtonIcon
        {
            get => (PackIconKind)GetValue(ActionButtonIconProperty);
            set => SetValue(ActionButtonIconProperty, value);
        }

        public string ActionButtonTooltip
        {
            get => (string)GetValue(ActionButtonTooltipProperty);
            set => SetValue(ActionButtonTooltipProperty, value);
        }

        public bool EnableAddNew
        {
            get => (bool)GetValue(EnableAddNewProperty);
            set => SetValue(EnableAddNewProperty, value);
        }

        private ObservableCollection<object> _filteredItems = new();
        public ObservableCollection<object> FilteredItems
        {
            get => _filteredItems;
            set
            {
                _filteredItems = value;
                OnPropertyChanged();
            }
        }

        private string _searchText = string.Empty;
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                    _ = FilterItemsAsync();
                }
            }
        }

        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        private bool _showNoResults;
        public bool ShowNoResults
        {
            get => _showNoResults;
            set
            {
                _showNoResults = value;
                OnPropertyChanged();
            }
        }

        #endregion

        #region Events

        public event EventHandler<object> AddNewRequested;
        public event EventHandler<object> ItemSelected;

        #endregion

        #region Constructor

        public SearchableComboBox()
        {
            InitializeComponent();
            DataContext = this;
        }

        #endregion

        #region Event Handlers

        private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SearchableComboBox control)
            {
                control.RefreshItems();
            }
        }

        private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SearchableComboBox control)
            {
                control.ItemSelected?.Invoke(control, e.NewValue);
            }
        }

        private void MainComboBox_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                if (MainComboBox.IsDropDownOpen && FilteredItems.Count > 0)
                {
                    SelectedItem = FilteredItems.First();
                    MainComboBox.IsDropDownOpen = false;
                    e.Handled = true;
                }
            }
            else if (e.Key == Key.Escape)
            {
                MainComboBox.IsDropDownOpen = false;
                SearchText = string.Empty;
                e.Handled = true;
            }
            else if (e.Key == Key.Down && !MainComboBox.IsDropDownOpen)
            {
                MainComboBox.IsDropDownOpen = true;
                e.Handled = true;
            }
        }

        private void MainComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = e.AddedItems[0];
                SelectedItem = selectedItem;
                
                // Update search text to match selected item
                if (selectedItem != null)
                {
                    var displayValue = GetDisplayValue(selectedItem);
                    if (!string.IsNullOrEmpty(displayValue))
                    {
                        SearchText = displayValue;
                    }
                }
            }
        }

        private void MainComboBox_DropDownOpened(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(SearchText))
            {
                RefreshItems();
            }
        }

        private void MainComboBox_DropDownClosed(object sender, EventArgs e)
        {
            ShowNoResults = false;
        }

        private void ActionButton_Click(object sender, RoutedEventArgs e)
        {
            if (EnableAddNew)
            {
                AddNewRequested?.Invoke(this, SearchText);
            }
            else
            {
                // Clear selection
                SelectedItem = null;
                SearchText = string.Empty;
                MainComboBox.Focus();
            }
        }

        #endregion

        #region Methods

        private void RefreshItems()
        {
            if (ItemsSource == null) return;

            FilteredItems.Clear();
            foreach (var item in ItemsSource)
            {
                FilteredItems.Add(item);
            }
            
            ShowNoResults = false;
        }

        private async Task FilterItemsAsync()
        {
            if (ItemsSource == null) return;

            IsLoading = true;
            
            try
            {
                // Simulate async operation for better UX
                await Task.Delay(100);
                
                var searchTerm = SearchText?.ToLower() ?? string.Empty;
                
                FilteredItems.Clear();
                
                if (string.IsNullOrEmpty(searchTerm))
                {
                    foreach (var item in ItemsSource)
                    {
                        FilteredItems.Add(item);
                    }
                }
                else
                {
                    foreach (var item in ItemsSource)
                    {
                        if (MatchesSearchTerm(item, searchTerm))
                        {
                            FilteredItems.Add(item);
                        }
                    }
                }
                
                ShowNoResults = !string.IsNullOrEmpty(searchTerm) && FilteredItems.Count == 0;
                
                // Open dropdown if there are results and user is typing
                if (!string.IsNullOrEmpty(searchTerm) && FilteredItems.Count > 0)
                {
                    MainComboBox.IsDropDownOpen = true;
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool MatchesSearchTerm(object item, string searchTerm)
        {
            if (item == null || string.IsNullOrEmpty(searchTerm)) return true;

            // Search in display member
            var displayValue = GetDisplayValue(item)?.ToLower();
            if (!string.IsNullOrEmpty(displayValue) && displayValue.Contains(searchTerm))
                return true;

            // Search in Code property if exists
            var codeProperty = item.GetType().GetProperty("Code");
            if (codeProperty != null)
            {
                var codeValue = codeProperty.GetValue(item)?.ToString()?.ToLower();
                if (!string.IsNullOrEmpty(codeValue) && codeValue.Contains(searchTerm))
                    return true;
            }

            // Search in Phone property for customers
            var phoneProperty = item.GetType().GetProperty("Phone");
            if (phoneProperty != null)
            {
                var phoneValue = phoneProperty.GetValue(item)?.ToString()?.ToLower();
                if (!string.IsNullOrEmpty(phoneValue) && phoneValue.Contains(searchTerm))
                    return true;
            }

            return false;
        }

        private string GetDisplayValue(object item)
        {
            if (item == null) return string.Empty;

            if (string.IsNullOrEmpty(DisplayMemberPath))
                return item.ToString();

            var property = item.GetType().GetProperty(DisplayMemberPath);
            return property?.GetValue(item)?.ToString() ?? item.ToString();
        }

        public void ClearSelection()
        {
            SelectedItem = null;
            SearchText = string.Empty;
        }

        public void FocusSearch()
        {
            MainComboBox.Focus();
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
