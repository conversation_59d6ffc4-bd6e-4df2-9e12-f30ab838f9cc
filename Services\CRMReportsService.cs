using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة تقارير وتحليلات CRM
    /// </summary>
    public class CRMReportsService
    {
        private readonly DatabaseService _dbService;

        public CRMReportsService(DatabaseService dbService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
        }

        #region Customer Analytics

        /// <summary>
        /// تحليل العملاء حسب الحالة
        /// </summary>
        public async Task<IEnumerable<CustomerStatusAnalytics>> GetCustomerStatusAnalyticsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT 
                        Status,
                        COUNT(*) as Count,
                        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM CrmCustomers), 2) as Percentage
                    FROM CrmCustomers 
                    GROUP BY Status
                    ORDER BY Count DESC";

                var data = await _dbService.QueryAsync<dynamic>(sql);
                return data.Select(d => new CustomerStatusAnalytics
                {
                    Status = d.Status,
                    Count = d.Count,
                    Percentage = d.Percentage
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل حالة العملاء");
                throw;
            }
        }

        /// <summary>
        /// تحليل العملاء حسب المصدر
        /// </summary>
        public async Task<IEnumerable<CustomerSourceAnalytics>> GetCustomerSourceAnalyticsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT 
                        Source,
                        COUNT(*) as Count,
                        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM CrmCustomers), 2) as Percentage,
                        COALESCE(SUM(TotalPurchases), 0) as TotalRevenue
                    FROM CrmCustomers 
                    GROUP BY Source
                    ORDER BY Count DESC";

                var data = await _dbService.QueryAsync<dynamic>(sql);
                return data.Select(d => new CustomerSourceAnalytics
                {
                    Source = d.Source,
                    Count = d.Count,
                    Percentage = d.Percentage,
                    TotalRevenue = d.TotalRevenue
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل مصادر العملاء");
                throw;
            }
        }

        /// <summary>
        /// تحليل نمو العملاء الشهري
        /// </summary>
        public async Task<IEnumerable<CustomerGrowthAnalytics>> GetCustomerGrowthAnalyticsAsync(int months = 12)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        strftime('%Y-%m', CreatedAt) as Month,
                        COUNT(*) as NewCustomers,
                        SUM(COUNT(*)) OVER (ORDER BY strftime('%Y-%m', CreatedAt)) as CumulativeCustomers
                    FROM CrmCustomers 
                    WHERE CreatedAt >= date('now', '-' || @Months || ' months')
                    GROUP BY strftime('%Y-%m', CreatedAt)
                    ORDER BY Month";

                var data = await _dbService.QueryAsync<dynamic>(sql, new { Months = months });
                return data.Select(d => new CustomerGrowthAnalytics
                {
                    Month = d.Month,
                    NewCustomers = d.NewCustomers,
                    CumulativeCustomers = d.CumulativeCustomers
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل نمو العملاء");
                throw;
            }
        }

        #endregion

        #region Opportunity Analytics

        /// <summary>
        /// تحليل الفرص حسب المرحلة
        /// </summary>
        public async Task<IEnumerable<OpportunityStageAnalytics>> GetOpportunityStageAnalyticsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT 
                        Stage,
                        COUNT(*) as Count,
                        COALESCE(SUM(EstimatedValue), 0) as TotalValue,
                        COALESCE(SUM(EstimatedValue * ProbabilityPercentage / 100.0), 0) as WeightedValue,
                        ROUND(AVG(ProbabilityPercentage), 2) as AverageProbability
                    FROM SalesOpportunities 
                    WHERE Status = 'Open'
                    GROUP BY Stage
                    ORDER BY 
                        CASE Stage
                            WHEN 'Prospecting' THEN 1
                            WHEN 'Qualification' THEN 2
                            WHEN 'NeedsAnalysis' THEN 3
                            WHEN 'Proposal' THEN 4
                            WHEN 'Negotiation' THEN 5
                            WHEN 'Closing' THEN 6
                        END";

                var data = await _dbService.QueryAsync<dynamic>(sql);
                return data.Select(d => new OpportunityStageAnalytics
                {
                    Stage = d.Stage,
                    Count = d.Count,
                    TotalValue = d.TotalValue,
                    WeightedValue = d.WeightedValue,
                    AverageProbability = d.AverageProbability
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل مراحل الفرص");
                throw;
            }
        }

        /// <summary>
        /// تحليل أداء الفرص الشهري
        /// </summary>
        public async Task<IEnumerable<OpportunityPerformanceAnalytics>> GetOpportunityPerformanceAnalyticsAsync(int months = 12)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        strftime('%Y-%m', CreatedAt) as Month,
                        COUNT(*) as TotalOpportunities,
                        SUM(CASE WHEN Status = 'Won' THEN 1 ELSE 0 END) as WonOpportunities,
                        SUM(CASE WHEN Status = 'Lost' THEN 1 ELSE 0 END) as LostOpportunities,
                        COALESCE(SUM(CASE WHEN Status = 'Won' THEN ActualValue ELSE 0 END), 0) as WonValue,
                        ROUND(
                            SUM(CASE WHEN Status = 'Won' THEN 1 ELSE 0 END) * 100.0 / 
                            NULLIF(SUM(CASE WHEN Status IN ('Won', 'Lost') THEN 1 ELSE 0 END), 0), 2
                        ) as WinRate
                    FROM SalesOpportunities 
                    WHERE CreatedAt >= date('now', '-' || @Months || ' months')
                    GROUP BY strftime('%Y-%m', CreatedAt)
                    ORDER BY Month";

                var data = await _dbService.QueryAsync<dynamic>(sql, new { Months = months });
                return data.Select(d => new OpportunityPerformanceAnalytics
                {
                    Month = d.Month,
                    TotalOpportunities = d.TotalOpportunities,
                    WonOpportunities = d.WonOpportunities,
                    LostOpportunities = d.LostOpportunities,
                    WonValue = d.WonValue,
                    WinRate = d.WinRate ?? 0
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل أداء الفرص");
                throw;
            }
        }

        #endregion

        #region Campaign Analytics

        /// <summary>
        /// تحليل أداء الحملات التسويقية
        /// </summary>
        public async Task<IEnumerable<CampaignPerformanceAnalytics>> GetCampaignPerformanceAnalyticsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT 
                        Type,
                        COUNT(*) as TotalCampaigns,
                        SUM(CASE WHEN Status = 'Active' THEN 1 ELSE 0 END) as ActiveCampaigns,
                        SUM(CASE WHEN Status = 'Completed' THEN 1 ELSE 0 END) as CompletedCampaigns,
                        COALESCE(SUM(Budget), 0) as TotalBudget,
                        COALESCE(SUM(ActualCost), 0) as TotalCost,
                        COALESCE(SUM(ActualReach), 0) as TotalReach,
                        COALESCE(SUM(Conversions), 0) as TotalConversions,
                        COALESCE(SUM(RevenueGenerated), 0) as TotalRevenue,
                        ROUND(AVG(CASE WHEN ActualCost > 0 THEN ((RevenueGenerated - ActualCost) / ActualCost) * 100 ELSE 0 END), 2) as AverageROI
                    FROM MarketingCampaigns 
                    GROUP BY Type
                    ORDER BY TotalRevenue DESC";

                var data = await _dbService.QueryAsync<dynamic>(sql);
                return data.Select(d => new CampaignPerformanceAnalytics
                {
                    Type = d.Type,
                    TotalCampaigns = d.TotalCampaigns,
                    ActiveCampaigns = d.ActiveCampaigns,
                    CompletedCampaigns = d.CompletedCampaigns,
                    TotalBudget = d.TotalBudget,
                    TotalCost = d.TotalCost,
                    TotalReach = d.TotalReach,
                    TotalConversions = d.TotalConversions,
                    TotalRevenue = d.TotalRevenue,
                    AverageROI = d.AverageROI ?? 0
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل أداء الحملات");
                throw;
            }
        }

        #endregion

        #region Sales Analytics

        /// <summary>
        /// تحليل أداء المبيعات الشهري
        /// </summary>
        public async Task<IEnumerable<SalesPerformanceAnalytics>> GetSalesPerformanceAnalyticsAsync(int months = 12)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        strftime('%Y-%m', o.OrderDate) as Month,
                        COUNT(o.Id) as TotalOrders,
                        COALESCE(SUM(o.TotalAmount), 0) as TotalRevenue,
                        COALESCE(AVG(o.TotalAmount), 0) as AverageOrderValue,
                        COUNT(DISTINCT o.CustomerId) as UniqueCustomers,
                        COUNT(DISTINCT CASE WHEN c.CreatedAt >= date('now', '-30 days') THEN o.CustomerId END) as NewCustomers
                    FROM Orders o
                    LEFT JOIN CrmCustomers c ON o.CustomerId = c.Id
                    WHERE o.OrderDate >= date('now', '-' || @Months || ' months')
                    GROUP BY strftime('%Y-%m', o.OrderDate)
                    ORDER BY Month";

                var data = await _dbService.QueryAsync<dynamic>(sql, new { Months = months });
                return data.Select(d => new SalesPerformanceAnalytics
                {
                    Month = d.Month,
                    TotalOrders = d.TotalOrders,
                    TotalRevenue = d.TotalRevenue,
                    AverageOrderValue = d.AverageOrderValue,
                    UniqueCustomers = d.UniqueCustomers,
                    NewCustomers = d.NewCustomers ?? 0
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحليل أداء المبيعات");
                throw;
            }
        }

        #endregion

        #region Dashboard Summary

        /// <summary>
        /// ملخص لوحة التحكم
        /// </summary>
        public async Task<CRMDashboardSummary> GetDashboardSummaryAsync()
        {
            try
            {
                var summary = new CRMDashboardSummary();

                // إحصائيات العملاء
                const string customersSql = @"
                    SELECT 
                        COUNT(*) as TotalCustomers,
                        SUM(CASE WHEN Status = 'Active' THEN 1 ELSE 0 END) as ActiveCustomers,
                        SUM(CASE WHEN CreatedAt >= date('now', '-30 days') THEN 1 ELSE 0 END) as NewCustomersThisMonth,
                        COALESCE(SUM(TotalPurchases), 0) as TotalCustomerValue
                    FROM CrmCustomers";

                var customerData = await _dbService.QuerySingleAsync<dynamic>(customersSql);
                summary.TotalCustomers = customerData.TotalCustomers;
                summary.ActiveCustomers = customerData.ActiveCustomers;
                summary.NewCustomersThisMonth = customerData.NewCustomersThisMonth;
                summary.TotalCustomerValue = customerData.TotalCustomerValue;

                // إحصائيات الفرص
                const string opportunitiesSql = @"
                    SELECT 
                        COUNT(*) as TotalOpportunities,
                        SUM(CASE WHEN Status = 'Open' THEN 1 ELSE 0 END) as OpenOpportunities,
                        SUM(CASE WHEN Status = 'Won' THEN 1 ELSE 0 END) as WonOpportunities,
                        COALESCE(SUM(CASE WHEN Status = 'Open' THEN EstimatedValue ELSE 0 END), 0) as PipelineValue,
                        COALESCE(SUM(CASE WHEN Status = 'Won' THEN ActualValue ELSE 0 END), 0) as WonValue,
                        ROUND(
                            SUM(CASE WHEN Status = 'Won' THEN 1 ELSE 0 END) * 100.0 / 
                            NULLIF(SUM(CASE WHEN Status IN ('Won', 'Lost') THEN 1 ELSE 0 END), 0), 2
                        ) as WinRate
                    FROM SalesOpportunities";

                var opportunityData = await _dbService.QuerySingleAsync<dynamic>(opportunitiesSql);
                summary.TotalOpportunities = opportunityData.TotalOpportunities;
                summary.OpenOpportunities = opportunityData.OpenOpportunities;
                summary.WonOpportunities = opportunityData.WonOpportunities;
                summary.PipelineValue = opportunityData.PipelineValue;
                summary.WonValue = opportunityData.WonValue;
                summary.WinRate = opportunityData.WinRate ?? 0;

                // إحصائيات الحملات
                const string campaignsSql = @"
                    SELECT 
                        COUNT(*) as TotalCampaigns,
                        SUM(CASE WHEN Status = 'Active' THEN 1 ELSE 0 END) as ActiveCampaigns,
                        COALESCE(SUM(Budget), 0) as TotalBudget,
                        COALESCE(SUM(ActualCost), 0) as TotalSpent,
                        COALESCE(SUM(LeadsGenerated), 0) as TotalLeads,
                        COALESCE(SUM(Conversions), 0) as TotalConversions
                    FROM MarketingCampaigns";

                var campaignData = await _dbService.QuerySingleAsync<dynamic>(campaignsSql);
                summary.TotalCampaigns = campaignData.TotalCampaigns;
                summary.ActiveCampaigns = campaignData.ActiveCampaigns;
                summary.TotalMarketingBudget = campaignData.TotalBudget;
                summary.TotalMarketingSpent = campaignData.TotalSpent;
                summary.TotalLeadsGenerated = campaignData.TotalLeads;
                summary.TotalConversions = campaignData.TotalConversions;

                // حساب المعدلات
                if (summary.TotalCustomers > 0)
                {
                    summary.CustomerRetentionRate = (decimal)summary.ActiveCustomers / summary.TotalCustomers * 100;
                }

                if (summary.TotalLeadsGenerated > 0)
                {
                    summary.LeadConversionRate = (decimal)summary.TotalConversions / summary.TotalLeadsGenerated * 100;
                }

                if (summary.TotalMarketingBudget > 0)
                {
                    summary.MarketingBudgetUtilization = summary.TotalMarketingSpent / summary.TotalMarketingBudget * 100;
                }

                return summary;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على ملخص لوحة التحكم");
                throw;
            }
        }

        #endregion

        #region Top Performers

        /// <summary>
        /// أفضل العملاء أداءً
        /// </summary>
        public async Task<IEnumerable<TopCustomer>> GetTopCustomersAsync(int count = 10)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        Id,
                        CASE 
                            WHEN CustomerType = 'Company' THEN CompanyName 
                            ELSE ContactPerson 
                        END as Name,
                        TotalPurchases,
                        TotalOrders,
                        AverageOrderValue,
                        LastOrderDate
                    FROM CrmCustomers 
                    WHERE TotalPurchases > 0
                    ORDER BY TotalPurchases DESC
                    LIMIT @Count";

                var data = await _dbService.QueryAsync<dynamic>(sql, new { Count = count });
                return data.Select(d => new TopCustomer
                {
                    Id = d.Id,
                    Name = d.Name,
                    TotalPurchases = d.TotalPurchases,
                    TotalOrders = d.TotalOrders,
                    AverageOrderValue = d.AverageOrderValue,
                    LastOrderDate = d.LastOrderDate != null ? DateTime.Parse(d.LastOrderDate) : (DateTime?)null
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على أفضل العملاء");
                throw;
            }
        }

        /// <summary>
        /// أفضل الفرص قيمة
        /// </summary>
        public async Task<IEnumerable<TopOpportunity>> GetTopOpportunitiesAsync(int count = 10)
        {
            try
            {
                const string sql = @"
                    SELECT 
                        o.Id,
                        o.Title,
                        o.EstimatedValue,
                        o.ProbabilityPercentage,
                        o.Stage,
                        o.ExpectedCloseDate,
                        c.CompanyName as CustomerName
                    FROM SalesOpportunities o
                    LEFT JOIN CrmCustomers c ON o.CustomerId = c.Id
                    WHERE o.Status = 'Open'
                    ORDER BY o.EstimatedValue DESC
                    LIMIT @Count";

                var data = await _dbService.QueryAsync<dynamic>(sql, new { Count = count });
                return data.Select(d => new TopOpportunity
                {
                    Id = d.Id,
                    Title = d.Title,
                    EstimatedValue = d.EstimatedValue,
                    ProbabilityPercentage = d.ProbabilityPercentage,
                    Stage = d.Stage,
                    ExpectedCloseDate = DateTime.Parse(d.ExpectedCloseDate),
                    CustomerName = d.CustomerName
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على أفضل الفرص");
                throw;
            }
        }

        #endregion

        #region Export Functions

        /// <summary>
        /// تصدير تقرير العملاء
        /// </summary>
        public async Task<byte[]> ExportCustomersReportAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var whereClause = "";
                var parameters = new Dictionary<string, object>();

                if (fromDate.HasValue)
                {
                    whereClause += " AND CreatedAt >= @FromDate";
                    parameters["FromDate"] = fromDate.Value.ToString("yyyy-MM-dd");
                }

                if (toDate.HasValue)
                {
                    whereClause += " AND CreatedAt <= @ToDate";
                    parameters["ToDate"] = toDate.Value.ToString("yyyy-MM-dd");
                }

                const string sql = @"
                    SELECT 
                        CustomerNumber,
                        CASE 
                            WHEN CustomerType = 'Company' THEN CompanyName 
                            ELSE ContactPerson 
                        END as Name,
                        CustomerType,
                        Status,
                        Category,
                        PrimaryEmail,
                        PrimaryPhone,
                        TotalPurchases,
                        TotalOrders,
                        CreatedAt
                    FROM CrmCustomers 
                    WHERE 1=1" + whereClause + @"
                    ORDER BY CreatedAt DESC";

                var customers = await _dbService.QueryAsync<dynamic>(sql, parameters);
                
                // هنا يمكن استخدام مكتبة مثل EPPlus لإنشاء ملف Excel
                // أو تحويل البيانات إلى CSV
                return await ExportToExcelAsync(customers, "تقرير العملاء");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير تقرير العملاء");
                throw;
            }
        }

        private async Task<byte[]> ExportToExcelAsync(IEnumerable<dynamic> data, string sheetName)
        {
            // Implementation for Excel export
            // This would use a library like EPPlus or ClosedXML
            await Task.Delay(100); // Placeholder
            return new byte[0]; // Placeholder
        }

        #endregion
    }

    #region Analytics Models

    public class CustomerStatusAnalytics
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
    }

    public class CustomerSourceAnalytics
    {
        public string Source { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal Percentage { get; set; }
        public decimal TotalRevenue { get; set; }
    }

    public class CustomerGrowthAnalytics
    {
        public string Month { get; set; } = string.Empty;
        public int NewCustomers { get; set; }
        public int CumulativeCustomers { get; set; }
    }

    public class OpportunityStageAnalytics
    {
        public string Stage { get; set; } = string.Empty;
        public int Count { get; set; }
        public decimal TotalValue { get; set; }
        public decimal WeightedValue { get; set; }
        public decimal AverageProbability { get; set; }
    }

    public class OpportunityPerformanceAnalytics
    {
        public string Month { get; set; } = string.Empty;
        public int TotalOpportunities { get; set; }
        public int WonOpportunities { get; set; }
        public int LostOpportunities { get; set; }
        public decimal WonValue { get; set; }
        public decimal WinRate { get; set; }
    }

    public class CampaignPerformanceAnalytics
    {
        public string Type { get; set; } = string.Empty;
        public int TotalCampaigns { get; set; }
        public int ActiveCampaigns { get; set; }
        public int CompletedCampaigns { get; set; }
        public decimal TotalBudget { get; set; }
        public decimal TotalCost { get; set; }
        public int TotalReach { get; set; }
        public int TotalConversions { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageROI { get; set; }
    }

    public class SalesPerformanceAnalytics
    {
        public string Month { get; set; } = string.Empty;
        public int TotalOrders { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageOrderValue { get; set; }
        public int UniqueCustomers { get; set; }
        public int NewCustomers { get; set; }
    }

    public class CRMDashboardSummary
    {
        // Customer Metrics
        public int TotalCustomers { get; set; }
        public int ActiveCustomers { get; set; }
        public int NewCustomersThisMonth { get; set; }
        public decimal TotalCustomerValue { get; set; }
        public decimal CustomerRetentionRate { get; set; }

        // Opportunity Metrics
        public int TotalOpportunities { get; set; }
        public int OpenOpportunities { get; set; }
        public int WonOpportunities { get; set; }
        public decimal PipelineValue { get; set; }
        public decimal WonValue { get; set; }
        public decimal WinRate { get; set; }

        // Campaign Metrics
        public int TotalCampaigns { get; set; }
        public int ActiveCampaigns { get; set; }
        public decimal TotalMarketingBudget { get; set; }
        public decimal TotalMarketingSpent { get; set; }
        public decimal MarketingBudgetUtilization { get; set; }
        public int TotalLeadsGenerated { get; set; }
        public int TotalConversions { get; set; }
        public decimal LeadConversionRate { get; set; }
    }

    public class TopCustomer
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public int TotalOrders { get; set; }
        public decimal AverageOrderValue { get; set; }
        public DateTime? LastOrderDate { get; set; }
    }

    public class TopOpportunity
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public decimal EstimatedValue { get; set; }
        public int ProbabilityPercentage { get; set; }
        public string Stage { get; set; } = string.Empty;
        public DateTime ExpectedCloseDate { get; set; }
        public string CustomerName { get; set; } = string.Empty;
    }

    #endregion
}
