using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة المخزون للمخازن المتعددة
    /// </summary>
    public class InventoryService
    {
        private readonly DatabaseService _dbService;
        private readonly NotificationService _notificationService;

        public InventoryService(DatabaseService dbService, NotificationService notificationService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        }

        #region Inventory Management

        /// <summary>
        /// إضافة مخزون جديد
        /// </summary>
        public async Task<int> AddInventoryAsync(int productId, int warehouseId, int? locationId, decimal quantity, decimal unitCost, 
            string batchNumber = "", string serialNumber = "", DateTime? expiryDate = null)
        {
            try
            {
                // البحث عن مخزون موجود
                var existingInventory = await GetInventoryItemAsync(productId, warehouseId, locationId, batchNumber);
                
                if (existingInventory != null)
                {
                    // تحديث المخزون الموجود
                    existingInventory.AddQuantity(quantity, unitCost);
                    await UpdateInventoryItemAsync(existingInventory);
                    return existingInventory.Id;
                }
                else
                {
                    // إنشاء مخزون جديد
                    var inventoryItem = new InventoryItem
                    {
                        ProductId = productId,
                        WarehouseId = warehouseId,
                        LocationId = locationId,
                        Quantity = quantity,
                        UnitCost = unitCost,
                        BatchNumber = batchNumber,
                        SerialNumber = serialNumber,
                        ExpiryDate = expiryDate,
                        LastUpdated = DateTime.Now
                    };

                    const string sql = @"
                        INSERT INTO InventoryItems (
                            ProductId, WarehouseId, LocationId, Quantity, UnitCost, TotalValue,
                            ReservedQuantity, AvailableQuantity, MinStockLevel, MaxStockLevel,
                            ReorderPoint, Volume, Weight, BatchNumber, SerialNumber, ExpiryDate,
                            LastUpdated, Notes
                        ) VALUES (
                            @ProductId, @WarehouseId, @LocationId, @Quantity, @UnitCost, @TotalValue,
                            @ReservedQuantity, @AvailableQuantity, @MinStockLevel, @MaxStockLevel,
                            @ReorderPoint, @Volume, @Weight, @BatchNumber, @SerialNumber, @ExpiryDate,
                            @LastUpdated, @Notes
                        );
                        SELECT last_insert_rowid();";

                    var parameters = new
                    {
                        inventoryItem.ProductId,
                        inventoryItem.WarehouseId,
                        inventoryItem.LocationId,
                        inventoryItem.Quantity,
                        inventoryItem.UnitCost,
                        inventoryItem.TotalValue,
                        inventoryItem.ReservedQuantity,
                        inventoryItem.AvailableQuantity,
                        inventoryItem.MinStockLevel,
                        inventoryItem.MaxStockLevel,
                        inventoryItem.ReorderPoint,
                        inventoryItem.Volume,
                        inventoryItem.Weight,
                        inventoryItem.BatchNumber,
                        inventoryItem.SerialNumber,
                        ExpiryDate = inventoryItem.ExpiryDate?.ToString("yyyy-MM-dd"),
                        LastUpdated = inventoryItem.LastUpdated.ToString("yyyy-MM-dd HH:mm:ss"),
                        inventoryItem.Notes
                    };

                    var inventoryId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                    inventoryItem.Id = inventoryId;

                    // التحقق من مستوى المخزون
                    await CheckStockLevelsAsync(inventoryItem);

                    LoggingService.LogInfo($"تم إضافة مخزون جديد: المنتج {productId} في المخزن {warehouseId}");
                    return inventoryId;
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إضافة المخزون: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// خصم من المخزون
        /// </summary>
        public async Task<bool> RemoveInventoryAsync(int productId, int warehouseId, int? locationId, decimal quantity)
        {
            try
            {
                var inventoryItem = await GetInventoryItemAsync(productId, warehouseId, locationId);
                
                if (inventoryItem == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود في المخزون");
                }

                if (inventoryItem.AvailableQuantity < quantity)
                {
                    throw new InvalidOperationException("الكمية المطلوبة أكبر من الكمية المتاحة");
                }

                inventoryItem.RemoveQuantity(quantity);
                await UpdateInventoryItemAsync(inventoryItem);

                // التحقق من مستوى المخزون
                await CheckStockLevelsAsync(inventoryItem);

                LoggingService.LogInfo($"تم خصم {quantity} من المنتج {productId} في المخزن {warehouseId}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في خصم المخزون: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// حجز مخزون
        /// </summary>
        public async Task<bool> ReserveInventoryAsync(int productId, int warehouseId, decimal quantity)
        {
            try
            {
                var inventoryItem = await GetInventoryItemAsync(productId, warehouseId);
                
                if (inventoryItem == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود في المخزون");
                }

                inventoryItem.ReserveQuantity(quantity);
                await UpdateInventoryItemAsync(inventoryItem);

                LoggingService.LogInfo($"تم حجز {quantity} من المنتج {productId} في المخزن {warehouseId}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حجز المخزون: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// إلغاء حجز المخزون
        /// </summary>
        public async Task<bool> ReleaseReservedInventoryAsync(int productId, int warehouseId, decimal quantity)
        {
            try
            {
                var inventoryItem = await GetInventoryItemAsync(productId, warehouseId);
                
                if (inventoryItem == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود في المخزون");
                }

                inventoryItem.ReleaseReservedQuantity(quantity);
                await UpdateInventoryItemAsync(inventoryItem);

                LoggingService.LogInfo($"تم إلغاء حجز {quantity} من المنتج {productId} في المخزن {warehouseId}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إلغاء حجز المخزون: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// تسوية المخزون
        /// </summary>
        public async Task<bool> AdjustInventoryAsync(int productId, int warehouseId, int? locationId, decimal newQuantity, string reason)
        {
            try
            {
                var inventoryItem = await GetInventoryItemAsync(productId, warehouseId, locationId);
                
                if (inventoryItem == null)
                {
                    throw new InvalidOperationException("المنتج غير موجود في المخزون");
                }

                var oldQuantity = inventoryItem.Quantity;
                inventoryItem.AdjustQuantity(newQuantity, reason);
                await UpdateInventoryItemAsync(inventoryItem);

                // إرسال إشعار التسوية
                await SendInventoryAdjustmentNotificationAsync(productId, warehouseId, oldQuantity, newQuantity, reason);

                // التحقق من مستوى المخزون
                await CheckStockLevelsAsync(inventoryItem);

                LoggingService.LogInfo($"تم تسوية المخزون للمنتج {productId}: من {oldQuantity} إلى {newQuantity}");
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تسوية المخزون: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// تحديث عنصر المخزون
        /// </summary>
        public async Task<bool> UpdateInventoryItemAsync(InventoryItem inventoryItem)
        {
            try
            {
                inventoryItem.LastUpdated = DateTime.Now;

                const string sql = @"
                    UPDATE InventoryItems SET
                        Quantity = @Quantity, UnitCost = @UnitCost, TotalValue = @TotalValue,
                        ReservedQuantity = @ReservedQuantity, AvailableQuantity = @AvailableQuantity,
                        MinStockLevel = @MinStockLevel, MaxStockLevel = @MaxStockLevel,
                        ReorderPoint = @ReorderPoint, Volume = @Volume, Weight = @Weight,
                        BatchNumber = @BatchNumber, SerialNumber = @SerialNumber,
                        ExpiryDate = @ExpiryDate, LastUpdated = @LastUpdated, Notes = @Notes
                    WHERE Id = @Id";

                var parameters = new
                {
                    inventoryItem.Id,
                    inventoryItem.Quantity,
                    inventoryItem.UnitCost,
                    inventoryItem.TotalValue,
                    inventoryItem.ReservedQuantity,
                    inventoryItem.AvailableQuantity,
                    inventoryItem.MinStockLevel,
                    inventoryItem.MaxStockLevel,
                    inventoryItem.ReorderPoint,
                    inventoryItem.Volume,
                    inventoryItem.Weight,
                    inventoryItem.BatchNumber,
                    inventoryItem.SerialNumber,
                    ExpiryDate = inventoryItem.ExpiryDate?.ToString("yyyy-MM-dd"),
                    LastUpdated = inventoryItem.LastUpdated.ToString("yyyy-MM-dd HH:mm:ss"),
                    inventoryItem.Notes
                };

                var rowsAffected = await _dbService.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث عنصر المخزون: {inventoryItem?.Id}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على عنصر مخزون
        /// </summary>
        public async Task<InventoryItem?> GetInventoryItemAsync(int productId, int warehouseId, int? locationId = null, string batchNumber = "")
        {
            try
            {
                var sql = @"
                    SELECT * FROM InventoryItems 
                    WHERE ProductId = @ProductId AND WarehouseId = @WarehouseId";

                var parameters = new Dictionary<string, object>
                {
                    ["ProductId"] = productId,
                    ["WarehouseId"] = warehouseId
                };

                if (locationId.HasValue)
                {
                    sql += " AND LocationId = @LocationId";
                    parameters["LocationId"] = locationId.Value;
                }

                if (!string.IsNullOrEmpty(batchNumber))
                {
                    sql += " AND BatchNumber = @BatchNumber";
                    parameters["BatchNumber"] = batchNumber;
                }

                sql += " LIMIT 1";

                var inventoryData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, parameters);
                
                if (inventoryData != null)
                {
                    return MapToInventoryItem(inventoryData);
                }

                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على عنصر المخزون: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على الكمية المتاحة للمنتج في مخزن
        /// </summary>
        public async Task<decimal> GetAvailableQuantityAsync(int productId, int warehouseId)
        {
            try
            {
                const string sql = @"
                    SELECT COALESCE(SUM(AvailableQuantity), 0) 
                    FROM InventoryItems 
                    WHERE ProductId = @ProductId AND WarehouseId = @WarehouseId";

                return await _dbService.QuerySingleAsync<decimal>(sql, new { ProductId = productId, WarehouseId = warehouseId });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على الكمية المتاحة: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على إجمالي الكمية للمنتج في جميع المخازن
        /// </summary>
        public async Task<decimal> GetTotalQuantityAsync(int productId)
        {
            try
            {
                const string sql = @"
                    SELECT COALESCE(SUM(Quantity), 0) 
                    FROM InventoryItems 
                    WHERE ProductId = @ProductId";

                return await _dbService.QuerySingleAsync<decimal>(sql, new { ProductId = productId });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على إجمالي الكمية: المنتج {productId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على مخزون المخزن
        /// </summary>
        public async Task<IEnumerable<InventoryItem>> GetWarehouseInventoryAsync(int warehouseId)
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InventoryItems 
                    WHERE WarehouseId = @WarehouseId 
                    ORDER BY LastUpdated DESC";

                var inventoryData = await _dbService.QueryAsync<dynamic>(sql, new { WarehouseId = warehouseId });
                return inventoryData.Select(MapToInventoryItem);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على مخزون المخزن: {warehouseId}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المنتجات منخفضة المخزون
        /// </summary>
        public async Task<IEnumerable<InventoryItem>> GetLowStockItemsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InventoryItems 
                    WHERE AvailableQuantity <= MinStockLevel AND MinStockLevel > 0
                    ORDER BY AvailableQuantity ASC";

                var inventoryData = await _dbService.QueryAsync<dynamic>(sql);
                return inventoryData.Select(MapToInventoryItem);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المنتجات منخفضة المخزون");
                throw;
            }
        }

        /// <summary>
        /// الحصول على المنتجات منتهية الصلاحية
        /// </summary>
        public async Task<IEnumerable<InventoryItem>> GetExpiredItemsAsync()
        {
            try
            {
                const string sql = @"
                    SELECT * FROM InventoryItems 
                    WHERE ExpiryDate IS NOT NULL AND ExpiryDate < DATE('now')
                    ORDER BY ExpiryDate ASC";

                var inventoryData = await _dbService.QueryAsync<dynamic>(sql);
                return inventoryData.Select(MapToInventoryItem);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على المنتجات منتهية الصلاحية");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// التحقق من مستويات المخزون
        /// </summary>
        private async Task CheckStockLevelsAsync(InventoryItem inventoryItem)
        {
            try
            {
                if (inventoryItem.IsOutOfStock)
                {
                    await SendStockNotificationAsync(inventoryItem, "نفد المخزون", "High");
                }
                else if (inventoryItem.IsLowStock)
                {
                    await SendStockNotificationAsync(inventoryItem, "مخزون منخفض", "Medium");
                }
                else if (inventoryItem.NeedsReorder)
                {
                    await SendStockNotificationAsync(inventoryItem, "يحتاج إعادة طلب", "Medium");
                }

                if (inventoryItem.IsExpired)
                {
                    await SendStockNotificationAsync(inventoryItem, "منتهي الصلاحية", "High");
                }
                else if (inventoryItem.IsNearExpiry)
                {
                    await SendStockNotificationAsync(inventoryItem, "قريب الانتهاء", "Medium");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في التحقق من مستويات المخزون");
            }
        }

        /// <summary>
        /// إرسال إشعار المخزون
        /// </summary>
        private async Task SendStockNotificationAsync(InventoryItem inventoryItem, string message, string priority)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "تنبيه مخزون",
                    Message = $"{message}: المنتج {inventoryItem.ProductId} في المخزن {inventoryItem.WarehouseId}",
                    Type = "Inventory",
                    Priority = priority,
                    ActionUrl = $"/inventory/{inventoryItem.Id}",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار المخزون");
            }
        }

        /// <summary>
        /// إرسال إشعار تسوية المخزون
        /// </summary>
        private async Task SendInventoryAdjustmentNotificationAsync(int productId, int warehouseId, decimal oldQuantity, decimal newQuantity, string reason)
        {
            try
            {
                var notification = new Notification
                {
                    Title = "تسوية مخزون",
                    Message = $"تم تسوية المنتج {productId} في المخزن {warehouseId} من {oldQuantity} إلى {newQuantity}. السبب: {reason}",
                    Type = "InventoryAdjustment",
                    Priority = "Normal",
                    ActionUrl = $"/inventory/adjustments",
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                await _notificationService.AddNotificationAsync(notification);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إرسال إشعار تسوية المخزون");
            }
        }

        /// <summary>
        /// تحويل البيانات إلى نموذج عنصر المخزون
        /// </summary>
        private InventoryItem MapToInventoryItem(dynamic data)
        {
            return new InventoryItem
            {
                Id = data.Id,
                ProductId = data.ProductId,
                WarehouseId = data.WarehouseId,
                LocationId = data.LocationId,
                Quantity = data.Quantity ?? 0,
                UnitCost = data.UnitCost ?? 0,
                TotalValue = data.TotalValue ?? 0,
                ReservedQuantity = data.ReservedQuantity ?? 0,
                AvailableQuantity = data.AvailableQuantity ?? 0,
                MinStockLevel = data.MinStockLevel ?? 0,
                MaxStockLevel = data.MaxStockLevel ?? 0,
                ReorderPoint = data.ReorderPoint ?? 0,
                Volume = data.Volume ?? 0,
                Weight = data.Weight ?? 0,
                BatchNumber = data.BatchNumber ?? "",
                SerialNumber = data.SerialNumber ?? "",
                ExpiryDate = DateTime.TryParse(data.ExpiryDate, out var expiryDate) ? expiryDate : null,
                LastUpdated = DateTime.TryParse(data.LastUpdated, out var lastUpdated) ? lastUpdated : DateTime.Now,
                Notes = data.Notes ?? ""
            };
        }

        #endregion
    }
}
