using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class SettingsService
    {
        private readonly DatabaseService _dbService;
        private Dictionary<string, string> _cachedSettings;

        public SettingsService(DatabaseService dbService)
        {
            _dbService = dbService;
            _cachedSettings = new Dictionary<string, string>();
        }

        public async Task InitializeAsync()
        {
            // Load all settings into cache
            await RefreshCacheAsync();

            // Apply language setting
            await ApplyLanguageSettingAsync();
        }

        private async Task RefreshCacheAsync()
        {
            const string sql = "SELECT * FROM Settings";
            var settings = await _dbService.QueryAsync<Setting>(sql);

            _cachedSettings.Clear();
            foreach (var setting in settings)
            {
                _cachedSettings[setting.Key] = setting.Value;
            }
        }

        public async Task<IEnumerable<Setting>> GetAllSettingsAsync()
        {
            const string sql = "SELECT * FROM Settings ORDER BY Key";
            return await _dbService.QueryAsync<Setting>(sql);
        }

        public async Task<string> GetSettingAsync(string key, string? defaultValue = null)
        {
            // Try to get from cache first
            if (_cachedSettings.TryGetValue(key, out string? value))
            {
                return value;
            }

            // If not in cache, try to get from database
            const string sql = "SELECT Value FROM Settings WHERE Key = @Key";
            value = await _dbService.QuerySingleOrDefaultAsync<string>(sql, new { Key = key });

            // Update cache
            if (value != null)
            {
                _cachedSettings[key] = value;
                return value;
            }

            // Return default value if setting not found
            return defaultValue ?? string.Empty;
        }

        public async Task<bool> SetSettingAsync(string key, string value)
        {
            // Check if setting exists
            const string checkSql = "SELECT COUNT(*) FROM Settings WHERE Key = @Key";
            int count = await _dbService.QuerySingleOrDefaultAsync<int>(checkSql, new { Key = key });

            bool result;
            if (count > 0)
            {
                // Update existing setting
                const string updateSql = "UPDATE Settings SET Value = @Value, UpdatedAt = @UpdatedAt WHERE Key = @Key";
                result = await _dbService.ExecuteAsync(updateSql, new
                {
                    Key = key,
                    Value = value,
                    UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                }) > 0;
            }
            else
            {
                // Insert new setting
                const string insertSql = "INSERT INTO Settings (Key, Value, CreatedAt) VALUES (@Key, @Value, @CreatedAt)";
                result = await _dbService.ExecuteAsync(insertSql, new
                {
                    Key = key,
                    Value = value,
                    CreatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                }) > 0;
            }

            // Update cache if successful
            if (result)
            {
                _cachedSettings[key] = value;

                // Apply special settings immediately
                if (key == "Language")
                {
                    await ApplyLanguageSettingAsync();
                }
            }

            return result;
        }

        public async Task<bool> DeleteSettingAsync(string key)
        {
            const string sql = "DELETE FROM Settings WHERE Key = @Key";
            bool result = await _dbService.ExecuteAsync(sql, new { Key = key }) > 0;

            // Remove from cache if successful
            if (result && _cachedSettings.ContainsKey(key))
            {
                _cachedSettings.Remove(key);
            }

            return result;
        }

        #region Application Settings

        public async Task<string> GetCompanyNameAsync()
        {
            return await GetSettingAsync("CompanyName", "Sales Management System");
        }

        public async Task<string> GetCompanyAddressAsync()
        {
            return await GetSettingAsync("CompanyAddress", "");
        }

        public async Task<string> GetCompanyPhoneAsync()
        {
            return await GetSettingAsync("CompanyPhone", "");
        }

        public async Task<string> GetCompanyEmailAsync()
        {
            return await GetSettingAsync("CompanyEmail", "");
        }

        public async Task<string> GetCurrencySymbolAsync()
        {
            return await GetSettingAsync("CurrencySymbol", "$");
        }

        public async Task<string> GetLanguageAsync()
        {
            return await GetSettingAsync("Language", "en-US");
        }

        public async Task<decimal> GetTaxRateAsync()
        {
            string taxRateStr = await GetSettingAsync("TaxRate", "0");
            if (decimal.TryParse(taxRateStr, out decimal taxRate))
            {
                return taxRate;
            }
            return 0;
        }



        public async Task<bool> GetShowLowStockNotificationsAsync()
        {
            string showNotificationsStr = await GetSettingAsync("ShowLowStockNotifications", "true");
            if (bool.TryParse(showNotificationsStr, out bool showNotifications))
            {
                return showNotifications;
            }
            return true;
        }

        public async Task<string> GetDateFormatAsync()
        {
            return await GetSettingAsync("DateFormat", "yyyy-MM-dd");
        }

        public async Task<string> GetTimeFormatAsync()
        {
            return await GetSettingAsync("TimeFormat", "HH:mm:ss");
        }

        public async Task<string> GetThemeAsync()
        {
            return await GetSettingAsync("Theme", "Light");
        }

        #endregion

        #region Settings Application

        private async Task ApplyLanguageSettingAsync()
        {
            string language = await GetLanguageAsync();
            try
            {
                // Set current culture
                Thread.CurrentThread.CurrentCulture = new CultureInfo(language);
                Thread.CurrentThread.CurrentUICulture = new CultureInfo(language);

                // Set application resources
                ResourceDictionary dict = new ResourceDictionary();
                dict.Source = new Uri($"/Resources/Localization/{language}.xaml", UriKind.Relative);

                // Clear existing dictionaries with the same source
                for (int i = Application.Current.Resources.MergedDictionaries.Count - 1; i >= 0; i--)
                {
                    var resourceDict = Application.Current.Resources.MergedDictionaries[i];
                    if (resourceDict.Source != null && resourceDict.Source.OriginalString.StartsWith("/Resources/Localization/"))
                    {
                        Application.Current.Resources.MergedDictionaries.RemoveAt(i);
                    }
                }

                // Add the new dictionary
                Application.Current.Resources.MergedDictionaries.Add(dict);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تطبيق إعداد اللغة");
            }
        }

        public async Task ApplyThemeAsync()
        {
            string theme = await GetThemeAsync();
            try
            {
                // Set application theme
                ResourceDictionary dict = new ResourceDictionary();
                dict.Source = new Uri($"/Resources/Themes/{theme}Theme.xaml", UriKind.Relative);

                // Clear existing theme dictionaries
                for (int i = Application.Current.Resources.MergedDictionaries.Count - 1; i >= 0; i--)
                {
                    var resourceDict = Application.Current.Resources.MergedDictionaries[i];
                    if (resourceDict.Source != null && resourceDict.Source.OriginalString.StartsWith("/Resources/Themes/"))
                    {
                        Application.Current.Resources.MergedDictionaries.RemoveAt(i);
                    }
                }

                // Add the new dictionary
                Application.Current.Resources.MergedDictionaries.Add(dict);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تطبيق إعداد المظهر");
            }
        }

        #endregion

        #region Additional Methods for ViewModels

        public async Task InitializeDefaultSettingsAsync()
        {
            // Initialize default settings if they don't exist
            var defaultSettings = new Dictionary<string, string>
            {
                { "CompanyName", "شركتي" },
                { "Language", "ar-SA" },
                { "Theme", "Light" },
                { "Currency", "SAR" },
                { "DefaultTaxRate", "0.15" },
                { "LowStockThreshold", "10" },
                { "ShowLowStockAlerts", "true" },
                { "AutoBackupEnabled", "false" },
                { "BackupIntervalDays", "7" },
                { "PrintReceipts", "true" }
            };

            foreach (var setting in defaultSettings)
            {
                var existing = await GetSettingAsync(setting.Key);
                if (string.IsNullOrEmpty(existing))
                {
                    await SetSettingAsync(setting.Key, setting.Value);
                }
            }
        }

        public async Task<string> GetCurrencyAsync()
        {
            return await GetSettingAsync("Currency", "SAR");
        }

        public async Task SetCurrencyAsync(string currency)
        {
            await SetSettingAsync("Currency", currency);
        }

        public async Task<decimal> GetDefaultTaxRateAsync()
        {
            var taxRateStr = await GetSettingAsync("DefaultTaxRate", "0.15");
            if (decimal.TryParse(taxRateStr, out decimal taxRate))
                return taxRate;
            return 0.15m;
        }

        public async Task SetDefaultTaxRateAsync(decimal rate)
        {
            await SetSettingAsync("DefaultTaxRate", rate.ToString());
        }

        public async Task<int> GetLowStockThresholdAsync()
        {
            var thresholdStr = await GetSettingAsync("LowStockThreshold", "10");
            if (int.TryParse(thresholdStr, out int threshold))
                return threshold;
            return 10;
        }

        public async Task SetLowStockThresholdAsync(int threshold)
        {
            await SetSettingAsync("LowStockThreshold", threshold.ToString());
        }

        public async Task<bool> GetShowLowStockAlertsAsync()
        {
            var alertsStr = await GetSettingAsync("ShowLowStockAlerts", "true");
            if (bool.TryParse(alertsStr, out bool alerts))
                return alerts;
            return true;
        }

        public async Task SetShowLowStockAlertsAsync(bool show)
        {
            await SetSettingAsync("ShowLowStockAlerts", show.ToString());
        }

        public async Task<bool> GetAutoBackupEnabledAsync()
        {
            var enabledStr = await GetSettingAsync("AutoBackupEnabled", "false");
            if (bool.TryParse(enabledStr, out bool enabled))
                return enabled;
            return false;
        }

        public async Task SetAutoBackupEnabledAsync(bool enabled)
        {
            await SetSettingAsync("AutoBackupEnabled", enabled.ToString());
        }

        public async Task<int> GetBackupIntervalDaysAsync()
        {
            var daysStr = await GetSettingAsync("BackupIntervalDays", "7");
            if (int.TryParse(daysStr, out int days))
                return days;
            return 7;
        }

        public async Task SetBackupIntervalDaysAsync(int days)
        {
            await SetSettingAsync("BackupIntervalDays", days.ToString());
        }

        public async Task<string> GetBackupPathAsync()
        {
            return await GetSettingAsync("BackupPath", "");
        }

        public async Task SetBackupPathAsync(string path)
        {
            await SetSettingAsync("BackupPath", path);
        }

        public async Task<bool> GetPrintReceiptsAsync()
        {
            var printStr = await GetSettingAsync("PrintReceipts", "true");
            if (bool.TryParse(printStr, out bool print))
                return print;
            return true;
        }

        public async Task SetPrintReceiptsAsync(bool print)
        {
            await SetSettingAsync("PrintReceipts", print.ToString());
        }

        public async Task<string> GetReceiptPrinterAsync()
        {
            return await GetSettingAsync("ReceiptPrinter", "");
        }

        public async Task SetReceiptPrinterAsync(string printer)
        {
            await SetSettingAsync("ReceiptPrinter", printer);
        }

        public async Task SetCompanyNameAsync(string name)
        {
            await SetSettingAsync("CompanyName", name);
        }

        public async Task SetCompanyAddressAsync(string address)
        {
            await SetSettingAsync("CompanyAddress", address);
        }

        public async Task SetCompanyPhoneAsync(string phone)
        {
            await SetSettingAsync("CompanyPhone", phone);
        }

        public async Task SetCompanyEmailAsync(string email)
        {
            await SetSettingAsync("CompanyEmail", email);
        }

        public async Task SetLanguageAsync(string language)
        {
            await SetSettingAsync("Language", language);
        }

        public async Task SetThemeAsync(string theme)
        {
            await SetSettingAsync("Theme", theme);
        }

        public async Task<IEnumerable<Setting>> GetSettingsByCategoryAsync(string category)
        {
            // For now, return all settings since we don't have categories in the current implementation
            var allSettings = await GetAllSettingsAsync();
            return allSettings.Where(s => s.Key.Contains(category, StringComparison.OrdinalIgnoreCase));
        }

        #endregion
    }
}