<Window x:Class="SalesManagementSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة المبيعات" 
        Height="600" 
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}"
        WindowStyle="None"
        AllowsTransparency="True">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisConverter"/>
        
        <Style x:Key="LoginTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
        
        <Style x:Key="LoginPasswordBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}">
            <Setter Property="Margin" Value="0,12"/>
            <Setter Property="Height" Value="56"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        </Style>
    </Window.Resources>

    <Border Background="{DynamicResource MaterialDesignPaper}"
           CornerRadius="8"
           Effect="{DynamicResource MaterialDesignShadowDepth3}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Title Bar -->
            <Border Grid.Row="0" 
                   Background="{DynamicResource PrimaryHueMidBrush}"
                   CornerRadius="8,8,0,0"
                   MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid Height="40">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0"
                              Text="تسجيل الدخول"
                              VerticalAlignment="Center"
                              HorizontalAlignment="Center"
                              Foreground="White"
                              FontWeight="Medium"/>
                    
                    <Button Grid.Column="1"
                           Click="CloseButton_Click"
                           Style="{DynamicResource MaterialDesignIconButton}"
                           Width="40" Height="40"
                           Foreground="White">
                        <materialDesign:PackIcon Kind="Close" Width="20" Height="20"/>
                    </Button>
                </Grid>
            </Border>

            <!-- Login Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel Margin="40,32,40,40">
                    
                    <!-- Logo and Title -->
                    <StackPanel HorizontalAlignment="Center" Margin="0,0,0,32">
                        <materialDesign:PackIcon Kind="ShoppingCart" 
                                               Width="64" Height="64" 
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               HorizontalAlignment="Center"/>
                        <TextBlock Text="نظام إدارة المبيعات" 
                                  Style="{DynamicResource MaterialDesignHeadline6TextBlock}"
                                  HorizontalAlignment="Center"
                                  Margin="0,8,0,0"/>
                        <TextBlock Text="مرحباً بك، يرجى تسجيل الدخول" 
                                  Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                                  HorizontalAlignment="Center"
                                  Opacity="0.7"
                                  Margin="0,4,0,0"/>
                    </StackPanel>

                    <!-- Login Form -->
                    <StackPanel>
                        <!-- Username -->
                        <TextBox x:Name="UsernameTextBox"
                                Style="{StaticResource LoginTextBox}"
                                materialDesign:HintAssist.Hint="اسم المستخدم"
                                Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                                KeyDown="Input_KeyDown">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <!-- Password -->
                        <PasswordBox x:Name="PasswordBox"
                                    Style="{StaticResource LoginPasswordBox}"
                                    materialDesign:HintAssist.Hint="كلمة المرور"
                                    PasswordChanged="PasswordBox_PasswordChanged"
                                    KeyDown="Input_KeyDown">
                            <PasswordBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                            </PasswordBox.InputBindings>
                        </PasswordBox>

                        <!-- Remember Me -->
                        <CheckBox Content="تذكرني"
                                 IsChecked="{Binding RememberMe}"
                                 Style="{DynamicResource MaterialDesignCheckBox}"
                                 Margin="0,16,0,0"/>

                        <!-- Error Message -->
                        <Border Background="#FFEBEE"
                               BorderBrush="#F44336"
                               BorderThickness="1"
                               CornerRadius="4"
                               Padding="12"
                               Margin="0,16,0,0"
                               Visibility="{Binding HasError, Converter={StaticResource BoolToVisConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AlertCircle" 
                                                       Foreground="#F44336"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding ErrorMessage}"
                                          Foreground="#F44336"
                                          TextWrapping="Wrap"
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Login Button -->
                        <Button Command="{Binding LoginCommand}"
                               Style="{DynamicResource MaterialDesignRaisedButton}"
                               Height="48"
                               Margin="0,24,0,16"
                               IsEnabled="{Binding CanLogin}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Login" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"
                                                       Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}, ConverterParameter=Inverted}"/>
                                <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                                           Width="20" Height="20"
                                           IsIndeterminate="True"
                                           Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}"
                                           Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding LoginButtonText}" 
                                          VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Additional Options -->
                        <StackPanel Orientation="Horizontal" 
                                   HorizontalAlignment="Center"
                                   Margin="0,16,0,0">
                            <Button Content="نسيت كلمة المرور؟"
                                   Style="{DynamicResource MaterialDesignFlatButton}"
                                   Command="{Binding ForgotPasswordCommand}"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- Version Info -->
                    <TextBlock Text="الإصدار 1.0.0"
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              HorizontalAlignment="Center"
                              Opacity="0.5"
                              Margin="0,32,0,0"/>
                </StackPanel>
            </ScrollViewer>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="2"
                  Background="#80000000"
                  Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisConverter}}">
                <StackPanel HorizontalAlignment="Center" 
                           VerticalAlignment="Center">
                    <ProgressBar Style="{DynamicResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="True"
                               Width="48" Height="48"/>
                    <TextBlock Text="{Binding LoadingMessage}" 
                              Foreground="White"
                              HorizontalAlignment="Center"
                              Margin="0,16,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
