using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using SalesManagementSystem.Models;
using Dapper;

namespace SalesManagementSystem.Services
{
    /// <summary>
    /// خدمة بوابات الدفع الإلكترونية
    /// </summary>
    public class PaymentGatewayService
    {
        private readonly DatabaseService _dbService;
        private readonly HttpClient _httpClient;
        private readonly NotificationService _notificationService;

        public PaymentGatewayService(
            DatabaseService dbService,
            HttpClient httpClient,
            NotificationService notificationService)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _notificationService = notificationService ?? throw new ArgumentNullException(nameof(notificationService));
        }

        #region Payment Method Management

        /// <summary>
        /// الحصول على جميع طرق الدفع النشطة
        /// </summary>
        public async Task<IEnumerable<PaymentMethod>> GetActivePaymentMethodsAsync()
        {
            try
            {
                const string sql = "SELECT * FROM PaymentMethods WHERE IsActive = 1 ORDER BY SortOrder, Name";
                var methodsData = await _dbService.QueryAsync<dynamic>(sql);
                return methodsData.Select(MapToPaymentMethod);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في الحصول على طرق الدفع النشطة");
                throw;
            }
        }

        /// <summary>
        /// إنشاء طريقة دفع جديدة
        /// </summary>
        public async Task<int> CreatePaymentMethodAsync(PaymentMethod paymentMethod)
        {
            try
            {
                const string sql = @"
                    INSERT INTO PaymentMethods (
                        Name, Code, Description, PaymentType, Provider, ProviderName, ApiEndpoint,
                        MerchantId, ApiKey, SecretKey, PublicKey, IsTestMode, IsActive, RequiresVerification,
                        TransactionFee, TransactionFeePercentage, MinimumAmount, MaximumAmount,
                        SupportedCurrencies, SupportedCountries, LogoUrl, Instructions, SortOrder,
                        CreatedAt, CreatedBy
                    ) VALUES (
                        @Name, @Code, @Description, @PaymentType, @Provider, @ProviderName, @ApiEndpoint,
                        @MerchantId, @ApiKey, @SecretKey, @PublicKey, @IsTestMode, @IsActive, @RequiresVerification,
                        @TransactionFee, @TransactionFeePercentage, @MinimumAmount, @MaximumAmount,
                        @SupportedCurrencies, @SupportedCountries, @LogoUrl, @Instructions, @SortOrder,
                        @CreatedAt, @CreatedBy
                    );
                    SELECT last_insert_rowid();";

                var parameters = new
                {
                    paymentMethod.Name,
                    paymentMethod.Code,
                    paymentMethod.Description,
                    PaymentType = paymentMethod.PaymentType.ToString(),
                    Provider = paymentMethod.Provider.ToString(),
                    paymentMethod.ProviderName,
                    paymentMethod.ApiEndpoint,
                    paymentMethod.MerchantId,
                    paymentMethod.ApiKey,
                    paymentMethod.SecretKey,
                    paymentMethod.PublicKey,
                    paymentMethod.IsTestMode,
                    paymentMethod.IsActive,
                    paymentMethod.RequiresVerification,
                    paymentMethod.TransactionFee,
                    paymentMethod.TransactionFeePercentage,
                    paymentMethod.MinimumAmount,
                    paymentMethod.MaximumAmount,
                    paymentMethod.SupportedCurrencies,
                    paymentMethod.SupportedCountries,
                    paymentMethod.LogoUrl,
                    paymentMethod.Instructions,
                    paymentMethod.SortOrder,
                    CreatedAt = paymentMethod.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    paymentMethod.CreatedBy
                };

                var methodId = await _dbService.QuerySingleAsync<int>(sql, parameters);
                paymentMethod.Id = methodId;

                LoggingService.LogInfo($"تم إنشاء طريقة دفع جديدة: {paymentMethod.Name}");
                return methodId;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في إنشاء طريقة الدفع: {paymentMethod?.Name}");
                throw;
            }
        }

        /// <summary>
        /// تحديث طريقة الدفع
        /// </summary>
        public async Task UpdatePaymentMethodAsync(PaymentMethod paymentMethod)
        {
            try
            {
                paymentMethod.UpdatedAt = DateTime.Now;

                const string sql = @"
                    UPDATE PaymentMethods SET
                        Name = @Name,
                        Description = @Description,
                        IsTestMode = @IsTestMode,
                        IsActive = @IsActive,
                        RequiresVerification = @RequiresVerification,
                        TransactionFee = @TransactionFee,
                        TransactionFeePercentage = @TransactionFeePercentage,
                        MinimumAmount = @MinimumAmount,
                        MaximumAmount = @MaximumAmount,
                        SupportedCurrencies = @SupportedCurrencies,
                        SupportedCountries = @SupportedCountries,
                        LogoUrl = @LogoUrl,
                        Instructions = @Instructions,
                        SortOrder = @SortOrder,
                        UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(sql, new
                {
                    paymentMethod.Id,
                    paymentMethod.Name,
                    paymentMethod.Description,
                    paymentMethod.IsTestMode,
                    paymentMethod.IsActive,
                    paymentMethod.RequiresVerification,
                    paymentMethod.TransactionFee,
                    paymentMethod.TransactionFeePercentage,
                    paymentMethod.MinimumAmount,
                    paymentMethod.MaximumAmount,
                    paymentMethod.SupportedCurrencies,
                    paymentMethod.SupportedCountries,
                    paymentMethod.LogoUrl,
                    paymentMethod.Instructions,
                    paymentMethod.SortOrder,
                    UpdatedAt = paymentMethod.UpdatedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                });

                LoggingService.LogInfo($"تم تحديث طريقة الدفع: {paymentMethod.Name}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تحديث طريقة الدفع: {paymentMethod?.Name}");
                throw;
            }
        }

        #endregion

        #region Payment Processing

        /// <summary>
        /// معالجة الدفع
        /// </summary>
        public async Task<PaymentResult> ProcessPaymentAsync(PaymentRequest request)
        {
            try
            {
                var paymentMethod = await GetPaymentMethodByIdAsync(request.PaymentMethodId);
                if (paymentMethod == null || !paymentMethod.IsActive)
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        ErrorMessage = "طريقة الدفع غير متاحة"
                    };
                }

                // التحقق من صحة المبلغ
                if (!paymentMethod.IsAmountValid(request.Amount))
                {
                    return new PaymentResult
                    {
                        IsSuccess = false,
                        ErrorMessage = $"المبلغ خارج النطاق المسموح ({paymentMethod.FormattedMinimumAmount} - {paymentMethod.FormattedMaximumAmount})"
                    };
                }

                // إنشاء معاملة دفع
                var transaction = paymentMethod.CreateTransaction(request.Amount, request.Currency, request.OrderId);
                transaction.Id = await SaveTransactionAsync(transaction);

                // معالجة الدفع حسب نوع البوابة
                var result = await ProcessPaymentByProviderAsync(paymentMethod, request, transaction);

                // تحديث حالة المعاملة
                transaction.Status = result.IsSuccess ? PaymentStatus.Completed : PaymentStatus.Failed;
                transaction.StatusMessage = result.ErrorMessage ?? "تم بنجاح";
                transaction.ProcessedAt = DateTime.Now;
                transaction.GatewayResponse = result.GatewayResponse ?? "";

                await UpdateTransactionAsync(transaction);

                // إرسال إشعار في حالة الفشل
                if (!result.IsSuccess)
                {
                    await _notificationService.SendNotificationAsync(
                        "فشل في معالجة الدفع",
                        $"فشل في معالجة الدفع للطلب {request.OrderId}: {result.ErrorMessage}",
                        NotificationType.Error
                    );
                }

                result.TransactionId = transaction.TransactionId;
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في معالجة الدفع للطلب: {request?.OrderId}");
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "حدث خطأ في معالجة الدفع"
                };
            }
        }

        /// <summary>
        /// معالجة الدفع حسب مقدم الخدمة
        /// </summary>
        private async Task<PaymentResult> ProcessPaymentByProviderAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction)
        {
            return paymentMethod.Provider switch
            {
                PaymentProvider.Mada => await ProcessMadaPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.Visa => await ProcessVisaPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.Mastercard => await ProcessMastercardPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.STCPay => await ProcessSTCPayPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.Urpay => await ProcessUrpayPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.Tabby => await ProcessTabbyPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.Tamara => await ProcessTamaraPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.ApplePay => await ProcessApplePayPaymentAsync(paymentMethod, request, transaction),
                PaymentProvider.PayPal => await ProcessPayPalPaymentAsync(paymentMethod, request, transaction),
                _ => await ProcessLocalPaymentAsync(paymentMethod, request, transaction)
            };
        }

        /// <summary>
        /// معالجة دفع مدى
        /// </summary>
        private async Task<PaymentResult> ProcessMadaPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction)
        {
            try
            {
                // تكوين طلب مدى
                var madaRequest = new
                {
                    merchant_id = paymentMethod.MerchantId,
                    amount = request.Amount,
                    currency = request.Currency,
                    order_id = request.OrderId,
                    transaction_id = transaction.TransactionId,
                    card_number = request.CardNumber,
                    expiry_month = request.ExpiryMonth,
                    expiry_year = request.ExpiryYear,
                    cvv = request.CVV,
                    cardholder_name = request.CardholderName
                };

                var json = JsonSerializer.Serialize(madaRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // إضافة رؤوس المصادقة
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {paymentMethod.ApiKey}");

                var response = await _httpClient.PostAsync(paymentMethod.ApiEndpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonSerializer.Deserialize<MadaResponse>(responseContent);
                    return new PaymentResult
                    {
                        IsSuccess = result?.Status == "success",
                        TransactionId = result?.TransactionId ?? transaction.TransactionId,
                        ErrorMessage = result?.Message,
                        GatewayResponse = responseContent
                    };
                }

                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "فشل في الاتصال ببوابة مدى",
                    GatewayResponse = responseContent
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في معالجة دفع مدى");
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "خطأ في معالجة دفع مدى"
                };
            }
        }

        /// <summary>
        /// معالجة دفع STC Pay
        /// </summary>
        private async Task<PaymentResult> ProcessSTCPayPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction)
        {
            try
            {
                // تكوين طلب STC Pay
                var stcRequest = new
                {
                    merchant_id = paymentMethod.MerchantId,
                    amount = request.Amount,
                    currency = request.Currency,
                    order_id = request.OrderId,
                    mobile_number = request.MobileNumber,
                    otp = request.OTP
                };

                var json = JsonSerializer.Serialize(stcRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {paymentMethod.ApiKey}");

                var response = await _httpClient.PostAsync(paymentMethod.ApiEndpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = JsonSerializer.Deserialize<STCPayResponse>(responseContent);
                    return new PaymentResult
                    {
                        IsSuccess = result?.Status == "success",
                        TransactionId = result?.TransactionId ?? transaction.TransactionId,
                        ErrorMessage = result?.Message,
                        GatewayResponse = responseContent
                    };
                }

                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "فشل في الاتصال ببوابة STC Pay",
                    GatewayResponse = responseContent
                };
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في معالجة دفع STC Pay");
                return new PaymentResult
                {
                    IsSuccess = false,
                    ErrorMessage = "خطأ في معالجة دفع STC Pay"
                };
            }
        }

        /// <summary>
        /// معالجة الدفع المحلي (الدفع عند الاستلام)
        /// </summary>
        private async Task<PaymentResult> ProcessLocalPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction)
        {
            // للدفع عند الاستلام، نعتبر المعاملة ناجحة مؤقتاً
            return new PaymentResult
            {
                IsSuccess = true,
                TransactionId = transaction.TransactionId,
                ErrorMessage = "سيتم الدفع عند الاستلام"
            };
        }

        #endregion

        #region Helper Methods

        private async Task<PaymentMethod?> GetPaymentMethodByIdAsync(int paymentMethodId)
        {
            try
            {
                const string sql = "SELECT * FROM PaymentMethods WHERE Id = @Id";
                var methodData = await _dbService.QuerySingleOrDefaultAsync<dynamic>(sql, new { Id = paymentMethodId });
                return methodData != null ? MapToPaymentMethod(methodData) : null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في الحصول على طريقة الدفع: {paymentMethodId}");
                return null;
            }
        }

        private async Task<int> SaveTransactionAsync(PaymentTransaction transaction)
        {
            try
            {
                transaction.TransactionId = Guid.NewGuid().ToString();

                const string sql = @"
                    INSERT INTO PaymentTransactions (
                        PaymentMethodId, TransactionId, OrderId, Amount, Currency, TransactionFee,
                        Status, StatusMessage, CreatedAt
                    ) VALUES (
                        @PaymentMethodId, @TransactionId, @OrderId, @Amount, @Currency, @TransactionFee,
                        @Status, @StatusMessage, @CreatedAt
                    );
                    SELECT last_insert_rowid();";

                return await _dbService.QuerySingleAsync<int>(sql, new
                {
                    transaction.PaymentMethodId,
                    transaction.TransactionId,
                    transaction.OrderId,
                    transaction.Amount,
                    transaction.Currency,
                    transaction.TransactionFee,
                    Status = transaction.Status.ToString(),
                    transaction.StatusMessage,
                    CreatedAt = transaction.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في حفظ معاملة الدفع");
                throw;
            }
        }

        private async Task UpdateTransactionAsync(PaymentTransaction transaction)
        {
            try
            {
                const string sql = @"
                    UPDATE PaymentTransactions SET
                        Status = @Status,
                        StatusMessage = @StatusMessage,
                        GatewayResponse = @GatewayResponse,
                        ProcessedAt = @ProcessedAt
                    WHERE Id = @Id";

                await _dbService.ExecuteAsync(sql, new
                {
                    transaction.Id,
                    Status = transaction.Status.ToString(),
                    transaction.StatusMessage,
                    transaction.GatewayResponse,
                    ProcessedAt = transaction.ProcessedAt?.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحديث معاملة الدفع");
                throw;
            }
        }

        private PaymentMethod MapToPaymentMethod(dynamic data)
        {
            var method = new PaymentMethod
            {
                Id = data.Id,
                Name = data.Name ?? "",
                Code = data.Code ?? "",
                Description = data.Description ?? "",
                ProviderName = data.ProviderName ?? "",
                ApiEndpoint = data.ApiEndpoint ?? "",
                MerchantId = data.MerchantId ?? "",
                ApiKey = data.ApiKey ?? "",
                SecretKey = data.SecretKey ?? "",
                PublicKey = data.PublicKey ?? "",
                IsTestMode = data.IsTestMode,
                IsActive = data.IsActive,
                RequiresVerification = data.RequiresVerification,
                TransactionFee = data.TransactionFee,
                TransactionFeePercentage = data.TransactionFeePercentage,
                MinimumAmount = data.MinimumAmount,
                MaximumAmount = data.MaximumAmount,
                SupportedCurrencies = data.SupportedCurrencies ?? "",
                SupportedCountries = data.SupportedCountries ?? "",
                LogoUrl = data.LogoUrl ?? "",
                Instructions = data.Instructions ?? "",
                SortOrder = data.SortOrder,
                CreatedBy = data.CreatedBy ?? ""
            };

            // Parse enums
            if (Enum.TryParse<PaymentType>(data.PaymentType?.ToString(), out var paymentType))
                method.PaymentType = paymentType;

            if (Enum.TryParse<PaymentProvider>(data.Provider?.ToString(), out var provider))
                method.Provider = provider;

            // Parse dates
            if (DateTime.TryParse(data.CreatedAt?.ToString(), out var createdAt))
                method.CreatedAt = createdAt;

            if (DateTime.TryParse(data.UpdatedAt?.ToString(), out var updatedAt))
                method.UpdatedAt = updatedAt;

            return method;
        }

        // Placeholder methods for other payment providers
        private async Task<PaymentResult> ProcessVisaPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);
        private async Task<PaymentResult> ProcessMastercardPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);
        private async Task<PaymentResult> ProcessUrpayPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);
        private async Task<PaymentResult> ProcessTabbyPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);
        private async Task<PaymentResult> ProcessTamaraPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);
        private async Task<PaymentResult> ProcessApplePayPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);
        private async Task<PaymentResult> ProcessPayPalPaymentAsync(PaymentMethod paymentMethod, PaymentRequest request, PaymentTransaction transaction) => await ProcessLocalPaymentAsync(paymentMethod, request, transaction);

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// طلب الدفع
    /// </summary>
    public class PaymentRequest
    {
        public int PaymentMethodId { get; set; }
        public string OrderId { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "SAR";
        public string CardNumber { get; set; } = string.Empty;
        public string ExpiryMonth { get; set; } = string.Empty;
        public string ExpiryYear { get; set; } = string.Empty;
        public string CVV { get; set; } = string.Empty;
        public string CardholderName { get; set; } = string.Empty;
        public string MobileNumber { get; set; } = string.Empty;
        public string OTP { get; set; } = string.Empty;
    }

    /// <summary>
    /// نتيجة الدفع
    /// </summary>
    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string TransactionId { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string GatewayResponse { get; set; } = string.Empty;
    }

    /// <summary>
    /// استجابة مدى
    /// </summary>
    public class MadaResponse
    {
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
    }

    /// <summary>
    /// استجابة STC Pay
    /// </summary>
    public class STCPayResponse
    {
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string TransactionId { get; set; } = string.Empty;
    }

    #endregion
}
