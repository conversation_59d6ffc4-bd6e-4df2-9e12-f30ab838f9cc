using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using iTextSharp.text;
using iTextSharp.text.pdf;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class ExportService
    {
        public ExportService()
        {
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        #region Excel Export

        /// <summary>
        /// تصدير تقرير المبيعات إلى Excel
        /// </summary>
        public async Task<bool> ExportSalesReportToExcelAsync(SalesReport report, string? filePath = null)
        {
            try
            {
                filePath ??= GetSaveFilePath("تقرير المبيعات", "xlsx");
                if (string.IsNullOrEmpty(filePath)) return false;

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المبيعات");

                // Header
                worksheet.Cells["A1"].Value = "تقرير المبيعات";
                worksheet.Cells["A1:F1"].Merge = true;
                worksheet.Cells["A1"].Style.Font.Size = 16;
                worksheet.Cells["A1"].Style.Font.Bold = true;
                worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Period
                worksheet.Cells["A2"].Value = $"الفترة: {report.StartDate:dd/MM/yyyy} - {report.EndDate:dd/MM/yyyy}";
                worksheet.Cells["A2:F2"].Merge = true;
                worksheet.Cells["A2"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Summary
                int row = 4;
                worksheet.Cells[row, 1].Value = "إجمالي المبيعات:";
                worksheet.Cells[row, 2].Value = report.TotalSales;
                worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.00";

                // Sales by Month
                if (report.SalesByMonth.Any())
                {
                    row += 3;
                    worksheet.Cells[row, 1].Value = "المبيعات الشهرية";
                    worksheet.Cells[row, 1].Style.Font.Bold = true;
                    row++;

                    worksheet.Cells[row, 1].Value = "الشهر";
                    worksheet.Cells[row, 2].Value = "المبلغ";
                    worksheet.Cells[row, 1, row, 2].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 2].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    worksheet.Cells[row, 1, row, 2].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    foreach (var monthData in report.SalesByMonth)
                    {
                        row++;
                        worksheet.Cells[row, 1].Value = monthData.MonthName;
                        worksheet.Cells[row, 2].Value = monthData.TotalAmount;
                        worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.00";
                    }
                }

                // Top Products
                if (report.TopProducts.Any())
                {
                    row += 3;
                    worksheet.Cells[row, 1].Value = "أفضل المنتجات مبيعاً";
                    worksheet.Cells[row, 1].Style.Font.Bold = true;
                    row++;

                    worksheet.Cells[row, 1].Value = "المنتج";
                    worksheet.Cells[row, 2].Value = "الكمية";
                    worksheet.Cells[row, 3].Value = "المبلغ";
                    worksheet.Cells[row, 1, row, 3].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 3].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    worksheet.Cells[row, 1, row, 3].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    foreach (var product in report.TopProducts)
                    {
                        row++;
                        worksheet.Cells[row, 1].Value = product.ProductName;
                        worksheet.Cells[row, 2].Value = product.TotalQuantity;
                        worksheet.Cells[row, 3].Value = product.TotalAmount;
                        worksheet.Cells[row, 3].Style.Numberformat.Format = "#,##0.00";
                    }
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Save file
                await package.SaveAsAsync(new FileInfo(filePath));
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير تقرير المبيعات إلى Excel");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// تصدير تقرير المخزون إلى Excel
        /// </summary>
        public async Task<bool> ExportInventoryReportToExcelAsync(InventoryReport report, string? filePath = null)
        {
            try
            {
                filePath ??= GetSaveFilePath("تقرير المخزون", "xlsx");
                if (string.IsNullOrEmpty(filePath)) return false;

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("تقرير المخزون");

                // Header
                worksheet.Cells["A1"].Value = "تقرير المخزون";
                worksheet.Cells["A1:F1"].Merge = true;
                worksheet.Cells["A1"].Style.Font.Size = 16;
                worksheet.Cells["A1"].Style.Font.Bold = true;
                worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                // Summary
                int row = 3;
                worksheet.Cells[row, 1].Value = "إجمالي المنتجات:";
                worksheet.Cells[row, 2].Value = report.TotalProducts;
                row++;
                worksheet.Cells[row, 1].Value = "قيمة المخزون:";
                worksheet.Cells[row, 2].Value = report.TotalStockValue;
                worksheet.Cells[row, 2].Style.Numberformat.Format = "#,##0.00";

                // Low Stock Products
                if (report.LowStockProducts.Any())
                {
                    row += 3;
                    worksheet.Cells[row, 1].Value = "المنتجات منخفضة المخزون";
                    worksheet.Cells[row, 1].Style.Font.Bold = true;
                    row++;

                    // Headers
                    worksheet.Cells[row, 1].Value = "اسم المنتج";
                    worksheet.Cells[row, 2].Value = "الكود";
                    worksheet.Cells[row, 3].Value = "المخزون الحالي";
                    worksheet.Cells[row, 4].Value = "الحد الأدنى";
                    worksheet.Cells[row, 5].Value = "سعر التكلفة";
                    worksheet.Cells[row, 6].Value = "سعر البيع";
                    
                    worksheet.Cells[row, 1, row, 6].Style.Font.Bold = true;
                    worksheet.Cells[row, 1, row, 6].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                    worksheet.Cells[row, 1, row, 6].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);

                    foreach (var product in report.LowStockProducts)
                    {
                        row++;
                        worksheet.Cells[row, 1].Value = product.Name;
                        worksheet.Cells[row, 2].Value = product.Code;
                        worksheet.Cells[row, 3].Value = product.CurrentStock;
                        worksheet.Cells[row, 4].Value = product.MinimumStock;
                        worksheet.Cells[row, 5].Value = product.CostPrice;
                        worksheet.Cells[row, 6].Value = product.SalePrice;
                        
                        worksheet.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        worksheet.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                    }
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Save file
                await package.SaveAsAsync(new FileInfo(filePath));
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير تقرير المخزون إلى Excel");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        #endregion

        #region PDF Export

        /// <summary>
        /// تصدير تقرير إلى PDF
        /// </summary>
        public async Task<bool> ExportToPdfAsync<T>(T reportData, string reportTitle, string? filePath = null)
        {
            try
            {
                filePath ??= GetSaveFilePath(reportTitle, "pdf");
                if (string.IsNullOrEmpty(filePath)) return false;

                using var document = new Document(PageSize.A4, 50, 50, 25, 25);
                using var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
                
                document.Open();

                // Add title
                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
                var title = new Paragraph(reportTitle, titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // Add date
                var dateFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);
                var date = new Paragraph($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}", dateFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(date);

                // Add content based on report type
                await AddReportContentToPdf(document, reportData);

                document.Close();
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير التقرير إلى PDF");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private async Task AddReportContentToPdf<T>(Document document, T reportData)
        {
            var contentFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);
            
            // Convert report data to string representation
            var content = ConvertReportToText(reportData);
            var paragraph = new Paragraph(content, contentFont);
            document.Add(paragraph);
            
            await Task.CompletedTask;
        }

        private string ConvertReportToText<T>(T reportData)
        {
            var sb = new StringBuilder();
            var properties = typeof(T).GetProperties();
            
            foreach (var prop in properties)
            {
                var value = prop.GetValue(reportData);
                sb.AppendLine($"{prop.Name}: {value}");
            }
            
            return sb.ToString();
        }

        #endregion

        #region CSV Export

        /// <summary>
        /// تصدير البيانات إلى CSV
        /// </summary>
        public async Task<bool> ExportToCsvAsync<T>(IEnumerable<T> data, string fileName, string? filePath = null)
        {
            try
            {
                filePath ??= GetSaveFilePath(fileName, "csv");
                if (string.IsNullOrEmpty(filePath)) return false;

                var csv = new StringBuilder();
                var properties = typeof(T).GetProperties();

                // Add headers
                csv.AppendLine(string.Join(",", properties.Select(p => p.Name)));

                // Add data
                foreach (var item in data)
                {
                    var values = properties.Select(p => 
                    {
                        var value = p.GetValue(item)?.ToString() ?? "";
                        return value.Contains(",") ? $"\"{value}\"" : value;
                    });
                    csv.AppendLine(string.Join(",", values));
                }

                await File.WriteAllTextAsync(filePath, csv.ToString(), Encoding.UTF8);
                return true;
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تصدير البيانات إلى CSV");
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        #endregion

        #region Helper Methods

        private string? GetSaveFilePath(string fileName, string extension)
        {
            var saveFileDialog = new SaveFileDialog
            {
                FileName = $"{fileName}_{DateTime.Now:yyyyMMdd_HHmmss}",
                DefaultExt = extension,
                Filter = extension.ToUpper() switch
                {
                    "XLSX" => "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                    "PDF" => "PDF Files (*.pdf)|*.pdf|All Files (*.*)|*.*",
                    "CSV" => "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*",
                    _ => "All Files (*.*)|*.*"
                }
            };

            return saveFileDialog.ShowDialog() == true ? saveFileDialog.FileName : null;
        }

        #endregion
    }
}
