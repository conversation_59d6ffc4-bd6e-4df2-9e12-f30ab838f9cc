using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views
{
    /// <summary>
    /// واجهة إدارة المخازن المتعددة
    /// </summary>
    public partial class WarehouseManagementView : UserControl, INotifyPropertyChanged
    {
        private readonly WarehouseService _warehouseService;
        private readonly InventoryService _inventoryService;
        private readonly NotificationService _notificationService;

        private ObservableCollection<Warehouse> _warehouses = new();
        private string _searchText = string.Empty;
        private WarehouseType? _selectedWarehouseType;
        private WarehouseStatus? _selectedWarehouseStatus;
        private bool _isLoading;

        public WarehouseManagementView()
        {
            InitializeComponent();
            DataContext = this;

            // Initialize services
            var dbService = new DatabaseService();
            var settingsService = new SettingsService(dbService);
            _notificationService = new NotificationService(dbService, settingsService);
            _inventoryService = new InventoryService(dbService, _notificationService);
            _warehouseService = new WarehouseService(dbService, _notificationService);

            InitializeCommands();
            _ = LoadDataAsync();
        }

        #region Properties

        public ObservableCollection<Warehouse> Warehouses
        {
            get => _warehouses;
            set
            {
                if (_warehouses != value)
                {
                    _warehouses = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                }
            }
        }

        public WarehouseType? SelectedWarehouseType
        {
            get => _selectedWarehouseType;
            set
            {
                if (_selectedWarehouseType != value)
                {
                    _selectedWarehouseType = value;
                    OnPropertyChanged();
                }
            }
        }

        public WarehouseStatus? SelectedWarehouseStatus
        {
            get => _selectedWarehouseStatus;
            set
            {
                if (_selectedWarehouseStatus != value)
                {
                    _selectedWarehouseStatus = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (_isLoading != value)
                {
                    _isLoading = value;
                    OnPropertyChanged();
                }
            }
        }

        // Collections for ComboBoxes
        public Array WarehouseTypes => Enum.GetValues(typeof(WarehouseType));
        public Array WarehouseStatuses => Enum.GetValues(typeof(WarehouseStatus));

        #endregion

        #region Commands

        public ICommand AddWarehouseCommand { get; private set; } = null!;
        public ICommand EditWarehouseCommand { get; private set; } = null!;
        public ICommand DeleteWarehouseCommand { get; private set; } = null!;
        public ICommand ViewWarehouseDetailsCommand { get; private set; } = null!;
        public ICommand SearchCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            AddWarehouseCommand = new RelayCommand(async () => await AddWarehouseAsync());
            EditWarehouseCommand = new RelayCommand<Warehouse>(async (warehouse) => await EditWarehouseAsync(warehouse));
            DeleteWarehouseCommand = new RelayCommand<Warehouse>(async (warehouse) => await DeleteWarehouseAsync(warehouse));
            ViewWarehouseDetailsCommand = new RelayCommand<Warehouse>(async (warehouse) => await ViewWarehouseDetailsAsync(warehouse));
            SearchCommand = new RelayCommand(async () => await SearchWarehousesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadDataAsync());
        }

        #endregion

        #region Data Loading

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;

                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                
                // تحميل تفاصيل إضافية لكل مخزن
                foreach (var warehouse in warehouses)
                {
                    // تحميل المواقع
                    var locations = await _warehouseService.GetWarehouseLocationsAsync(warehouse.Id);
                    warehouse.Locations = new ObservableCollection<WarehouseLocation>(locations);

                    // تحميل المخزون
                    var inventory = await _warehouseService.GetWarehouseInventoryAsync(warehouse.Id);
                    warehouse.Inventory = new ObservableCollection<InventoryItem>(inventory);

                    // تحديث الإحصائيات
                    warehouse.UpdateInventory();
                }

                Warehouses = new ObservableCollection<Warehouse>(warehouses);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في تحميل بيانات المخازن");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SearchWarehousesAsync()
        {
            try
            {
                IsLoading = true;

                if (string.IsNullOrWhiteSpace(SearchText) && !SelectedWarehouseType.HasValue && !SelectedWarehouseStatus.HasValue)
                {
                    await LoadDataAsync();
                    return;
                }

                var warehouses = await _warehouseService.SearchWarehousesAsync(SearchText, SelectedWarehouseStatus, SelectedWarehouseType);
                
                // تحميل تفاصيل إضافية لكل مخزن
                foreach (var warehouse in warehouses)
                {
                    var locations = await _warehouseService.GetWarehouseLocationsAsync(warehouse.Id);
                    warehouse.Locations = new ObservableCollection<WarehouseLocation>(locations);

                    var inventory = await _warehouseService.GetWarehouseInventoryAsync(warehouse.Id);
                    warehouse.Inventory = new ObservableCollection<InventoryItem>(inventory);

                    warehouse.UpdateInventory();
                }

                Warehouses = new ObservableCollection<Warehouse>(warehouses);
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في البحث في المخازن");
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        #endregion

        #region Warehouse Operations

        private async Task AddWarehouseAsync()
        {
            try
            {
                var dialog = new AddWarehouseDialog();
                if (dialog.ShowDialog() == true)
                {
                    var warehouse = dialog.Warehouse;
                    var warehouseId = await _warehouseService.AddWarehouseAsync(warehouse);
                    
                    if (warehouseId > 0)
                    {
                        warehouse.Id = warehouseId;
                        Warehouses.Insert(0, warehouse);
                        
                        MessageBox.Show("تم إضافة المخزن بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, "خطأ في إضافة المخزن");
                MessageBox.Show($"خطأ في إضافة المخزن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task EditWarehouseAsync(Warehouse? warehouse)
        {
            if (warehouse == null) return;

            try
            {
                var dialog = new AddWarehouseDialog(warehouse);
                if (dialog.ShowDialog() == true)
                {
                    var updatedWarehouse = dialog.Warehouse;
                    var success = await _warehouseService.UpdateWarehouseAsync(updatedWarehouse);
                    
                    if (success)
                    {
                        // Update the warehouse in the collection
                        var index = Warehouses.IndexOf(warehouse);
                        if (index >= 0)
                        {
                            Warehouses[index] = updatedWarehouse;
                        }
                        
                        MessageBox.Show("تم تحديث المخزن بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في تعديل المخزن: {warehouse.Id}");
                MessageBox.Show($"خطأ في تعديل المخزن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteWarehouseAsync(Warehouse? warehouse)
        {
            if (warehouse == null) return;

            try
            {
                var result = MessageBox.Show($"هل تريد حذف المخزن '{warehouse.Name}'؟\n\nتحذير: سيتم حذف جميع المواقع المرتبطة بهذا المخزن.", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    var success = await _warehouseService.DeleteWarehouseAsync(warehouse.Id);
                    
                    if (success)
                    {
                        Warehouses.Remove(warehouse);
                        
                        MessageBox.Show("تم حذف المخزن بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في حذف المخزن: {warehouse.Id}");
                MessageBox.Show($"خطأ في حذف المخزن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ViewWarehouseDetailsAsync(Warehouse? warehouse)
        {
            if (warehouse == null) return;

            try
            {
                // تحديث بيانات المخزن
                var updatedWarehouse = await _warehouseService.GetWarehouseByIdAsync(warehouse.Id);
                if (updatedWarehouse != null)
                {
                    var detailsWindow = new WarehouseDetailsWindow(updatedWarehouse);
                    detailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError(ex, $"خطأ في عرض تفاصيل المخزن: {warehouse.Id}");
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    // Placeholder classes for dialogs - these would be implemented separately
    public class AddWarehouseDialog : Window
    {
        public Warehouse Warehouse { get; set; } = new Warehouse();
        
        public AddWarehouseDialog() 
        {
            Title = "إضافة مخزن جديد";
            Width = 600;
            Height = 500;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
        }
        
        public AddWarehouseDialog(Warehouse warehouse) : this()
        {
            Title = "تعديل المخزن";
            Warehouse = warehouse;
        }
    }

    public class WarehouseDetailsWindow : Window
    {
        public WarehouseDetailsWindow(Warehouse warehouse) 
        {
            Title = $"تفاصيل المخزن - {warehouse.Name}";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
        }
    }
}
