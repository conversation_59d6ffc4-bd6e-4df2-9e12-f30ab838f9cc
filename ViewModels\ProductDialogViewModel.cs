using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Prism.Commands;
using SalesManagementSystem.Models;
using SalesManagementSystem.Services;

namespace SalesManagementSystem.ViewModels
{
    public class ProductDialogViewModel : BaseViewModel
    {
        #region Services

        private readonly DatabaseService _dbService;
        private readonly ProductService _productService;
        private readonly CategoryService _categoryService;

        #endregion

        #region Properties

        private Product _product = new();
        public Product Product
        {
            get => _product;
            set
            {
                if (SetProperty(ref _product, value))
                {
                    OnProductChanged();
                }
            }
        }

        private ObservableCollection<Category> _categories = new();
        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set => SetProperty(ref _categories, value);
        }

        private Category? _selectedCategory;
        public Category? SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                if (SetProperty(ref _selectedCategory, value))
                {
                    if (value != null)
                        Product.CategoryId = value.Id;
                }
            }
        }

        private ObservableCollection<string> _units = new();
        public ObservableCollection<string> Units
        {
            get => _units;
            set => SetProperty(ref _units, value);
        }

        private string _windowTitle = "إضافة منتج جديد";
        public string WindowTitle
        {
            get => _windowTitle;
            set => SetProperty(ref _windowTitle, value);
        }

        private string _headerIcon = "Package";
        public string HeaderIcon
        {
            get => _headerIcon;
            set => SetProperty(ref _headerIcon, value);
        }

        private bool _isCodeEditable = true;
        public bool IsCodeEditable
        {
            get => _isCodeEditable;
            set => SetProperty(ref _isCodeEditable, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (SetProperty(ref _isEditMode, value))
                {
                    UpdateWindowTitle();
                    IsCodeEditable = !value;
                }
            }
        }

        public decimal ProfitMargin
        {
            get
            {
                if (Product.PurchasePrice > 0)
                {
                    return ((Product.SalePrice - Product.PurchasePrice) / Product.PurchasePrice) * 100;
                }
                return 0;
            }
        }

        public bool IsLowStock => Product.Quantity <= Product.MinQuantity;

        #endregion

        #region Commands

        private DelegateCommand? _saveCommand;
        public DelegateCommand SaveCommand => _saveCommand ??= new DelegateCommand(SaveProduct, CanSaveProduct);

        private DelegateCommand? _cancelCommand;
        public DelegateCommand CancelCommand => _cancelCommand ??= new DelegateCommand(Cancel);

        #endregion

        #region Constructor

        public ProductDialogViewModel()
        {
            _dbService = new DatabaseService();
            _productService = new ProductService(_dbService);
            _categoryService = new CategoryService(_dbService);

            InitializeUnits();
            _ = LoadDataAsync();
        }

        public ProductDialogViewModel(Product product) : this()
        {
            Product = product.Clone();
            IsEditMode = true;
        }

        #endregion

        #region Methods

        private void InitializeUnits()
        {
            Units.Clear();
            Units.Add("قطعة");
            Units.Add("كيلو");
            Units.Add("جرام");
            Units.Add("لتر");
            Units.Add("متر");
            Units.Add("علبة");
            Units.Add("كرتون");
            Units.Add("دزينة");
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل البيانات...";
                ClearError();

                await LoadCategoriesAsync();

                if (IsEditMode)
                {
                    await LoadProductCategoryAsync();
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في تحميل البيانات: {ex.Message}");
                LoggingService.LogError(ex, "خطأ في تحميل بيانات حوار المنتج");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadCategoriesAsync()
        {
            var categories = await _categoryService.GetAllCategoriesAsync();
            Categories.Clear();

            foreach (var category in categories)
            {
                Categories.Add(category);
            }
        }

        private async Task LoadProductCategoryAsync()
        {
            if (Product.CategoryId.HasValue && Product.CategoryId.Value > 0)
            {
                var category = await _categoryService.GetCategoryByIdAsync(Product.CategoryId.Value);
                if (category != null)
                {
                    SelectedCategory = Categories.FirstOrDefault(c => c.Id == category.Id);
                }
            }
        }

        private void OnProductChanged()
        {
            OnPropertyChanged(nameof(ProfitMargin));
            OnPropertyChanged(nameof(IsLowStock));
            SaveCommand.RaiseCanExecuteChanged();
        }

        private void UpdateWindowTitle()
        {
            WindowTitle = IsEditMode ? "تعديل المنتج" : "إضافة منتج جديد";
            HeaderIcon = IsEditMode ? "PackageVariant" : "Package";
        }

        #endregion

        #region Command Handlers

        private async void SaveProduct()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = IsEditMode ? "جاري تحديث المنتج..." : "جاري حفظ المنتج...";
                ClearError();

                // Validate input
                if (!ValidateProduct())
                    return;

                // Generate code if new product
                if (!IsEditMode && string.IsNullOrWhiteSpace(Product.Code))
                {
                    Product.Code = await GenerateProductCodeAsync();
                }

                // Save product
                if (IsEditMode)
                {
                    var success = await _productService.UpdateProductAsync(Product);
                    if (success)
                    {
                        LoggingService.LogProductUpdate(Product.Name, "تحديث", "تم تحديث المنتج بنجاح");
                        ProductSaved?.Invoke(Product);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في تحديث المنتج");
                    }
                }
                else
                {
                    var result = await _productService.AddProductAsync(Product);
                    if (result != null)
                    {
                        Product = result; // Update with the saved product (including ID)
                        LoggingService.LogProductUpdate(Product.Name, "إضافة", "تم إضافة المنتج بنجاح");
                        ProductSaved?.Invoke(Product);
                        RequestClose?.Invoke(true);
                    }
                    else
                    {
                        SetError("فشل في حفظ المنتج");
                    }
                }
            }
            catch (Exception ex)
            {
                SetError($"خطأ في حفظ المنتج: {ex.Message}");
                LoggingService.LogError(ex, $"خطأ في حفظ المنتج: {Product.Name}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private bool CanSaveProduct()
        {
            return !string.IsNullOrWhiteSpace(Product.Name) &&
                   Product.SalePrice > 0 &&
                   Product.PurchasePrice > 0 &&
                   SelectedCategory != null &&
                   !IsLoading;
        }

        private bool ValidateProduct()
        {
            ClearAllValidationErrors();

            if (string.IsNullOrWhiteSpace(Product.Name))
            {
                AddValidationError(nameof(Product.Name), "اسم المنتج مطلوب");
            }

            if (Product.SalePrice <= 0)
            {
                AddValidationError(nameof(Product.SalePrice), "سعر البيع يجب أن يكون أكبر من صفر");
            }

            if (Product.PurchasePrice <= 0)
            {
                AddValidationError(nameof(Product.PurchasePrice), "سعر الشراء يجب أن يكون أكبر من صفر");
            }

            if (Product.SalePrice <= Product.PurchasePrice)
            {
                AddValidationError(nameof(Product.SalePrice), "سعر البيع يجب أن يكون أكبر من سعر الشراء");
            }

            if (SelectedCategory == null)
            {
                AddValidationError(nameof(SelectedCategory), "يجب اختيار فئة للمنتج");
            }

            if (Product.Quantity < 0)
            {
                AddValidationError(nameof(Product.Quantity), "الكمية لا يمكن أن تكون سالبة");
            }

            if (Product.MinQuantity < 0)
            {
                AddValidationError(nameof(Product.MinQuantity), "الحد الأدنى للكمية لا يمكن أن يكون سالب");
            }

            if (HasValidationErrors)
            {
                var firstError = GetValidationErrors(nameof(Product.Name)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.SalePrice)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.PurchasePrice)).FirstOrDefault() ??
                               GetValidationErrors(nameof(SelectedCategory)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.Quantity)).FirstOrDefault() ??
                               GetValidationErrors(nameof(Product.MinQuantity)).FirstOrDefault();

                SetError(firstError ?? "يرجى تصحيح الأخطاء المدخلة");
                return false;
            }

            return true;
        }

        private Task<string> GenerateProductCodeAsync()
        {
            try
            {
                // Generate code based on category and sequence
                var categoryPrefix = SelectedCategory?.Name.Substring(0, Math.Min(3, SelectedCategory.Name.Length)).ToUpper() ?? "PRD";
                var timestamp = DateTime.Now.ToString("yyyyMMdd");
                var random = new Random().Next(100, 999);

                return Task.FromResult($"{categoryPrefix}-{timestamp}-{random}");
            }
            catch
            {
                return Task.FromResult($"PRD-{DateTime.Now:yyyyMMddHHmmss}");
            }
        }

        private void Cancel()
        {
            RequestClose?.Invoke(false);
        }

        #endregion

        #region Events

        public event Action<Product>? ProductSaved;
        public event Action<bool>? RequestClose;

        #endregion
    }
}
