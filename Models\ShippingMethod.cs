using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;

namespace SalesManagementSystem.Models
{
    /// <summary>
    /// نموذج طريقة الشحن
    /// </summary>
    public class ShippingMethod : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _code = string.Empty;
        private string _description = string.Empty;
        private ShippingType _shippingType = ShippingType.Standard;
        private decimal _baseCost;
        private decimal _costPerKg;
        private decimal _costPerKm;
        private decimal _freeShippingThreshold;
        private int _estimatedDaysMin = 1;
        private int _estimatedDaysMax = 3;
        private decimal _maxWeight;
        private decimal _maxDimensions;
        private bool _isActive = true;
        private bool _requiresSignature;
        private bool _allowCashOnDelivery;
        private bool _allowInsurance;
        private decimal _insuranceRate;
        private string _trackingUrl = string.Empty;
        private string _carrierName = string.Empty;
        private string _carrierCode = string.Empty;
        private string _apiEndpoint = string.Empty;
        private string _apiKey = string.Empty;
        private string _supportedCountries = string.Empty;
        private string _supportedCities = string.Empty;
        private int _sortOrder;
        private DateTime _createdAt = DateTime.Now;
        private DateTime? _updatedAt;
        private string _createdBy = string.Empty;
        private ObservableCollection<ShippingZone> _shippingZones = new();

        #region Properties

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public ShippingType ShippingType
        {
            get => _shippingType;
            set
            {
                if (_shippingType != value)
                {
                    _shippingType = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(ShippingTypeDisplay));
                }
            }
        }

        public decimal BaseCost
        {
            get => _baseCost;
            set
            {
                if (_baseCost != value)
                {
                    _baseCost = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedBaseCost));
                }
            }
        }

        public decimal CostPerKg
        {
            get => _costPerKg;
            set
            {
                if (_costPerKg != value)
                {
                    _costPerKg = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCostPerKg));
                }
            }
        }

        public decimal CostPerKm
        {
            get => _costPerKm;
            set
            {
                if (_costPerKm != value)
                {
                    _costPerKm = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCostPerKm));
                }
            }
        }

        public decimal FreeShippingThreshold
        {
            get => _freeShippingThreshold;
            set
            {
                if (_freeShippingThreshold != value)
                {
                    _freeShippingThreshold = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedFreeShippingThreshold));
                    OnPropertyChanged(nameof(HasFreeShipping));
                }
            }
        }

        public int EstimatedDaysMin
        {
            get => _estimatedDaysMin;
            set
            {
                if (_estimatedDaysMin != value)
                {
                    _estimatedDaysMin = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(EstimatedDeliveryTime));
                }
            }
        }

        public int EstimatedDaysMax
        {
            get => _estimatedDaysMax;
            set
            {
                if (_estimatedDaysMax != value)
                {
                    _estimatedDaysMax = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(EstimatedDeliveryTime));
                }
            }
        }

        public decimal MaxWeight
        {
            get => _maxWeight;
            set
            {
                if (_maxWeight != value)
                {
                    _maxWeight = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMaxWeight));
                }
            }
        }

        public decimal MaxDimensions
        {
            get => _maxDimensions;
            set
            {
                if (_maxDimensions != value)
                {
                    _maxDimensions = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedMaxDimensions));
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool RequiresSignature
        {
            get => _requiresSignature;
            set
            {
                if (_requiresSignature != value)
                {
                    _requiresSignature = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool AllowCashOnDelivery
        {
            get => _allowCashOnDelivery;
            set
            {
                if (_allowCashOnDelivery != value)
                {
                    _allowCashOnDelivery = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool AllowInsurance
        {
            get => _allowInsurance;
            set
            {
                if (_allowInsurance != value)
                {
                    _allowInsurance = value;
                    OnPropertyChanged();
                }
            }
        }

        public decimal InsuranceRate
        {
            get => _insuranceRate;
            set
            {
                if (_insuranceRate != value)
                {
                    _insuranceRate = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedInsuranceRate));
                }
            }
        }

        public string TrackingUrl
        {
            get => _trackingUrl;
            set
            {
                if (_trackingUrl != value)
                {
                    _trackingUrl = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(HasTracking));
                }
            }
        }

        public string CarrierName
        {
            get => _carrierName;
            set
            {
                if (_carrierName != value)
                {
                    _carrierName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CarrierCode
        {
            get => _carrierCode;
            set
            {
                if (_carrierCode != value)
                {
                    _carrierCode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApiEndpoint
        {
            get => _apiEndpoint;
            set
            {
                if (_apiEndpoint != value)
                {
                    _apiEndpoint = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ApiKey
        {
            get => _apiKey;
            set
            {
                if (_apiKey != value)
                {
                    _apiKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SupportedCountries
        {
            get => _supportedCountries;
            set
            {
                if (_supportedCountries != value)
                {
                    _supportedCountries = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SupportedCities
        {
            get => _supportedCities;
            set
            {
                if (_supportedCities != value)
                {
                    _supportedCities = value;
                    OnPropertyChanged();
                }
            }
        }

        public int SortOrder
        {
            get => _sortOrder;
            set
            {
                if (_sortOrder != value)
                {
                    _sortOrder = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(FormattedCreatedAt));
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CreatedBy
        {
            get => _createdBy;
            set
            {
                if (_createdBy != value)
                {
                    _createdBy = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<ShippingZone> ShippingZones
        {
            get => _shippingZones;
            set
            {
                if (_shippingZones != value)
                {
                    _shippingZones = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

        #region Calculated Properties

        public string ShippingTypeDisplay
        {
            get
            {
                return ShippingType switch
                {
                    ShippingType.Standard => "شحن عادي",
                    ShippingType.Express => "شحن سريع",
                    ShippingType.Overnight => "شحن ليلي",
                    ShippingType.SameDay => "نفس اليوم",
                    ShippingType.Pickup => "استلام من المتجر",
                    _ => "غير محدد"
                };
            }
        }

        public string EstimatedDeliveryTime
        {
            get
            {
                if (EstimatedDaysMin == EstimatedDaysMax)
                    return $"{EstimatedDaysMin} يوم";
                return $"{EstimatedDaysMin}-{EstimatedDaysMax} أيام";
            }
        }

        public bool HasFreeShipping => FreeShippingThreshold > 0;
        public bool HasTracking => !string.IsNullOrEmpty(TrackingUrl);

        // Formatted Properties
        public string FormattedBaseCost => $"{BaseCost:C}";
        public string FormattedCostPerKg => $"{CostPerKg:C}/كجم";
        public string FormattedCostPerKm => $"{CostPerKm:C}/كم";
        public string FormattedFreeShippingThreshold => HasFreeShipping ? $"شحن مجاني للطلبات أكثر من {FreeShippingThreshold:C}" : "لا يوجد شحن مجاني";
        public string FormattedMaxWeight => MaxWeight > 0 ? $"{MaxWeight} كجم" : "غير محدود";
        public string FormattedMaxDimensions => MaxDimensions > 0 ? $"{MaxDimensions} سم" : "غير محدود";
        public string FormattedInsuranceRate => $"{InsuranceRate:P}";
        public string FormattedCreatedAt => CreatedAt.ToString("dd/MM/yyyy");

        #endregion

        #region Methods

        /// <summary>
        /// حساب تكلفة الشحن
        /// </summary>
        public decimal CalculateShippingCost(decimal orderTotal, decimal weight, decimal distance = 0)
        {
            // إذا كان الطلب يستحق الشحن المجاني
            if (HasFreeShipping && orderTotal >= FreeShippingThreshold)
                return 0;

            decimal cost = BaseCost;

            // إضافة تكلفة الوزن
            if (CostPerKg > 0 && weight > 0)
                cost += weight * CostPerKg;

            // إضافة تكلفة المسافة
            if (CostPerKm > 0 && distance > 0)
                cost += distance * CostPerKm;

            return Math.Max(0, cost);
        }

        /// <summary>
        /// حساب تكلفة التأمين
        /// </summary>
        public decimal CalculateInsuranceCost(decimal orderTotal)
        {
            if (!AllowInsurance || InsuranceRate <= 0)
                return 0;

            return orderTotal * InsuranceRate;
        }

        /// <summary>
        /// التحقق من إمكانية الشحن للعنوان
        /// </summary>
        public bool CanShipTo(Address address)
        {
            if (address == null || !IsActive)
                return false;

            // التحقق من البلدان المدعومة
            if (!string.IsNullOrEmpty(SupportedCountries))
            {
                var countries = SupportedCountries.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (!countries.Any(c => c.Trim().Equals(address.Country, StringComparison.OrdinalIgnoreCase)))
                    return false;
            }

            // التحقق من المدن المدعومة
            if (!string.IsNullOrEmpty(SupportedCities))
            {
                var cities = SupportedCities.Split(',', StringSplitOptions.RemoveEmptyEntries);
                if (!cities.Any(c => c.Trim().Equals(address.City, StringComparison.OrdinalIgnoreCase)))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// تقدير تاريخ التسليم
        /// </summary>
        public DateTime EstimateDeliveryDate(DateTime orderDate)
        {
            // استخدام الحد الأقصى للأيام المقدرة
            return orderDate.AddDays(EstimatedDaysMax);
        }

        /// <summary>
        /// إنشاء رابط التتبع
        /// </summary>
        public string GenerateTrackingUrl(string trackingNumber)
        {
            if (string.IsNullOrEmpty(TrackingUrl) || string.IsNullOrEmpty(trackingNumber))
                return string.Empty;

            return TrackingUrl.Replace("{tracking_number}", trackingNumber);
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// منطقة الشحن
    /// </summary>
    public class ShippingZone
    {
        public int Id { get; set; }
        public int ShippingMethodId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Countries { get; set; } = string.Empty;
        public string Cities { get; set; } = string.Empty;
        public decimal AdditionalCost { get; set; }
        public int AdditionalDays { get; set; }
        public bool IsActive { get; set; } = true;
    }

    #endregion

    #region Enums

    public enum ShippingType
    {
        Standard,       // شحن عادي
        Express,        // شحن سريع
        Overnight,      // شحن ليلي
        SameDay,        // نفس اليوم
        Pickup          // استلام من المتجر
    }

    #endregion

    #region Validation

    public class ShippingMethodValidator : AbstractValidator<ShippingMethod>
    {
        public ShippingMethodValidator()
        {
            RuleFor(s => s.Name)
                .NotEmpty().WithMessage("اسم طريقة الشحن مطلوب")
                .MaximumLength(100).WithMessage("اسم طريقة الشحن لا يمكن أن يتجاوز 100 حرف");

            RuleFor(s => s.Code)
                .NotEmpty().WithMessage("كود طريقة الشحن مطلوب")
                .MaximumLength(50).WithMessage("كود طريقة الشحن لا يمكن أن يتجاوز 50 حرف");

            RuleFor(s => s.BaseCost)
                .GreaterThanOrEqualTo(0).WithMessage("التكلفة الأساسية يجب أن تكون أكبر من أو تساوي صفر");

            RuleFor(s => s.CostPerKg)
                .GreaterThanOrEqualTo(0).WithMessage("تكلفة الكيلوجرام يجب أن تكون أكبر من أو تساوي صفر");

            RuleFor(s => s.EstimatedDaysMin)
                .GreaterThan(0).WithMessage("الحد الأدنى للأيام يجب أن يكون أكبر من صفر");

            RuleFor(s => s.EstimatedDaysMax)
                .GreaterThanOrEqualTo(s => s.EstimatedDaysMin)
                .WithMessage("الحد الأقصى للأيام يجب أن يكون أكبر من أو يساوي الحد الأدنى");

            RuleFor(s => s.MaxWeight)
                .GreaterThan(0).When(s => s.MaxWeight > 0)
                .WithMessage("الحد الأقصى للوزن يجب أن يكون أكبر من صفر");

            RuleFor(s => s.InsuranceRate)
                .InclusiveBetween(0, 1).WithMessage("معدل التأمين يجب أن يكون بين 0 و 1");
        }
    }

    #endregion
}
