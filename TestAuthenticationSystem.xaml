<Window x:Class="SalesManagementSystem.TestAuthenticationSystem"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختبار نظام المصادقة والأمان" 
        Height="700" 
        Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="Security" 
                                   Width="32" Height="32" 
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="اختبار نظام المصادقة والأمان" 
                      Style="{DynamicResource MaterialDesignHeadline5TextBlock}"
                      VerticalAlignment="Center" 
                      Margin="12,0,0,0"/>
        </StackPanel>

        <!-- Current User Info -->
        <GroupBox Grid.Row="1" Header="المستخدم الحالي" 
                 Style="{DynamicResource MaterialDesignCardGroupBox}"
                 Margin="0,0,0,16">
            <Grid Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="اسم المستخدم" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                    <TextBlock x:Name="CurrentUserTextBlock"
                              Text="غير مسجل دخول" 
                              Style="{DynamicResource MaterialDesignBody1TextBlock}"
                              FontWeight="Medium"/>
                </StackPanel>

                <StackPanel Grid.Column="1">
                    <TextBlock Text="الدور" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                    <TextBlock x:Name="CurrentRoleTextBlock"
                              Text="-" 
                              Style="{DynamicResource MaterialDesignBody1TextBlock}"
                              FontWeight="Medium"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <TextBlock Text="آخر تسجيل دخول" 
                              Style="{DynamicResource MaterialDesignCaptionTextBlock}"
                              Opacity="0.7"/>
                    <TextBlock x:Name="LastLoginTextBlock"
                              Text="-" 
                              Style="{DynamicResource MaterialDesignBody1TextBlock}"
                              FontWeight="Medium"/>
                </StackPanel>

                <Button Grid.Column="3"
                       x:Name="LogoutButton"
                       Click="LogoutButton_Click"
                       Style="{DynamicResource MaterialDesignOutlinedButton}"
                       Width="100"
                       IsEnabled="False">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Logout" 
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="تسجيل خروج" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </Grid>
        </GroupBox>

        <!-- Test Actions -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Authentication Tests -->
                <GroupBox Header="اختبارات المصادقة" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="LoginButton"
                                   Click="LoginButton_Click"
                                   Style="{DynamicResource MaterialDesignRaisedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Login" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تسجيل دخول" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="ChangePasswordButton"
                                   Click="ChangePasswordButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="4,0"
                                   IsEnabled="False">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="LockReset" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="تغيير كلمة المرور" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                   x:Name="CreateUserButton"
                                   Click="CreateUserButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountPlus" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="إنشاء مستخدم" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Security Tests -->
                <GroupBox Header="اختبارات الأمان" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}"
                         Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                   x:Name="TestPasswordStrengthButton"
                                   Click="TestPasswordStrengthButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="0,0,8,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ShieldCheck" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="اختبار قوة كلمة المرور" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="1"
                                   x:Name="TestEncryptionButton"
                                   Click="TestEncryptionButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="4,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Key" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="اختبار التشفير" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button Grid.Column="2"
                                   x:Name="GeneratePasswordButton"
                                   Click="GeneratePasswordButton_Click"
                                   Style="{DynamicResource MaterialDesignOutlinedButton}"
                                   Margin="8,0,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AutoFix" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="توليد كلمة مرور" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </GroupBox>

                <!-- Test Results -->
                <GroupBox Header="نتائج الاختبارات" 
                         Style="{DynamicResource MaterialDesignCardGroupBox}">
                    <ScrollViewer MaxHeight="300" VerticalScrollBarVisibility="Auto">
                        <TextBox x:Name="ResultsTextBox"
                                Style="{DynamicResource MaterialDesignOutlinedTextBox}"
                                AcceptsReturn="True"
                                IsReadOnly="True"
                                TextWrapping="Wrap"
                                VerticalScrollBarVisibility="Auto"
                                Text="جاهز لبدء الاختبارات..."
                                Margin="16"/>
                    </ScrollViewer>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
