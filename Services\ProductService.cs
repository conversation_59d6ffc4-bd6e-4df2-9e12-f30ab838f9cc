using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SalesManagementSystem.Models;

namespace SalesManagementSystem.Services
{
    public class ProductService
    {
        private readonly DatabaseService _dbService;

        public ProductService(DatabaseService dbService)
        {
            _dbService = dbService;
        }

        public async Task<IEnumerable<Product>> GetAllProductsAsync()
        {
            const string sql = @"
                SELECT p.*, c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                ORDER BY p.Name";

            return await _dbService.QueryAsync<Product>(sql);
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            const string sql = @"
                SELECT p.*, c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Quantity <= p.MinQuantity
                ORDER BY p.Name";

            return await _dbService.QueryAsync<Product>(sql);
        }

        public async Task<IEnumerable<TopSellingProduct>> GetTopSellingProductsAsync(int count, DateTime startDate, DateTime endDate)
        {
            const string sql = @"
                SELECT p.Id, p.Name, p.Code, SUM(si.Quantity) as TotalSold
                FROM Products p
                INNER JOIN SaleItems si ON p.Id = si.ProductId
                INNER JOIN Sales s ON si.SaleId = s.Id
                WHERE s.Date BETWEEN @StartDate AND @EndDate
                GROUP BY p.Id, p.Name, p.Code
                ORDER BY TotalSold DESC
                LIMIT @Count";

            return await _dbService.QueryAsync<TopSellingProduct>(sql, new
            {
                StartDate = startDate.ToString("yyyy-MM-dd"),
                EndDate = endDate.ToString("yyyy-MM-dd"),
                Count = count
            });
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
        {
            const string sql = @"
                SELECT p.*, c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Name LIKE @SearchTerm OR p.Code LIKE @SearchTerm OR p.Description LIKE @SearchTerm
                ORDER BY p.Name";

            return await _dbService.QueryAsync<Product>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Product> GetProductByIdAsync(int id)
        {
            const string sql = @"
                SELECT p.*, c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Id = @Id";

            return await _dbService.QuerySingleOrDefaultAsync<Product>(sql, new { Id = id });
        }

        public async Task<Product> GetProductByCodeAsync(string code)
        {
            const string sql = @"
                SELECT p.*, c.Name as CategoryName
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                WHERE p.Code = @Code";

            return await _dbService.QuerySingleOrDefaultAsync<Product>(sql, new { Code = code });
        }

        public async Task<Product> AddProductAsync(Product product)
        {
            product.CreatedAt = DateTime.Now;
            return await _dbService.InsertAsync<Product>("Products", product);
        }

        public async Task<bool> UpdateProductAsync(Product product)
        {
            product.UpdatedAt = DateTime.Now;
            return await _dbService.UpdateAsync("Products", product);
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            return await _dbService.DeleteAsync("Products", id);
        }

        public async Task<bool> UpdateProductStockAsync(int productId, int quantity)
        {
            const string sql = @"
                UPDATE Products
                SET Quantity = Quantity + @Quantity, UpdatedAt = @UpdatedAt
                WHERE Id = @ProductId";

            var result = await _dbService.ExecuteAsync(sql, new
            {
                ProductId = productId,
                Quantity = quantity,
                UpdatedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
            });

            return result > 0;
        }

        public async Task<IEnumerable<Product>> GetTopSellingProductsAsync(int count = 10, DateTime? startDate = null, DateTime? endDate = null)
        {
            string dateFilter = "";
            if (startDate.HasValue && endDate.HasValue)
            {
                dateFilter = "AND s.Date BETWEEN @StartDate AND @EndDate";
            }

            string sql = $@"
                SELECT p.*, c.Name as CategoryName, SUM(si.Quantity) as TotalSold
                FROM Products p
                LEFT JOIN Categories c ON p.CategoryId = c.Id
                INNER JOIN SaleItems si ON p.Id = si.ProductId
                INNER JOIN Sales s ON si.SaleId = s.Id
                WHERE 1=1 {dateFilter}
                GROUP BY p.Id
                ORDER BY TotalSold DESC
                LIMIT @Count";

            return await _dbService.QueryAsync<Product>(sql, new
            {
                Count = count,
                StartDate = startDate?.ToString("yyyy-MM-dd"),
                EndDate = endDate?.ToString("yyyy-MM-dd")
            });
        }

        public async Task<IEnumerable<Category>> GetAllCategoriesAsync()
        {
            const string sql = "SELECT * FROM Categories ORDER BY Name";
            return await _dbService.QueryAsync<Category>(sql);
        }

        public async Task<Category> GetCategoryByIdAsync(int id)
        {
            const string sql = "SELECT * FROM Categories WHERE Id = @Id";
            return await _dbService.QuerySingleOrDefaultAsync<Category>(sql, new { Id = id });
        }

        public async Task<Category> AddCategoryAsync(Category category)
        {
            return await _dbService.InsertAsync<Category>("Categories", category);
        }

        public async Task<bool> UpdateCategoryAsync(Category category)
        {
            return await _dbService.UpdateAsync("Categories", category);
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            return await _dbService.DeleteAsync("Categories", id);
        }

        // Additional methods for ReportService
        public async Task<int> GetProductCountAsync()
        {
            const string sql = "SELECT COUNT(*) FROM Products";
            return await _dbService.QuerySingleAsync<int>(sql);
        }

        public async Task<decimal> GetTotalStockValueAsync()
        {
            const string sql = "SELECT SUM(Quantity * PurchasePrice) FROM Products";
            var result = await _dbService.QuerySingleOrDefaultAsync<decimal?>(sql);
            return result ?? 0;
        }

        public async Task<IEnumerable<CategoryProductCount>> GetProductCountByCategoryAsync()
        {
            const string sql = @"
                SELECT c.Name as CategoryName, COUNT(p.Id) as ProductCount
                FROM Categories c
                LEFT JOIN Products p ON c.Id = p.CategoryId
                GROUP BY c.Id, c.Name
                ORDER BY ProductCount DESC";

            return await _dbService.QueryAsync<CategoryProductCount>(sql);
        }
    }



    public class CategoryProductCount
    {
        public string CategoryName { get; set; } = string.Empty;
        public int ProductCount { get; set; }
    }

    public class TopSellingProduct
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int? TotalSold { get; set; }
    }
}