using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using FluentValidation;
using System.Linq;

namespace SalesManagementSystem.Models
{
    public class User : INotifyPropertyChanged
    {
        private int _id;
        private string _username = string.Empty;
        private string _email = string.Empty;
        private string _fullName = string.Empty;
        private string _phone = string.Empty;
        private int _roleId;
        private string _roleName = string.Empty;
        private bool _isActive = true;
        private bool _isLocked;
        private int _failedLoginAttempts;
        private DateTime? _lastLoginDate;
        private DateTime? _lastPasswordChangeDate;
        private DateTime _createdAt;
        private DateTime? _updatedAt;
        private string _passwordHash = string.Empty;
        private string _salt = string.Empty;
        private ObservableCollection<Permission> _permissions = new();

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Username
        {
            get => _username;
            set
            {
                if (_username != value)
                {
                    _username = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Email
        {
            get => _email;
            set
            {
                if (_email != value)
                {
                    _email = value;
                    OnPropertyChanged();
                }
            }
        }

        public string FullName
        {
            get => _fullName;
            set
            {
                if (_fullName != value)
                {
                    _fullName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Phone
        {
            get => _phone;
            set
            {
                if (_phone != value)
                {
                    _phone = value;
                    OnPropertyChanged();
                }
            }
        }

        public int RoleId
        {
            get => _roleId;
            set
            {
                if (_roleId != value)
                {
                    _roleId = value;
                    OnPropertyChanged();
                }
            }
        }

        public string RoleName
        {
            get => _roleName;
            set
            {
                if (_roleName != value)
                {
                    _roleName = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsLocked
        {
            get => _isLocked;
            set
            {
                if (_isLocked != value)
                {
                    _isLocked = value;
                    OnPropertyChanged();
                }
            }
        }

        public int FailedLoginAttempts
        {
            get => _failedLoginAttempts;
            set
            {
                if (_failedLoginAttempts != value)
                {
                    _failedLoginAttempts = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? LastLoginDate
        {
            get => _lastLoginDate;
            set
            {
                if (_lastLoginDate != value)
                {
                    _lastLoginDate = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? LastPasswordChangeDate
        {
            get => _lastPasswordChangeDate;
            set
            {
                if (_lastPasswordChangeDate != value)
                {
                    _lastPasswordChangeDate = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string PasswordHash
        {
            get => _passwordHash;
            set
            {
                if (_passwordHash != value)
                {
                    _passwordHash = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Salt
        {
            get => _salt;
            set
            {
                if (_salt != value)
                {
                    _salt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<Permission> Permissions
        {
            get => _permissions ?? (_permissions = new ObservableCollection<Permission>());
            set
            {
                if (_permissions != value)
                {
                    _permissions = value;
                    OnPropertyChanged();
                }
            }
        }

        // Calculated properties
        public bool IsPasswordExpired => LastPasswordChangeDate.HasValue &&
            DateTime.Now.Subtract(LastPasswordChangeDate.Value).TotalDays > 90;

        public bool RequiresPasswordChange => !LastPasswordChangeDate.HasValue || IsPasswordExpired;

        public string StatusText => IsLocked ? "مقفل" : IsActive ? "نشط" : "غير نشط";

        public string LastLoginText => LastLoginDate?.ToString("yyyy/MM/dd HH:mm") ?? "لم يسجل دخول من قبل";

        public bool IsAdmin => RoleName?.Equals("Admin", StringComparison.OrdinalIgnoreCase) ?? false;

        // Methods
        public bool HasPermission(string permissionName)
        {
            return Permissions.Any(p => p.Name == permissionName && p.IsGranted);
        }

        public bool HasAnyPermission(params string[] permissionNames)
        {
            return permissionNames.Any(HasPermission);
        }

        public bool HasAllPermissions(params string[] permissionNames)
        {
            return permissionNames.All(HasPermission);
        }

        // Clone method
        public User Clone()
        {
            var clonedUser = new User
            {
                Id = this.Id,
                Username = this.Username,
                Email = this.Email,
                PasswordHash = this.PasswordHash,
                Salt = this.Salt,
                FullName = this.FullName,
                Phone = this.Phone,
                RoleId = this.RoleId,
                RoleName = this.RoleName,
                IsActive = this.IsActive,
                IsLocked = this.IsLocked,
                FailedLoginAttempts = this.FailedLoginAttempts,
                LastLoginDate = this.LastLoginDate,
                LastPasswordChangeDate = this.LastPasswordChangeDate,
                CreatedAt = this.CreatedAt,
                UpdatedAt = this.UpdatedAt
            };

            // Clone permissions
            clonedUser.Permissions.Clear();
            foreach (var permission in this.Permissions)
            {
                clonedUser.Permissions.Add(permission.Clone());
            }

            return clonedUser;
        }

        // INotifyPropertyChanged implementation
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class UserValidator : AbstractValidator<User>
    {
        public UserValidator()
        {
            RuleFor(u => u.Username).NotEmpty().WithMessage("Username is required")
                .MinimumLength(3).WithMessage("Username must be at least 3 characters")
                .MaximumLength(50).WithMessage("Username cannot exceed 50 characters")
                .Matches(@"^[a-zA-Z0-9_]+$").WithMessage("Username can only contain letters, numbers, and underscores");

            RuleFor(u => u.Email).NotEmpty().WithMessage("Email is required")
                .EmailAddress().WithMessage("Invalid email format")
                .MaximumLength(100).WithMessage("Email cannot exceed 100 characters");

            RuleFor(u => u.FullName).NotEmpty().WithMessage("Full name is required")
                .MaximumLength(100).WithMessage("Full name cannot exceed 100 characters");

            RuleFor(u => u.RoleName).NotEmpty().WithMessage("Role is required")
                .Must(r => new[] { "Admin", "Manager", "Cashier", "Inventory", "Accountant" }.Contains(r))
                .WithMessage("Invalid role");
        }
    }

    public class PasswordValidator : AbstractValidator<string>
    {
        public PasswordValidator()
        {
            RuleFor(password => password).NotEmpty().WithMessage("Password is required")
                .MinimumLength(8).WithMessage("Password must be at least 8 characters")
                .Matches(@"[A-Z]+").WithMessage("Password must contain at least one uppercase letter")
                .Matches(@"[a-z]+").WithMessage("Password must contain at least one lowercase letter")
                .Matches(@"[0-9]+").WithMessage("Password must contain at least one number")
                .Matches(@"[\W_]+").WithMessage("Password must contain at least one special character");
        }
    }

    public class LoginModel
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool RememberMe { get; set; }
    }

    public class ChangePasswordModel
    {
        public string CurrentPassword { get; set; } = string.Empty;
        public string NewPassword { get; set; } = string.Empty;
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public class Role : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _description = string.Empty;
        private bool _isActive = true;
        private DateTime _createdAt;
        private DateTime? _updatedAt;
        private ObservableCollection<Permission> _permissions = new();

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedAt
        {
            get => _createdAt;
            set
            {
                if (_createdAt != value)
                {
                    _createdAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? UpdatedAt
        {
            get => _updatedAt;
            set
            {
                if (_updatedAt != value)
                {
                    _updatedAt = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<Permission> Permissions
        {
            get => _permissions ?? (_permissions = new ObservableCollection<Permission>());
            set
            {
                if (_permissions != value)
                {
                    _permissions = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class Permission : INotifyPropertyChanged
    {
        private int _id;
        private string _name = string.Empty;
        private string _displayName = string.Empty;
        private string _description = string.Empty;
        private string _category = string.Empty;
        private bool _isGranted;

        public int Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged();
                }
            }
        }

        public string DisplayName
        {
            get => _displayName;
            set
            {
                if (_displayName != value)
                {
                    _displayName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Category
        {
            get => _category;
            set
            {
                if (_category != value)
                {
                    _category = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsGranted
        {
            get => _isGranted;
            set
            {
                if (_isGranted != value)
                {
                    _isGranted = value;
                    OnPropertyChanged();
                }
            }
        }

        // Clone method
        public Permission Clone()
        {
            return new Permission
            {
                Id = this.Id,
                Name = this.Name,
                DisplayName = this.DisplayName,
                Description = this.Description,
                Category = this.Category,
                IsGranted = this.IsGranted
            };
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}