using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Media;

namespace SalesManagementSystem.Converters
{
    public class SearchHighlightConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length != 2 || values[0] == null)
                return values[0];

            string text = values[0].ToString();
            string searchTerm = values[1]?.ToString()?.ToLower();

            if (string.IsNullOrEmpty(searchTerm))
                return text;

            var textBlock = new System.Windows.Controls.TextBlock();
            
            int index = 0;
            string lowerText = text.ToLower();
            
            while (index < text.Length)
            {
                int foundIndex = lowerText.IndexOf(searchTerm, index, StringComparison.OrdinalIgnoreCase);
                
                if (foundIndex == -1)
                {
                    // Add remaining text
                    textBlock.Inlines.Add(new Run(text.Substring(index)));
                    break;
                }
                
                // Add text before match
                if (foundIndex > index)
                {
                    textBlock.Inlines.Add(new Run(text.Substring(index, foundIndex - index)));
                }
                
                // Add highlighted match
                var highlightedRun = new Run(text.Substring(foundIndex, searchTerm.Length))
                {
                    Background = Brushes.Yellow,
                    Foreground = Brushes.Black,
                    FontWeight = System.Windows.FontWeights.Bold
                };
                textBlock.Inlines.Add(highlightedRun);
                
                index = foundIndex + searchTerm.Length;
            }
            
            return textBlock;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
