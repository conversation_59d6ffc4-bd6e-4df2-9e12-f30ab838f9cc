using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using SalesManagementSystem.Models;
using SalesManagementSystem.ViewModels;

namespace SalesManagementSystem.Views
{
    public partial class LoginWindow : Window
    {
        private readonly LoginViewModel _viewModel;

        public User? AuthenticatedUser { get; private set; }

        public LoginWindow()
        {
            InitializeComponent();

            _viewModel = new LoginViewModel();
            DataContext = _viewModel;

            // Subscribe to events
            _viewModel.LoginSuccessful += OnLoginSuccessful;

            // Focus on username textbox
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        #region Event Handlers

        private void OnLoginSuccessful(User user)
        {
            AuthenticatedUser = user;
            DialogResult = true;
            Close();
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox)
            {
                _viewModel.SetPassword(passwordBox.Password);
            }
        }

        private void Input_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && _viewModel.CanLogin)
            {
                _viewModel.LoginCommand.Execute();
            }
            else if (e.Key == Key.Tab)
            {
                // Handle tab navigation
                if (sender == UsernameTextBox)
                {
                    PasswordBox.Focus();
                    e.Handled = true;
                }
            }
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // Unsubscribe from events
            _viewModel.LoginSuccessful -= OnLoginSuccessful;

            base.OnClosed(e);
        }

        #endregion
    }
}
